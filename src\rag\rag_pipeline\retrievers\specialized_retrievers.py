from langchain_core.runnables.base import Runnable


class GenericDenseRetriever(Runnable):
    """
    一个通用的密集检索器。

    它直接使用向量存储的 `as_retriever` 方法进行标准相似性搜索。
    """
    def __init__(self, vector_db, k: int = 4):
        """
        初始化 GenericDenseRetriever。

        Args:
            vector_db: Chroma 向量数据库实例。
            k: 检索器返回的最大文档数量。
        """
        self.vector_db = vector_db
        self.k = k

    # def _get_relevant_documents(
    #     self, query: str, *, run_manager: CallbackManagerForRetrieverRun
    # ) -> List[Document]:
    #     """
    #     获取与查询相关的文档。

    #     Args:
    #         query: 用户查询字符串。
    #         run_manager: LangChain 回调管理器。

    #     Returns:
    #         相关文档列表。
    #     """
    #     base_retriever = self.vector_db.as_retriever(
    #         search_type="mmr",
    #         search_kwargs={
    #             "k": self.k,
    #             "fetch_k": 4 * self.k,
    #             "lambda_mult": 0.5
    #         }
    #     )
    #     return base_retriever.get_relevant_documents(query)
    
    def invoke(self, query, config=None, **kwargs):
        base_retriever = self.vector_db.as_retriever(
            search_type="mmr",
            search_kwargs={
                "k": self.k,
                "fetch_k": 4 * self.k,
                "lambda_mult": 0.5
            }
        )
        return base_retriever.invoke(query)