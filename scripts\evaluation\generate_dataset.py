from pathlib import Path
import logging
import json
import argparse
import requests
from concurrent.futures import ThreadPoolExecutor
from tqdm import tqdm

# Setup logging
logging.basicConfig(level=logging.INFO)

def ask_minecrag(question: str, request_url: str, key: str) -> tuple[str, list[str]]:
  headers = {'X-Dump-Key': key}
  data = {'user_content': question, 'dump_key': key}
  response = requests.post(request_url, headers=headers, params=data)
  if response.status_code != 200 or response.json().get('event') == 'error':
    logging.error(f'Request failed with status code {response.status_code}: {response.text}')
    return None, None
  response_data = response.json()
  return response_data['response'], response_data['retrieved_contexts']


def to_ragas_dataset(qra_dir: Path, output_path: Path, request_url: str, key: str) -> None:
  rows = []
  for file in qra_dir.glob('*.json'):
    with open(file, 'r', encoding='utf-8') as f:
      data = json.load(f)
    flat_qras = [{
      'type': qra_type,
      'user_input': qra['问题'],
      'reference': qra['答案'],
      'reference_contexts': qra['ref'],
      } for qra_type, qra_list in data.items() for qra in qra_list]
    rows.extend(flat_qras)
  def worker(row: dict) -> None:
    row['response'], row['retrieved_contexts'] = ask_minecrag(row['user_input'], request_url, key)
  with ThreadPoolExecutor() as executor:
    list(tqdm(executor.map(worker, rows), total=len(rows), desc='Asking MinecRAG'))
  with open(output_path, 'w', encoding='utf-8') as f:
    json.dump(rows, f, ensure_ascii=False, indent=4)


if __name__ == '__main__':
  parser = argparse.ArgumentParser(description='Generate benchmark compatible with Ragas')
  parser.add_argument('--qra_dir', type=str, required=True, help='The directory containing QRA JSON files')
  parser.add_argument('--output_dir', type=str, required=True, help='The directory to save the output JSON file')
  parser.add_argument('--request_url', type=str, required=True, help='The URL to send requests to')
  parser.add_argument('--key', type=str, required=True, help='The dump key for the request')

  args = parser.parse_args()

  qra_dir = Path(args.qra_dir)
  output_dir = Path(args.output_dir)

  to_ragas_dataset(qra_dir, output_dir / f'{qra_dir.resolve().name}.json', args.request_url, args.key)
