import json
from dotenv import load_dotenv
from langchain_chroma import Chroma
from langchain_community.chat_models import ChatZhipuAI
from langchain_huggingface import HuggingFaceEmbeddings
from ragas import RunConfig
from ragas.embeddings import LangchainEmbeddingsWrapper
from ragas.llms import LangchainLLMWrapper
from ragas.testset.synthesizers import default_query_distribution
from ragas.testset.graph import KnowledgeGraph, Node, NodeType
from ragas.testset.transforms import default_transforms, apply_transforms
from ragas.testset import TestsetGenerator
from chinese_synthesizers import default_chinese_query_distribution
from tqdm import tqdm

from build_db import load_documents

load_dotenv()
llm = ChatZhipuAI(model="GLM-4-Air-250414")

model_name = "BAAI/bge-large-zh-v1.5"
model_kwargs = {'device': 'cuda'}
encode_kwargs = {'normalize_embeddings': True}

embeddings = HuggingFaceEmbeddings(
    model_name=model_name,
    model_kwargs=model_kwargs,
    encode_kwargs=encode_kwargs
)

generator_llm = LangchainLLMWrapper(llm)
generator_embeddings = LangchainEmbeddingsWrapper(embeddings)

def build_knowledge_graph(docs):
    kg = KnowledgeGraph()
    for doc in docs:
        properties = {
            "page_content": doc.page_content,
            "document_metadata": doc.metadata
        }
        kg.nodes.append(
            Node(
                type=NodeType.DOCUMENT,
                properties=properties
            )
        )
    return kg

def generate_testset(run_config: RunConfig = RunConfig()):
    print("正在加载文档...")
    # docs = load_documents(15,15)
    # print(f"已加载 {len(docs)} 个文档")
    # print("构建知识图谱...")
    # kg = build_knowledge_graph(docs)
    # print(f"初始图谱: {kg}")
    # print("应用知识图谱转换...")
    # trans = default_transforms(
    #     documents=docs,
    #     llm=generator_llm,
    #     embedding_model=generator_embeddings
    # )
    # apply_transforms(
    #     kg,
    #     trans,
    #     run_config=run_config
    # )
    # print(f"增强后图谱: {kg}")
    # kg.save("minecraft_knowledge_graph.json")
    kg = KnowledgeGraph.load("minecraft_knowledge_graph.json")
    generator = TestsetGenerator(
        llm=generator_llm,
        embedding_model=generator_embeddings,
        knowledge_graph=kg
    )
    print("生成测试问题...")
    # query_distribution_dict = default_chinese_query_distribution(generator_llm)
    # query_distribution = [(synthesizer, prob) for _, (synthesizer, prob) in query_distribution_dict.items()]
    testset = generator.generate(
        testset_size=10,
        query_distribution=default_query_distribution(generator_llm),
        run_config=run_config
    )
    print(f"已生成并保存 {len(testset)} 个测试用例")
    df = testset.to_pandas()
    df.to_json("mod_qa_testset.json", orient="records", force_ascii=False)
    return testset

if __name__ == "__main__":
    run_config = RunConfig(max_workers=32)
    testset = generate_testset(run_config)
