"""
文档管理服务模块

此模块提供文档管理相关功能，包括向向量数据库添加新文档。
"""
from typing import Optional, List, Dict, Any
from langchain_core.documents import Document
from langchain_chroma import Chroma
from langchain_huggingface import HuggingFaceEmbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter

class DocumentService:
    def __init__(self, vector_db: Chroma, embedding: HuggingFaceEmbeddings):
        """
        初始化文档服务
        
        Args:
            vector_db: Chroma向量数据库实例
            embedding: 嵌入模型实例
        """
        self.vector_db = vector_db
        self.embedding = embedding
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=500,  
            chunk_overlap=50, 
            separators=["\n\n", "\n", "。", "；", ",", " "]
        )
    async def add_document(self, title: str, content: str, 
                        modename: str = None, url: str = None) -> List[str]:
        """
        向向量数据库添加新文档
        
        Args:
            title: 文档标题
            content: 文档内容
            modename: 模组名称，默认为None，将被转换为"未知模组"
            url: 源网址，默认为None，将被转换为"未知网址"
        Returns:
            List[str]: 添加的文档ID列表
        """
        # 处理空字符串或None情况
        if modename is None or modename == "":
            modename = "未知模组"
        if url is None or url == "":
            url = "未知网址"
            
        formatted_content = f"""
[源网站] {url}
[所属模组] {modename}
[摘要] {title}
[教程内容] {content}
"""
            
        metadata = {
            "类型": "教程",
            "模组名称": modename,
            "源网站": url,
            "摘要": title
        }
            
        docs = self.text_splitter.create_documents([formatted_content], [metadata])
        
        print(f"文档内容: {formatted_content}")
        print(f"文档元数据: {metadata}")

        print(self.vector_db._collection.count())
        ids = self.vector_db.add_documents(docs)
        print(self.vector_db._collection.count())
        
        # self.vector_db.persist()
        
        return ids
    
    async def delete_document(self, document_id: str) -> bool:
        """
        从向量数据库中删除文档
        
        Args:
            document_id: 文档ID
            
        Returns:
            bool: 删除成功返回True，否则返回False
        """
        try:
            self.vector_db.delete([document_id])
            self.vector_db.persist()
            return True
        except Exception as e:
            print(f"删除文档时出错: {e}")
            return False
