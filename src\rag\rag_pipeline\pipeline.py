"""
RAG Pipeline模块

此模块提供了RAG Pipeline的核心实现，封装了检索、过滤、上下文整合、Prompt组装、LLM调用等步骤。
"""

from typing import List, Tuple, Dict, Any, AsyncIterable, Optional, Union
from langchain_core.documents import Document
from langchain_openai import ChatOpenAI # For type hint of llm, title_llm
from langchain_core.language_models.chat_models import BaseChatModel # For metadata_llm type hint
from langchain_chroma import Chroma # For vector_db type hint
from langchain_core.retrievers import BaseRetriever
from langchain_community.document_transformers import EmbeddingsRedundantFilter
from langchain_core.prompts import ChatPromptTemplate
import json
import time
import asyncio
from concurrent.futures import ThreadPoolExecutor
import os

from .llm_chain import LLMChainManager
from .intent_processing import IntentClassifier
from .retrievers.factory import RetrieverFactory # 导入 RetrieverFactory
from ..prompts.unified_rag_prompt import format_unified_rag_prompt # V3 阶段三：导入统一 Prompt 格式化函数
from .. import config
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from .reranker import Reranker
    from .query_enhancer import QueryEnhancer
# 使用统一的日志配置
from ..logging_config import get_logger
logger = get_logger("rag_pipeline")

class RAGPipeline:
    """
    RAG Pipeline类，封装检索、过滤、上下文整合、Prompt组装、LLM调用等核心步骤
    """

    def __init__(
        self,
        vector_db: Chroma,
        metadata_llm: BaseChatModel,
        k: int,
        filter: EmbeddingsRedundantFilter,
        llm: ChatOpenAI, # 主 LLM
        prompt: ChatPromptTemplate,
        title_llm: Optional[ChatOpenAI] = None,
        title_prompt: Optional[ChatPromptTemplate] = None,
        intent_classifier: Optional[IntentClassifier] = None,
        reranker: Optional["Reranker"] = None,  # 新增: 重排序器
        query_enhancer: Optional["QueryEnhancer"] = None,  # 新增: 查询增强器
        recording: bool = False,
    ):
        """
        初始化 RAG Pipeline。

        参数:
            vector_db: Chroma 向量数据库实例。
            metadata_llm: 用于元数据匹配的语言模型。
            k: 检索器返回的最大文档数量。
            filter: EmbeddingsRedundantFilter 实例 (当前主要通过URL去重，此过滤器可选)。
            llm: 用于生成答案的主要 ChatOpenAI 实例。
            prompt: 用于主 RAG 链的 ChatPromptTemplate。
            title_llm: (可选) 用于生成对话标题的 ChatOpenAI 实例。
            title_prompt: (可选) 用于生成对话标题的 ChatPromptTemplate。
            intent_classifier: (可选) IntentClassifier 实例。
            reranker: (可选) 用于对检索结果进行重排序的 Reranker 实例。
            query_enhancer: (可选) 用于扩展用户查询的 QueryEnhancer 实例。
        """
        # 存储 RetrieverFactory 及其他组件可能需要的依赖项
        self.vector_db = vector_db
        self.metadata_llm = metadata_llm
        self.k = k

        # 在 Pipeline 内部实例化 RetrieverFactory
        self.retriever_factory = RetrieverFactory(
            vector_db=self.vector_db,
            metadata_llm=self.metadata_llm,
            k=self.k
        )
        self.filter = filter
        self.llm = llm
        self.prompt = prompt
        self.title_llm = title_llm
        self.title_prompt = title_prompt
        self.intent_classifier = intent_classifier
        self.reranker = reranker  # 新增: 存储重排序器
        self.query_enhancer = query_enhancer  # 新增: 存储查询增强器

        # 创建LLM链
        self.chain = LLMChainManager.create_rag_chain(llm, prompt)

        # 创建线程池用于CPU密集型操作
        # 根据CPU核心数确定线程池大小，但最少2个线程，最多8个线程
        max_workers = min(max(os.cpu_count() or 2, 2), 8)
        logger.info(f"初始化 RAG Pipeline 线程池，线程数: {max_workers}")
        self.executor = ThreadPoolExecutor(max_workers=max_workers)

        self.recording = recording

    async def retrieve_and_format_context(self, query: str, original_query: str = None, intents: List[str] = None) -> Tuple[str, List[Document], List[str]]:
        """
        执行并行检索、过滤并格式化上下文

        Args:
            query: 用于检索的查询（可能是扩展后的查询）
            original_query: 用户原始查询，用于重排序（如果为None，则使用query）
            intents: 预先识别的意图列表（如果为None，则使用空列表）

        Returns:
            Tuple[str, List[Document], List[str]]: 上下文字符串、过滤后的文档列表和意图列表
        """
        # 如果未提供原始查询，则使用当前查询
        if original_query is None:
            original_query = query

        # 如果未提供意图，则使用空列表
        if intents is None:
            intents = []

        try:
            total_start_time = time.time()
            loop = asyncio.get_event_loop()

            logger.info(f"开始检索和格式化上下文 | 查询: '{query}' | 意图: {intents}")

            # 2. 获取检索器
            retrieval_start_time = time.time()
            # 根据意图获取检索器
            retrievers_to_run: List[BaseRetriever] = self.retriever_factory.get_retrievers_by_intent(intents)

            if not retrievers_to_run:
                logger.warning(f"未找到针对意图 {intents} 的特定检索器，将使用回退检索器。")
                # 使用空列表或特定回退逻辑获取回退检索器
                retrievers_to_run = self.retriever_factory.get_retrievers_by_intent([]) # 假设空列表触发回退

            # 获取要运行的检索器名称列表
            retriever_names_to_run = [getattr(r, 'source_name', r.__class__.__name__) for r in retrievers_to_run]
            logger.info("准备为查询 '%s' 并行运行 %d 个检索器: %s", query, len(retrievers_to_run), retriever_names_to_run)

            # 3. 并行执行检索
            tasks = []
            task_start_times = {} # 用于记录每个任务的开始时间
            for retriever in retrievers_to_run:
                retriever_source_name = getattr(retriever, 'source_name', retriever.__class__.__name__)
                task_name = f"{retriever_source_name}_{retriever.__class__.__name__}"

                # 记录任务开始时间
                task_start_times[task_name] = time.time()

                # 检查是否有 ainvoke 方法
                if hasattr(retriever, 'ainvoke') and callable(retriever.ainvoke):
                    # logger.debug("开始异步检索: %s", retriever_source_name) # 移除启动日志
                    tasks.append(asyncio.create_task(retriever.ainvoke(query), name=f"ainvoke_{task_name}"))
                # 否则，假定有 invoke 方法，并在线程池中运行
                elif hasattr(retriever, 'invoke') and callable(retriever.invoke):
                    # logger.debug("开始同步检索(线程池): %s", retriever_source_name) # 移除启动日志
                    # 传递 retriever.invoke 而不是 retriever.invoke(query)
                    tasks.append(asyncio.create_task(loop.run_in_executor(self.executor, retriever.invoke, query), name=f"invoke_{task_name}"))
                else:
                    logger.warning(f"检索器 {retriever_source_name} ({retriever.__class__.__name__}) 没有可调用的 'ainvoke' 或 'invoke' 方法，已跳过。")

            # 使用 asyncio.gather 并行运行所有任务
            # return_exceptions=True 使得即使某个任务失败，也能获取其他任务的结果
            results: List[Union[List[Document], Exception]] = await asyncio.gather(*tasks, return_exceptions=True)
            retrieval_time = time.time() - retrieval_start_time

            # 4. 聚合结果
            combined_docs: List[Document] = []
            unique_docs_by_url: List[Document] = []
            successful_retrievals = 0
            failed_retrievals = 0
            for i, result in enumerate(results):
                retriever = retrievers_to_run[i]
                retriever_source_name = getattr(retriever, 'source_name', retriever.__class__.__name__)
                task_name = tasks[i].get_name() if hasattr(tasks[i], 'get_name') else f"Task_{i}" # 用于匹配开始时间
                task_end_time = time.time()
                task_duration = task_end_time - task_start_times.get(task_name.split('_', 1)[-1], task_end_time) # 获取对应任务的耗时

                if isinstance(result, Exception):
                    failed_retrievals += 1
                    logger.error(f"并行检索任务 '{task_name}' 失败: {result}", exc_info=result)
                    logger.debug("检索完成: %s | 状态: 失败 | 耗时: %.2fs", retriever_source_name, task_duration)
                elif isinstance(result, list):
                    successful_retrievals += 1
                    combined_docs.extend(result)
                    logger.debug("检索完成: %s | 状态: 成功 (%d 个文档) | 耗时: %.2fs", retriever_source_name, len(result), task_duration)
                else:
                    # 对于未知类型，也计入失败，并记录警告
                    failed_retrievals += 1
                    logger.warning(f"并行检索任务 '{task_name}' 返回了意外类型: {type(result)}")
                    logger.debug("检索完成: %s | 状态: 未知结果类型 | 耗时: %.2fs", retriever_source_name, task_duration)

            logger.info("并行检索完成 | 总耗时: %.2fs | 成功: %d | 失败: %d | 合并后文档数: %d", retrieval_time, successful_retrievals, failed_retrievals, len(combined_docs))            # 5. 简单而高效的去重 (基于内容完全一致的去重)
            dedup_start_time = time.time()

            if combined_docs:
                logger.info("开始基于内容完全一致的去重，处理 %d 个文档...", len(combined_docs))
                seen_contents = set()
                unique_docs = []
                num_duplicates = 0
                for doc in combined_docs:
                    content_key = doc.page_content.strip()
                    
                    if content_key not in seen_contents:
                        seen_contents.add(content_key)
                        unique_docs.append(doc)
                    else:
                        num_duplicates += 1
                        logger.debug("发现重复内容，跳过文档: %s...", content_key[:50])

                dedup_time = time.time() - dedup_start_time
                logger.info("内容去重完成 | 耗时: %.2fs | 去重前: %d -> 去重后: %d | 重复内容: %d",
                            dedup_time, len(combined_docs), len(unique_docs), num_duplicates)
                unique_docs_by_url = unique_docs  

            # 5. 过滤与去重 (在线程池中执行)
            filtered_docs: List[Document] = []
            # --- 嵌入冗余过滤 (可选，当前已禁用) ---
            # 当前主要依赖前置的 URL 去重逻辑。如果需要更强的语义去重能力，
            # 可以考虑取消注释下面的代码块，并重新启用 EmbeddingsRedundantFilter。
            # 注意：若启用，可能需要仔细调整 EmbeddingsRedundantFilter 的 similarity_threshold 参数。
            # logger.info("开始进行嵌入冗余过滤，处理 %d 个文档...", len(unique_docs_by_url))
            # filter_start_time = time.time()
            # try:
            #     # # 定义同步过滤函数 (以便在线程池中运行)
            #     # def perform_filtering(docs_to_filter: List[Document]) -> List[Document]:
            #     #     return self.filter.transform_documents(docs_to_filter)
            #     #
            #     # # 在线程池中异步执行过滤操作
            #     # filtered_docs = await loop.run_in_executor(
            #     #     self.executor, perform_filtering, unique_docs_by_url
            #     # )
            #     filter_time = time.time() - filter_start_time
            #     logger.info("嵌入冗余过滤完成 | 耗时: %.2fs | 过滤前文档数: %d -> 过滤后文档数: %d", filter_time, len(unique_docs_by_url), len(filtered_docs))
            # except Exception as filter_error:
            #     logger.error("嵌入冗余过滤过程中发生错误: %s", filter_error, exc_info=True)
            #     # 在过滤失败的情况下，使用仅经过 URL 去重的文档列表作为回退机制
            #     logger.warning("由于过滤时发生错误，已回退至使用仅基于 URL 去重后的文档列表。")
            #     filtered_docs = unique_docs_by_url

            # 5.5 重排序 (Rerank)
            reranked_docs: List[Document] = []
            if unique_docs_by_url and self.reranker:
                rerank_start_time = time.time()
                logger.info(f"开始对 {len(unique_docs_by_url)} 个文档进行重排序...")
                try:
                    # 使用原始查询进行重排序，而不是可能扩展后的查询
                    reranked_docs = await self.reranker.rerank_documents(original_query, unique_docs_by_url)
                    rerank_time = time.time() - rerank_start_time
                    logger.info(f"重排序完成 | 耗时: {rerank_time:.2f}秒 | 重排序前: {len(unique_docs_by_url)} -> 重排序后: {len(reranked_docs)}")
                except Exception as rerank_error:
                    logger.error(f"重排序过程中发生错误: {rerank_error}", exc_info=True)
                    # 在重排序失败的情况下，使用 URL 去重后的文档列表作为回退
                    logger.warning("由于重排序错误，回退至使用 URL 去重后的文档")
                    reranked_docs = unique_docs_by_url[:config.RERANK_TOP_K]  # 使用配置的 TOP_K 值
            elif unique_docs_by_url:
                # 如果没有配置重排序器，但有文档，则使用 URL 去重后的前 K 个文档
                logger.info(f"未配置重排序器，使用 URL 去重后的前 {config.RERANK_TOP_K} 个文档")
                reranked_docs = unique_docs_by_url[:config.RERANK_TOP_K]
            else:
                # 如果没有文档，则保持空列表
                logger.info("无文档可供重排序")

            # 使用重排序后的文档作为基础
            filtered_docs = reranked_docs

            # 5.6 意图驱动的文档评分和排序
            scored_docs = self._score_documents_by_intent(filtered_docs, intents)
            # 按评分降序排序
            scored_docs.sort(key=lambda x: x[1], reverse=True)
            # 提取排序后的文档
            filtered_docs = [doc for doc, _ in scored_docs]
            logger.info(f"意图驱动的文档评分和排序完成 | 文档数: {len(filtered_docs)}")

            # 6. 格式化上下文
            context_parts = []
            processed_sources = set() # 跟踪已添加标题的来源组合

            for doc in filtered_docs:
                source_info = ""
                # 从元数据获取意图和原始来源信息
                intent_source = doc.metadata.get("retrieved_by_intent", "未知意图")
                original_source = doc.metadata.get("source", "未知来源")
                source_key = (intent_source, original_source)

                # 为新的来源组合添加标题
                if source_key not in processed_sources:
                    source_info = f"--- 来自意图 '{intent_source}' 的信息 (来源: {original_source}) ---\n"
                    processed_sources.add(source_key)

                # 添加文档元数据和内容
                metadata_str = json.dumps(doc.metadata, ensure_ascii=False, indent=2)
                context_parts.append(f"{source_info}[文档元数据]:\n{metadata_str}\n\n[文档内容]:\n{doc.page_content}")

            context_str = "\n\n".join(context_parts)

            # 记录总体耗时
            total_time = time.time() - total_start_time
            logger.info(f"RAG 检索与格式化总耗时: {total_time:.2f}秒")

            return context_str, filtered_docs, intents

        except Exception as e:
            logger.error(f"获取 RAG 上下文时发生意外错误: {e}", exc_info=True)
            return "", [], [] # 返回空值，确保流程继续

    async def stream_llm_response(
        self,
        chat_history_str: str,
        # context_str: str, # V3 阶段三：移除 context_str，信息已在 final_prompt_string 中
        final_prompt_string: str # V3 阶段三：接收统一后的 Prompt
    ) -> AsyncIterable[str]:
        """
        流式处理LLM调用并生成响应

        Args:
            chat_history_str: 对话历史字符串
            final_prompt_string: 经过 `format_unified_rag_prompt` 处理后的最终Prompt字符串

        Yields:
            str: 响应文本块
        """
        try:
            # 获取 LLM 模型名称 (尝试多种常见属性以提高兼容性)
            model_name = getattr(self.llm, "model_name", None) or \
                         getattr(self.llm, "_model", None) or \
                         getattr(self.llm, "model_name_or_path", "未知LLM模型")

            # 准备输入
            chain_input = {
                # "context": context_str, # V3 阶段三：上下文已包含在 final_prompt_string 中，此处的 key 取决于 self.prompt 的期望
                "chat_history": chat_history_str,
                "question": final_prompt_string # V3 阶段三：使用 final_prompt_string 作为 LLM 的主要输入
                                              # 假设 self.prompt (ChatPromptTemplate) 的输入变量包含 "question"
            }

            # 记录首次响应时间
            first_token_received = False
            llm_start_time = time.time()
            token_count = 0

            # 使用astream方法获取流式响应
            async for chunk in self.chain.astream(chain_input):
                # 提取内容
                content = chunk.content if hasattr(chunk, "content") else str(chunk)

                # 记录首个 token 的时间
                if not first_token_received and content:
                    first_token_received = True
                    first_token_time = time.time() - llm_start_time
                    logger.info(f"LLM 首次响应耗时: {first_token_time:.2f}秒")

                # 估算 token 数量
                if content:
                    token_count += len(content) / 4 # 粗略估计

                yield content

            # 记录总体LLM调用时间
            llm_total_time = time.time() - llm_start_time

            # 计算并记录 LLM 性能
            if token_count > 0 and llm_total_time > 0:
                tokens_per_second = token_count / llm_total_time
                logger.info(f"LLM 性能 | 模型: {model_name} | 总耗时: {llm_total_time:.2f}秒 | 约 {token_count:.0f} tokens | 速度: {tokens_per_second:.2f} t/s")

        except Exception as e:
            logger.error(f"流式处理查询时出错: {e}", exc_info=True)
            yield f"抱歉，处理您的问题时出现错误: {str(e)}"

    async def _process_core(
        self,
        original_query: str, # V3 阶段三：参数名明确为 original_query
        chat_history_str: str = "",
        dump: bool = False,
    ) -> AsyncIterable[Dict[str, Any]]:
        """
        核心查询处理逻辑，被process_query和process_new_conversation共享

        Args:
            original_query: 用户原始查询
            chat_history_str: 对话历史字符串

        Yields:
            Dict[str, Any]: 事件字典，包含事件类型和数据
        """
        try:
            # 记录开始时间
            total_start_time = time.time()
            logger.info(f"RAGPipeline._process_core: 开始处理查询: '{original_query}'")

            if dump:
                record = {"user_input": original_query}

            # 0. 真正并行执行查询扩展和意图分类（使用线程池）
            query_for_retrieval = original_query
            intents = []

            # 创建包装函数，在独立线程中运行异步任务
            async def run_async_in_thread(async_func, *args):
                """在独立线程中运行异步函数"""
                # 创建一个可在线程中运行的同步函数
                def run_in_new_thread():
                    # 创建新的事件循环
                    new_loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(new_loop)
                    try:
                        # 在新循环中运行异步函数
                        return new_loop.run_until_complete(async_func(*args))
                    finally:
                        new_loop.close()

                # 在线程池中运行同步函数
                loop = asyncio.get_running_loop()
                return await loop.run_in_executor(self.executor, run_in_new_thread)

            # 准备并行任务
            parallel_tasks = []
            task_names = []

            # 添加查询扩展任务
            if self.query_enhancer:
                logger.info("RAGPipeline._process_core: 开始真正并行查询扩展（线程池）")
                query_expansion_task = run_async_in_thread(
                    self.query_enhancer.expand_query, original_query
                )
                parallel_tasks.append(query_expansion_task)
                task_names.append("查询扩展")
            else:
                logger.info("RAGPipeline._process_core: 未配置查询增强器，使用原始查询")
                # 添加一个返回原始查询的占位任务
                parallel_tasks.append(asyncio.sleep(0, result=original_query))
                task_names.append("查询扩展（占位）")

            # 添加意图分类任务
            if self.intent_classifier:
                logger.info("RAGPipeline._process_core: 开始真正并行意图分类（线程池）")
                intent_classification_task = run_async_in_thread(
                    self.intent_classifier.classify_intent, original_query
                )
                parallel_tasks.append(intent_classification_task)
                task_names.append("意图分类")
            else:
                logger.info("RAGPipeline._process_core: 未配置意图分类器，使用空意图列表")
                # 添加一个返回空列表的占位任务
                parallel_tasks.append(asyncio.sleep(0, result=[]))
                task_names.append("意图分类（占位）")

            # 并行执行任务
            parallel_start_time = time.time()
            results = await asyncio.gather(*parallel_tasks, return_exceptions=True)
            parallel_time = time.time() - parallel_start_time
            logger.info(f"RAGPipeline._process_core: 真正并行任务完成，耗时: {parallel_time:.2f}秒")

            # 处理任务结果
            for i, (result, task_name) in enumerate(zip(results, task_names)):
                if isinstance(result, Exception):
                    logger.error(f"{task_name}失败: {result}", exc_info=result)
                else:
                    logger.info(f"{task_name}成功完成")

            # 处理查询扩展结果
            if not isinstance(results[0], Exception):
                query_for_retrieval = results[0]
                if query_for_retrieval != original_query:
                    logger.info(f"RAGPipeline._process_core: 查询已扩展为: '{query_for_retrieval}'")
                else:
                    logger.info("RAGPipeline._process_core: 查询扩展未改变原始查询")
            else:
                logger.warning("由于查询扩展失败，使用原始查询")

            # 处理意图分类结果
            if len(parallel_tasks) > 1 and not isinstance(results[1], Exception):
                intents = results[1]
                logger.info(f"RAGPipeline._process_core: 意图分类结果: {intents}")
            else:
                logger.warning("由于意图分类失败或未配置，使用空意图列表")

            # 1. 获取上下文 (使用并行任务的结果)
            # V3.1: 使用扩展后的查询进行检索，但传递原始查询用于重排序，并传递已识别的意图
            aggregated_context_string, filtered_docs, _ = await self.retrieve_and_format_context(
                query=query_for_retrieval,
                original_query=original_query,
                intents=intents
            )
            logger.info(f"RAGPipeline._process_core: 接收到的原始查询: '{original_query}', 识别出的意图: {intents}")
            if dump:
                record["retrieved_contexts"] = [doc.page_content for doc in filtered_docs]

            # 2. Chit-chat 快速路径 (V3 阶段三，步骤 4)
            if intents == ["chit_chat"]:
                logger.info("RAGPipeline._process_core: 检测到 'chit_chat' 意图，使用快速路径。")
                chit_chat_response = "你好！有什么我可以帮助你的吗？" # 预设的友好中文闲聊回应
                yield {"event": "message", "data": chit_chat_response}
                yield {"event": "end", "data": ""}
                # 记录LLM（此处为预设）响应
                logger.info(f"RAGPipeline._process_core: Chit-chat 路径响应长度: {len(chit_chat_response)}")
                total_time = time.time() - total_start_time
                logger.info(f"RAGPipeline._process_core: Chit-chat 路径处理总耗时: {total_time:.2f}秒")
                return

            # 3. 主 RAG 路径 (V3 阶段三，步骤 2)
            logger.info(f"RAGPipeline._process_core: 进入主 RAG 路径。")
            logger.info(f"RAGPipeline._process_core: retrieve_and_format_context 返回的 aggregated_context_string 长度: {len(aggregated_context_string)}")

            # 构建统一 Prompt
            final_prompt_string = format_unified_rag_prompt(
                original_query=original_query,
                intents=intents,
                aggregated_context=aggregated_context_string
            )
            #出于安全和隐私考虑，开发阶段记录完整Prompt，生产环境可以考虑截断或哈希
            # 写入 final_prompt_string.txt
            with open("final_prompt_string.txt", "w", encoding="utf-8") as f:
                f.write(final_prompt_string)
            logger.info(f"RAGPipeline._process_core: 生成的 final_prompt_string 长度: {len(final_prompt_string)}")


            # 流式生成响应 - 将 final_prompt_string 传递给 LLM
            # stream_llm_response 的签名和内部逻辑也需要相应调整
            full_response_content = []
            async for chunk in self.stream_llm_response(chat_history_str, final_prompt_string): # 注意参数变化
                if chunk:
                    full_response_content.append(chunk)
                    # 发送消息块事件
                    yield {"event": "message", "data": chunk}

            final_answer = "".join(full_response_content)
            logger.info(f"RAGPipeline._process_core: LLM 返回的最终答案长度: {len(final_answer)}")
            if len(final_answer) > 100:
                logger.info(f"RAGPipeline._process_core: LLM 返回的最终答案摘要 (前100字符): {final_answer[:100]}...")
            else:
                logger.info(f"RAGPipeline._process_core: LLM 返回的最终答案: {final_answer}")

            if dump:
                record["response"] = final_answer
                yield {"event": "dump", "data": json.dumps(record, ensure_ascii=False)}

            # 流结束
            yield {"event": "end", "data": ""}

            # 记录总体耗时
            total_time = time.time() - total_start_time
            logger.info(f"RAGPipeline._process_core: 主 RAG 路径处理总耗时: {total_time:.2f}秒")

            # 注意: 异步生成器通过 yield 传递数据，不能使用 return 返回值

        except Exception as e:
            logger.error(f"处理查询 '_process_core' 时出错: {e}", exc_info=True)
            # 发送错误事件
            yield {"event": "error", "data": json.dumps({"message": f"处理回复时发生错误: {str(e)}"})}
            # 异步生成器不能使用return返回值

    async def process_query(
        self,
        query: str,
        chat_history_str: str = "",
        dump: bool = False,
    ) -> AsyncIterable[Dict[str, Any]]:
        """
        处理查询并生成事件流

        Args:
            query: 用户查询
            chat_history_str: 对话历史字符串

        Yields:
            Dict[str, Any]: 事件字典，包含事件类型和数据
        """
        # 调用核心处理逻辑
        async for event in self._process_core(original_query=query, chat_history_str=chat_history_str, dump=dump): # V3 阶段三：确保参数名一致
            yield event

    async def generate_title(self, query: str) -> str:
        """
        生成对话标题

        Args:
            query: 用户查询

        Returns:
            str: 生成的标题
        """
        # 记录开始时间
        start_time = time.time()

        if self.title_llm is None or self.title_prompt is None:
            # 如果没有提供标题生成组件，使用查询的前15个字符作为标题
            if len(query) > 15:
                title = query[:12] + "..."
            else:
                title = query
            return title

        try:
            # 获取标题生成 LLM 模型名称 (尝试多种常见属性以提高兼容性)
            model_name = getattr(self.title_llm, "model_name", None) or \
                         getattr(self.title_llm, "_model", None) or \
                         getattr(self.title_llm, "model_name_or_path", "未知标题LLM模型")

            # 创建处理链
            from langchain_core.output_parsers import StrOutputParser
            title_chain = (
                self.title_prompt
                | self.title_llm
                | StrOutputParser()
            )

            # 定义同步函数以便在线程池中执行
            def generate_title_sync():
                raw_title = title_chain.invoke({"query": query})
                # 清理标题中的引号
                return raw_title.strip().replace("\"", "").replace("'", "")

            # 在线程池中异步执行标题生成
            loop = asyncio.get_event_loop()
            title = await loop.run_in_executor(self.executor, generate_title_sync)
            # 限制标题长度
            if len(title) > 30:
                title = title[:27] + "..."

            # 记录总体耗时
            title_time = time.time() - start_time
            logger.info(f"标题生成 | 模型: {model_name} | 耗时: {title_time:.2f}秒 | 标题: '{title}'")

            return title
        except Exception as e:
            logger.error(f"生成标题时出错: {e}", exc_info=True) # 添加 exc_info
            # 出错时使用截断的查询作为回退标题
            title = query[:12] + "..." if len(query) > 15 else query
            return title

    async def process_new_conversation(
        self,
        query: str
    ) -> Tuple[str, AsyncIterable[Dict[str, Any]]]:
        """
        处理新对话

        Args:
            query: 用户查询

        Returns:
            Tuple[str, AsyncIterable[Dict[str, Any]]]: 初始标题和事件生成器
        """
        # 记录开始时间
        start_time = time.time()

        # 使用查询前缀作为初始标题
        initial_title = query[:15] + "..." if len(query) > 15 else query

        # 定义事件生成器
        async def event_generator() -> AsyncIterable[Dict[str, Any]]:
            # 调用核心处理逻辑
            async for event in self._process_core(original_query=query, chat_history_str=""):  # V3 阶段三：确保参数名一致，新对话历史为空
                yield event

                # 在流结束后生成并发送最终标题
                if event["event"] == "end":
                    generated_title = await self.generate_title(query) # 此处会记录标题生成日志
                    yield {"event": "title_update", "data": json.dumps({"title": generated_title})}

                    # 记录总体耗时
                    total_time = time.time() - start_time
                    logger.info(f"新对话处理总耗时: {total_time:.2f}秒")

        return initial_title, event_generator()

    def _score_documents_by_intent(self, documents: List[Document], intents: List[str]) -> List[Tuple[Document, float]]:
        """
        根据意图对文档进行评分

        Args:
            documents: 待评分的文档列表
            intents: 意图列表

        Returns:
            List[Tuple[Document, float]]: 文档和评分的元组列表
        """
        if not documents:
            return []

        if not intents:
            # 如果没有意图，保持原始顺序和评分
            return [(doc, 1.0) for doc in documents]

        scored_docs = []

        for doc in documents:
            # 基础分数，保留原始重排序的相对顺序
            base_score = 1.0
            # 从元数据中获取重排序分数（如果有）
            if "rerank_score" in doc.metadata:
                base_score = doc.metadata["rerank_score"]

            # 意图相关加分
            intent_score = 0.0

            # 原版Minecraft查询的评分调整
            if "find_vanilla_wiki" in intents and "find_mod_info" not in intents:
                # 检查是否包含模组相关关键词
                mod_keywords = ["模组", "mod", "forge", "fabric", "mekanism", "tinkers"]
                if any(mod_keyword in doc.page_content.lower() for mod_keyword in mod_keywords):
                    # 包含模组关键词，降低评分但不完全排除
                    intent_score -= 0.5
                    logger.debug(f"文档包含模组关键词，评分-0.5")

            # 教程查询的评分调整
            if "find_tutorial" in intents:
                tutorial_keywords = ["步骤", "教程", "指南", "如何", "方法", "首先", "然后", "接着", "最后"]
                # 计算包含的教程关键词数量
                tutorial_keyword_count = sum(1 for keyword in tutorial_keywords if keyword in doc.page_content)
                if tutorial_keyword_count > 0:
                    # 根据关键词数量加分，最多加0.5分
                    tutorial_score = min(0.1 * tutorial_keyword_count, 0.5)
                    intent_score += tutorial_score
                    logger.debug(f"文档包含{tutorial_keyword_count}个教程关键词，评分+{tutorial_score:.1f}")

            # 合成配方查询的评分调整
            if "find_crafting_recipe" in intents:
                recipe_keywords = ["合成", "配方", "材料", "制作", "工作台", "熔炉"]
                # 计算包含的合成关键词数量
                recipe_keyword_count = sum(1 for keyword in recipe_keywords if keyword in doc.page_content)
                if recipe_keyword_count > 0:
                    # 根据关键词数量加分，最多加0.5分
                    recipe_score = min(0.1 * recipe_keyword_count, 0.5)
                    intent_score += recipe_score
                    logger.debug(f"文档包含{recipe_keyword_count}个合成关键词，评分+{recipe_score:.1f}")

            # 物品查询的评分调整
            if "find_item" in intents:
                item_keywords = ["物品", "方块", "工具", "武器", "装备", "属性", "效果", "用途"]
                # 计算包含的物品关键词数量
                item_keyword_count = sum(1 for keyword in item_keywords if keyword in doc.page_content)
                if item_keyword_count > 0:
                    # 根据关键词数量加分，最多加0.4分
                    item_score = min(0.1 * item_keyword_count, 0.4)
                    intent_score += item_score
                    logger.debug(f"文档包含{item_keyword_count}个物品关键词，评分+{item_score:.1f}")

            # 模组信息查询的评分调整
            if "find_mod_info" in intents:
                mod_info_keywords = ["模组", "mod", "版本", "更新", "作者", "功能", "特性"]
                # 计算包含的模组信息关键词数量
                mod_info_keyword_count = sum(1 for keyword in mod_info_keywords if keyword in doc.page_content.lower())
                if mod_info_keyword_count > 0:
                    # 根据关键词数量加分，最多加0.4分
                    mod_info_score = min(0.1 * mod_info_keyword_count, 0.4)
                    intent_score += mod_info_score
                    logger.debug(f"文档包含{mod_info_keyword_count}个模组信息关键词，评分+{mod_info_score:.1f}")

            # 综合回答的评分调整
            if "comprehensive_answer" in intents:
                # 对于综合回答，更长的文档可能包含更多信息
                length_score = min(len(doc.page_content) / 2000, 0.3)  # 最多加0.3分
                intent_score += length_score
                logger.debug(f"文档长度评分+{length_score:.1f}")

            # 计算最终评分
            final_score = base_score + intent_score
            scored_docs.append((doc, final_score))

            # 记录评分详情（仅在调试级别）
            logger.debug(f"文档评分 | 基础: {base_score:.2f} | 意图调整: {intent_score:+.2f} | 最终: {final_score:.2f}")

        return scored_docs

    def __del__(self):
        """
        析构函数，确保线程池被正确关闭
        """
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=False)
            logger.info("RAG Pipeline 线程池已关闭。")
