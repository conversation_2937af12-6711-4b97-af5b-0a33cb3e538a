import logging
import os
import random
import re
import time
import requests
from bs4 import BeautifulSoup
import json
from urllib.parse import urljoin # Keep in case relative URLs need resolving later
from pathlib import Path
import traceback
from concurrent.futures import ThreadPoolExecutor, as_completed

# --- Configuration ---
NUM_WORKERS = 50
MAX_POST_ID = 40000
POST_OUTPUT_FOLDER = './post' # Changed output folder name
LOG_FILE = 'crawl_posts_log.txt'
REQUEST_TIMEOUT = 30 # Kept original timeout for posts
MAX_RETRIES = 3     # Kept original retries for posts

# --- Proxy Configuration (Keep as is from script 1) ---
# IMPORTANT: Replace with your actual Kuaidaili API details if using it
PROXY_API_URL = "https://dps.kdlapi.com/api/getdps/?secret_id=o908aykc7vhhaftk9iz6&signature=5818amln1aur09moioq1sie91299nzlv&num={num}&pt=1&sep=1"
PROXY_USER = "d3758262232"
PROXY_PASS = "e6upmnib"

# --- Target Site Configuration ---
# !!! IMPORTANT: You MUST change this URL pattern !!!
POST_URL_TEMPLATE = "https://www.mcmod.cn/post/{post_id}.html" # Replace with actual target URL pattern

USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) RAG-Data-Collector/1.0' # Custom agent
]
# --- Global Variables ---
proxies = []

# --- Logging Setup ---
logging.basicConfig(filename=LOG_FILE, level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S')

def my_log(msg):
    """Prints message to console and logs it to file."""
    print(msg)
    logging.info(msg)

# --- Utility Functions ---
def clean_text(text):
    """Cleans text by removing excess whitespace and non-breaking spaces."""
    if text:
        text = text.replace('\xa0', ' ') # Replace non-breaking space
        text = re.sub(r'\s+', ' ', text) # Replace multiple whitespace with single space
        return text.strip()
    return ""

def extract_post_id(post_url):
    """Extract the post ID from the URL."""
    # Extract numeric ID from URL like https://www.mcmod.cn/post/123.html
    match = re.search(r'/post/(\d+)\.html', post_url)
    if match:
        return match.group(1)
    return None

def clean_html_content(html_content):
    """Clean the HTML content and extract title and text."""
    soup = BeautifulSoup(html_content, 'html.parser')

    # Try to find title
    title_elem = soup.find('h1')
    title = clean_text(title_elem.get_text()) if title_elem else ""

    # Remove unwanted elements
    for unwanted in soup.select('script, style, header, footer, nav, .sidebar, .ads'):
        unwanted.decompose()

    # Extract cleaned text
    content = clean_text(soup.get_text(separator=' ', strip=True))

    return title, content

# --- Proxy Management (Using functions from the first script, as they are generally compatible) ---
def proxy_init():
    """Initializes the list of proxies for workers."""
    global proxies
    # Replace placeholder API URL and credentials if necessary
    if "YOUR_SECRET_ID" in PROXY_API_URL or "YOUR_PROXY_USER" in PROXY_USER:
        my_log("错误：代理 API URL 或凭据未配置。将不使用代理。")
        proxies = [None] * NUM_WORKERS
        return True # Allow running without proxies, but log warning

    api_url = PROXY_API_URL.format(num=NUM_WORKERS)
    my_log(f"初始化代理 (数量: {NUM_WORKERS}) from {api_url}...")
    try:
        response = requests.get(api_url, timeout=30)
        response.raise_for_status()
        proxy_ips_text = response.text.strip()

        if not proxy_ips_text:
            raise ValueError("获取代理失败，API 返回空内容")

        proxy_list = [ip.strip() for ip in proxy_ips_text.split('\n') if ip.strip()]

        if len(proxy_list) < NUM_WORKERS:
            my_log(f"警告: 获取代理数量不足 {NUM_WORKERS} 个，实际获取 {len(proxy_list)} 个。")
            if not proxy_list:
                 my_log("警告: 未获取到任何有效 IP，所有 Worker 将不使用代理。")
                 proxies = [None] * NUM_WORKERS
                 return True # Proceed without proxies

        proxies_temp = [
            {
                "http":  f"http://{PROXY_USER}:{PROXY_PASS}@{ip}/",
                "https": f"http://{PROXY_USER}:{PROXY_PASS}@{ip}/"
            } for ip in proxy_list
        ]
        # Fill remaining slots with None if fewer proxies were obtained
        proxies = proxies_temp + [None] * (NUM_WORKERS - len(proxies_temp))

        my_log(f"成功初始化 {len(proxy_list)} 个代理。")
        return True
    except requests.exceptions.RequestException as e:
        my_log(f"代理初始化请求失败: {e}")
    except ValueError as e:
        my_log(f"代理初始化值错误: {e}")
    except Exception as e:
        my_log(f"代理初始化时发生未知错误: {e}")
        traceback.print_exc()

    my_log("代理初始化失败，所有 Worker 将不使用代理。")
    proxies = [None] * NUM_WORKERS
    return True # Allow execution even if proxy init fails

def refresh_proxy(worker_id):
    """Refreshes the proxy for a specific worker. (Using logic from first script)"""
    global proxies
    if worker_id < 0 or worker_id >= len(proxies):
        my_log(f"错误: 尝试刷新无效的 Worker ID {worker_id} 的代理。")
        return False

    # Replace placeholder API URL and credentials if necessary
    if "YOUR_SECRET_ID" in PROXY_API_URL or "YOUR_PROXY_USER" in PROXY_USER:
         my_log(f"Worker {worker_id}: 代理未配置，无法刷新。")
         proxies[worker_id] = None # Ensure it's None if not configured
         return False

    api_url = PROXY_API_URL.format(num=1)
    my_log(f"Worker {worker_id}: 正在刷新代理 from {api_url}...")
    try:
        response = requests.get(api_url, timeout=30)
        response.raise_for_status()
        proxy_ip = response.text.strip()

        if proxy_ip:
            new_proxy = {
                "http":  f"http://{PROXY_USER}:{PROXY_PASS}@{proxy_ip}/",
                "https": f"http://{PROXY_USER}:{PROXY_PASS}@{proxy_ip}/"
            }
            proxies[worker_id] = new_proxy
            # Avoid logging full IP:Port for security/privacy
            proxy_ip_display = proxy_ip.split(':')[0] + ":****" if ':' in proxy_ip else proxy_ip
            my_log(f"Worker {worker_id}: 代理已更新为 {proxy_ip_display}")
            return True
        else:
            my_log(f"Worker {worker_id}: 获取新代理失败，返回空IP。将移除代理。")
            proxies[worker_id] = None # Remove potentially bad proxy
            return False
    except requests.exceptions.RequestException as e:
        my_log(f"Worker {worker_id}: 刷新代理请求失败: {e}")
    except Exception as e:
        my_log(f"Worker {worker_id}: 刷新代理时出错: {e}")
        traceback.print_exc()

    my_log(f"Worker {worker_id}: 代理刷新失败，将移除代理.")
    proxies[worker_id] = None # Remove potentially bad proxy
    return False


def process_post(post_id, session, proxy, worker_id):
    """
    Fetches, parses, and saves a single post. (Function body remains unchanged)
    Returns:
        True: Success
        "not_found": Post resulted in 404
        "blocked": Access was blocked (e.g., 403)
        "timeout": Request timed out
        "error": Other request or processing error
    """
    # Construct the post URL
    url = POST_URL_TEMPLATE.format(post_id=post_id)
    output_path = Path(POST_OUTPUT_FOLDER) / f"post{post_id}.json"

    # Check if file already exists (moved to worker_task, but kept here conceptually)
    # if output_path.exists():
    #     my_log(f"Worker {worker_id}: Post {post_id} already exists, skipping")
    #     return True # Handled in worker_task now

    proxy_ip_display = "N/A"
    if proxy and proxy.get("http"):
        try:
            # Basic obfuscation for logging
            proxy_ip_display = proxy["http"].split('@')[-1].split(':')[0] + ":****"
        except Exception:
            proxy_ip_display = "InvalidFormat"
    elif proxy:
        proxy_ip_display = "Misconfigured"

    my_log(f"Worker {worker_id}: Fetching Post {post_id}: {url} using proxy {proxy_ip_display}")
    headers = {'User-Agent': random.choice(USER_AGENTS)}

    try:
        response = session.get(url, headers=headers, timeout=REQUEST_TIMEOUT, proxies=proxy)

        if response.status_code == 403:
            my_log(f"Worker {worker_id}: [403 Blocked] Post {post_id}: {url}")
            return "blocked"
        if response.status_code == 404:
            my_log(f"Worker {worker_id}: [404 Not Found] Post {post_id}: {url}")
            return "not_found"

        response.raise_for_status() # Checks for other HTTP errors (4xx, 5xx)
        response.encoding = response.apparent_encoding if response.apparent_encoding else 'utf-8'

        soup = BeautifulSoup(response.text, 'html.parser')
        if not soup:
            my_log(f"Worker {worker_id}: [错误] 无法解析 HTML for Post {post_id}: {url}")
            return "error"

        # Find content using the class from the original code
        content_div = soup.find('div', {'class': 'post-row'})

        content = "" # Initialize content
        title = f"标题未找到 (Post {post_id})" # Default title

        if content_div:
            # Use the clean_html_content function to extract title and cleaned content
            # Note: clean_html_content extracts from the whole soup passed to it
            # We pass the specific div's content here to limit scope
            _ , cleaned_content = clean_html_content(content_div.prettify())
            content = cleaned_content
            # Try finding title within the context if possible, or fallback to page title
            title_elem_in_div = content_div.find(['h1', 'h2']) # Look for headers inside the div
            if title_elem_in_div:
                 title = clean_text(title_elem_in_div.get_text())
            else:
                # Try getting title from the main soup if not in div
                page_title_elem = soup.find('h1')
                if page_title_elem:
                    title = clean_text(page_title_elem.get_text())

        else:
            my_log(f"Worker {worker_id}: Post {post_id} - 特定内容 div ('post-row') 未找到. 尝试从 body 提取.")
            # Fallback method: Clean entire body
            body_tag = soup.body
            if body_tag:
                 page_title_elem = soup.find('h1') # Still try to get a proper title
                 if page_title_elem:
                      title = clean_text(page_title_elem.get_text())

                 for unwanted in body_tag.select('script, style, header, footer, nav, .sidebar, .ads, .post-row-subject, .post-row-content-meta'): # Add more specific selectors to remove
                       unwanted.decompose()
                 content = clean_text(body_tag.get_text(separator=' ', strip=True))
                 if len(content) < 50: # Arbitrary threshold for meaningful content
                       my_log(f"Worker {worker_id}: [警告] Post {post_id} - 从 body 提取的文本过短 (<50 chars)，可能无效。")
                       content = "" # Discard very short fallback content
            else:
                 content = "" # No body tag found


        if not content:
            my_log(f"Worker {worker_id}: [警告] Post {post_id} - 未能提取到有效内容 from {url}")
            # Return "error" if no content could be extracted after trying main div and fallback
            return "error"

        # --- Data Preparation & Saving ---
        data = {
            "id": str(post_id),
            "url": url,
            "content": content
        }

        output_path.parent.mkdir(parents=True, exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)

        my_log(f"Worker {worker_id}: Post {post_id} saved successfully to {output_path}")
        return True

    except requests.exceptions.Timeout:
        my_log(f"Worker {worker_id}: [Timeout] Post {post_id}: {url}")
        return "timeout"
    except requests.exceptions.RequestException as e:
        # More specific error logging
        status_code = e.response.status_code if e.response is not None else "N/A"
        my_log(f"Worker {worker_id}: [Request Error] Post {post_id} ({url}): Status={status_code}, Error={e}")
        # Check if status code indicates specific known issues
        if status_code == 403: return "blocked"
        # Treat common server-side issues or overload as potentially temporary -> timeout category for retry/refresh
        if status_code in [500, 502, 503, 504]: return "timeout"
        return "error" # Other request errors
    except Exception as e:
        my_log(f"Worker {worker_id}: [Processing Error] Post {post_id} ({url}): {e}")
        traceback.print_exc()
        return "error"

# --- Worker Thread Function (MODIFIED with logic from Script 2) ---
def worker_task(worker_id):
    """The task executed by each worker thread, using Script 2's proxy handling."""
    my_log(f"Worker {worker_id}: 开始执行...")
    processed_count = 0
    success_count = 0
    fail_count = 0
    skipped_count = 0
    not_found_count = 0

    # Distribute post IDs among workers
    current_post_id = worker_id + 1 # Start from 1

    session = requests.Session()
    # Use adapter settings from script 1
    adapter = requests.adapters.HTTPAdapter(pool_connections=5, pool_maxsize=10)
    session.mount('http://', adapter)
    session.mount('https://', adapter)

    while current_post_id <= MAX_POST_ID:
        processed_count += 1
        output_path = Path(POST_OUTPUT_FOLDER) / f"post{current_post_id}.json"

        # Check if file already exists and is not empty (to avoid reprocessing failed attempts marked by empty/error files)
        if output_path.exists() and output_path.stat().st_size > 0:
            # my_log(f"Worker {worker_id}: Post {current_post_id} exists and not empty, skipping.") # Reduce log noise
            skipped_count += 1
            current_post_id += NUM_WORKERS # Move to the next assigned ID
            continue

        success = False
        for attempt in range(MAX_RETRIES):
            # Get the current proxy assigned to this worker for this attempt
            proxy = proxies[worker_id] if worker_id < len(proxies) else None

            # Added check from Script 2: If no proxy, don't bother trying
            if not proxy and attempt == 0:
                my_log(f"Worker {worker_id}: [警告] Post {current_post_id} - 第 {attempt+1} 次尝试 - 无代理分配给此 Worker。跳过此 ID 的尝试。")
                # This post ID cannot be processed by this worker without a proxy.
                # Break the inner retry loop; it will be counted as a failure below.
                break

            # Optional: Add delay between retries (using logic from script 1)
            if attempt > 0:
                time.sleep(random.uniform(1.0, 2.0) * attempt) # Slightly adjusted delay

            # Process the post
            result = process_post(current_post_id, session, proxy, worker_id)

            # --- Apply Proxy Error Handling Logic from Script 2 ---
            if result is True:
                success = True
                success_count += 1
                break # Success, move to next post ID
            elif result == "not_found":
                not_found_count += 1
                success = True # Treat as "processed", not a failure needing retry
                break
            elif result == "blocked":
                my_log(f"Worker {worker_id}: Post {current_post_id} 尝试 {attempt + 1}/{MAX_RETRIES} - 被阻止，刷新代理...")
                refresh_proxy(worker_id) # Immediately refresh proxy
            elif result == "timeout":
                my_log(f"Worker {worker_id}: Post {current_post_id} 尝试 {attempt + 1}/{MAX_RETRIES} - 超时，刷新代理...")
                refresh_proxy(worker_id) # Immediately refresh proxy
            elif result == "error":
                my_log(f"Worker {worker_id}: Post {current_post_id} 尝试 {attempt + 1}/{MAX_RETRIES} - 发生错误，刷新代理...")
                refresh_proxy(worker_id) # Immediately refresh proxy on generic errors too
            else: # Should not happen if process_post returns defined values
                my_log(f"Worker {worker_id}: Post {current_post_id} 尝试 {attempt + 1}/{MAX_RETRIES} - 未知结果: {result}，刷新代理...")
                refresh_proxy(worker_id) # Refresh just in case

            # Note: The loop continues to the next attempt after refresh_proxy is called.
            # The refreshed proxy (or None if refresh failed) will be used in the *next* iteration.

        # After the retry loop finishes for this post_id
        if not success:
            fail_count += 1
            my_log(f"Worker {worker_id}: [失败] Post {current_post_id} 处理最终失败 (尝试 {MAX_RETRIES} 次).")
            # Optional: Create an empty file or a file with error info to mark as failed & prevent re-skip
            try:
                output_path.parent.mkdir(parents=True, exist_ok=True)
                # Write minimal info to mark failure but keep file size > 0 if needed for skip logic
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump({"id": str(current_post_id), "url": POST_URL_TEMPLATE.format(post_id=current_post_id), "status": "failed after retries"}, f)
            except Exception as e:
                my_log(f"Worker {worker_id}: [错误] 无法写入 Post {current_post_id} 的失败标记文件: {e}")


        # Move to the next post ID assigned to this worker
        current_post_id += NUM_WORKERS

        # Log progress periodically
        if processed_count % 50 == 0: # Log every 50 attempts per worker
            my_log(f"Worker {worker_id}: 进度 - 尝试处理: {processed_count}, 成功: {success_count}, 跳过: {skipped_count}, 未找到: {not_found_count}, 失败: {fail_count}")

    session.close()
    my_log(f"Worker {worker_id}: 完成. 总尝试: {processed_count}, 成功: {success_count}, 跳过: {skipped_count}, 未找到: {not_found_count}, 失败: {fail_count}")
    return {"processed": processed_count, "success": success_count, "skipped": skipped_count, "not_found": not_found_count, "failed": fail_count}

# --- Main Execution (Unchanged from Script 1) ---
if __name__ == "__main__":
    start_time_main = time.perf_counter()
    my_log("脚本开始执行 - 帖子爬虫...")

    # Create output directory
    os.makedirs(POST_OUTPUT_FOLDER, exist_ok=True)
    my_log(f"输出目录: {POST_OUTPUT_FOLDER}")
    my_log(f"目标范围: Posts 1 - {MAX_POST_ID}")
    my_log(f"工作线程数: {NUM_WORKERS}")
    # !!! Remind user about URL and selectors !!!
    my_log("-" * 30)
    my_log("重要提示:")
    my_log(f"  - 请确保 `POST_URL_TEMPLATE` ('{POST_URL_TEMPLATE}') 正确!")
    my_log("  - 请检查并调整 `process_post` 函数中的 BeautifulSoup 选择器 (如 'div.post-row') 以匹配目标网站的 HTML 结构。")
    my_log("  - 如果使用代理，请确保 `PROXY_API_URL`, `PROXY_USER`, `PROXY_PASS` 已正确设置。")
    my_log("-" * 30)


    # Initialize proxies (allow continuation even if it fails)
    proxy_init()
    # Check if any proxies were actually obtained if API wasn't skipped
    if "YOUR_SECRET_ID" not in PROXY_API_URL and all(p is None for p in proxies):
       my_log("警告: 代理初始化完成，但未能获取任何有效代理。Worker 将不使用代理。")
    elif any(p is not None for p in proxies):
        valid_proxy_count = sum(1 for p in proxies if p is not None)
        my_log(f"代理初始化完成，获取了 {valid_proxy_count} / {NUM_WORKERS} 个代理。")
    else:
        my_log("代理初始化完成，没有配置或未能获取代理。") # Catch all case

    total_processed = 0
    total_success = 0
    total_skipped = 0
    total_not_found = 0
    total_failed = 0

    my_log(f"启动 {NUM_WORKERS} 个 Worker...")
    futures = []
    # Use a dictionary to map futures back to worker_id for better error reporting if needed
    future_to_wid = {}
    with ThreadPoolExecutor(max_workers=NUM_WORKERS) as executor:
        for wid in range(NUM_WORKERS):
             # Assign the task to the worker
             future = executor.submit(worker_task, wid)
             futures.append(future)
             future_to_wid[future] = wid # Map future to its worker ID

        # Process results as they complete
        for future in as_completed(futures):
             worker_id = future_to_wid[future] # Get worker ID from the map
             try:
                 result = future.result() # Get result or raise exception if worker failed internally
                 total_processed += result["processed"]
                 total_success += result["success"]
                 total_skipped += result["skipped"]
                 total_not_found += result["not_found"]
                 total_failed += result["failed"]
                 # Log partial completion less frequently or just when finished
                 # my_log(f"Worker {worker_id} 完成. (部分统计: 成功={result['success']}, 跳过={result['skipped']}, 未找到={result['not_found']}, 失败={result['failed']})")
             except Exception as e:
                 # This catches errors *within* the worker_task function itself IF NOT handled there
                 my_log(f"Worker {worker_id} 执行时发生严重未捕获异常: {e}")
                 traceback.print_exc()
                 # Decide how to count this. Adding to total_failed seems reasonable.
                 # Estimate items this worker might have failed on (crude estimate)
                 # approx_items_per_worker = (MAX_POST_ID // NUM_WORKERS) + 1
                 # total_failed += approx_items_per_worker # Or just increment by 1 to signify worker failure
                 total_failed +=1 # Count the worker crash as 1 major failure event

    elapsed_main = time.perf_counter() - start_time_main
    my_log("-" * 30)
    my_log(f"所有 Worker 执行完毕.")
    my_log(f"总耗时: {elapsed_main:.2f} 秒 ({elapsed_main/60:.2f} 分钟)")
    my_log(f"目标范围: Posts 1 - {MAX_POST_ID}")

    my_log(f"最终统计:")
    my_log(f"  成功抓取并保存: {total_success}")
    my_log(f"  已存在跳过: {total_skipped}")
    my_log(f"  未找到 (404): {total_not_found}")
    my_log(f"  最终处理失败: {total_failed}") # Includes retries failed + worker crashes
    total_files_expected_or_skipped_or_failed = total_success + total_skipped + total_not_found + total_failed
    # This count might not exactly equal MAX_POST_ID if workers crashed mid-way
    my_log(f"  (总文件/状态记录尝试: {total_files_expected_or_skipped_or_failed})")
    my_log("-" * 30)
    my_log(f"日志文件位于: {LOG_FILE}")
    my_log(f"输出文件位于: {POST_OUTPUT_FOLDER}")