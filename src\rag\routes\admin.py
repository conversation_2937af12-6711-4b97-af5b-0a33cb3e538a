"""
管理员 API 路由模块

此模块定义了与管理员相关的 API 端点，
包括知识库管理、验证码生成等功能。
"""
from fastapi import APIRouter, Depends, HTTPException, status, Request # 从 fastapi 的导入中移除了 UploadFile
from starlette.datastructures import UploadFile # 直接从 starlette.datastructures 导入 UploadFile
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import json
import logging

from ..database import User, KnowledgeBaseType
from ..dependencies import get_knowledge_service, get_auth_service, is_admin
from ..services.knowledge_service import KnowledgeService
from ..services.auth_service import AuthService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/admin", tags=["admin"])

# 请求和响应模型 (保持不变)
class KnowledgeBaseRequest(BaseModel):
    type: KnowledgeBaseType
    start_index: int
    page_size: int

class KnowledgeBaseItem(BaseModel):
    title: str
    content: str

class KnowledgeBaseResponse(BaseModel):
    content: List[Dict[str, Any]]
    page_count: int

class UploadKnowledgeBaseResponse(BaseModel):
    success: bool
    message: str

class GenerateCodeResponse(BaseModel):
    code: str
    success: bool
    message: str

class VerificationCodesResponse(BaseModel):
    codes: List[Dict[str, Any]]
    success: bool

# 分页获取某个知识库的内容 (保持不变)
@router.post("/getKnowledgeBase", response_model=KnowledgeBaseResponse)
async def get_knowledge_base(
        request: KnowledgeBaseRequest,
        current_admin: User = Depends(is_admin),
        knowledge_service: KnowledgeService = Depends(get_knowledge_service)
):
    result = await knowledge_service.get_knowledge_base_content(
        kb_type=request.type,
        start_index=request.start_index,
        page_size=request.page_size
    )
    return result

# 上传知识库内容 (主要修改在这里的 UploadFile 类型检查)
@router.post("/uploadKnowledgeBase", response_model=UploadKnowledgeBaseResponse)
async def upload_knowledge_base(
        request: Request,
        current_admin: User = Depends(is_admin),
        knowledge_service: KnowledgeService = Depends(get_knowledge_service)
):
    form_data = await request.form()
    logger.info(f"UploadKnowledgeBase: 接收到的表单字段名: {list(form_data.keys())}")

    metadata_parts: Dict[int, str] = {}
    file_parts: Dict[int, UploadFile] = {}

    for key, value in form_data.items():
        logger.debug(f"UploadKnowledgeBase: 正在处理表单部分: key='{key}', type(value)='{type(value)}'")
        if key.startswith("metadata[") and key.endswith("]"):
            try:
                idx_str = key[len("metadata["):-1]
                idx = int(idx_str)
                if not isinstance(value, str):
                    logger.error(f"UploadKnowledgeBase: 元数据部分 {key} 的值不是字符串，类型为 {type(value)}.")
                    raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"元数据部分 {key} 不是字符串。")
                metadata_parts[idx] = value
                logger.debug(f"UploadKnowledgeBase: 识别到元数据部分: key='{key}', value (前100字符)='{value[:100]}...'")
            except ValueError:
                logger.error(f"UploadKnowledgeBase: 元数据部分 {key} 的索引格式无效。")
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"无效的元数据索引格式: {key}")
        elif key.startswith("file[") and key.endswith("]"):
            try:
                idx_str = key[len("file["):-1]
                idx = int(idx_str)
                # 使用从 starlette.datastructures 导入的 UploadFile进行类型检查
                if not isinstance(value, UploadFile): # 这里的 UploadFile 现在是 starlette.datastructures.UploadFile
                    logger.error(f"UploadKnowledgeBase: 文件部分 {key} 的值不是 UploadFile 对象，类型为 {type(value)}。")
                    raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"文件部分 {key} 不是一个上传的文件。接收到的类型: {type(value)}")
                file_parts[idx] = value
                logger.debug(f"UploadKnowledgeBase: 识别到文件部分: key='{key}', filename='{value.filename}'")
            except ValueError:
                logger.error(f"UploadKnowledgeBase: 文件部分 {key} 的索引格式无效。")
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"无效的文件索引格式: {key}")

    logger.info(f"UploadKnowledgeBase: 解析后的元数据索引: {list(metadata_parts.keys())}")
    logger.info(f"UploadKnowledgeBase: 解析后的文件索引: {list(file_parts.keys())}")

    if not metadata_parts and not file_parts:
        logger.error("UploadKnowledgeBase: 表单中既没有元数据部分也没有文件部分。")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="未提交任何数据。")

    if not file_parts:
        logger.error("UploadKnowledgeBase: 表单中未找到任何有效的文件部分。")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="未提供文件。")

    sorted_indices = sorted(file_parts.keys())

    final_files_to_service: List[UploadFile] = []
    parsed_metadata_for_service: List[Dict[str, Any]] = []

    for idx in sorted_indices:
        current_file = file_parts[idx]
        final_files_to_service.append(current_file)

        metadata_json_array_str = metadata_parts.get(idx)
        file_meta_dict: Dict[str, Any] = {}

        if metadata_json_array_str:
            try:
                logger.debug(f"UploadKnowledgeBase: 正在解析索引 {idx} 的元数据字符串: {metadata_json_array_str[:200]}...")
                meta_string_array: List[str] = json.loads(metadata_json_array_str)

                for meta_item_str in meta_string_array:
                    logger.debug(f"UploadKnowledgeBase: 正在解析元数据项: '{meta_item_str}'")
                    parts = meta_item_str.split(":", 1)
                    if len(parts) == 2:
                        key_from_item = parts[0].strip()
                        value_str_from_item = parts[1].strip()

                        if key_from_item == "kb_metadata":
                            try:
                                file_meta_dict[key_from_item] = json.loads(value_str_from_item)
                                logger.debug(f"UploadKnowledgeBase: 解析 kb_metadata 成功: {file_meta_dict[key_from_item]}")
                            except json.JSONDecodeError as e_json_kb:
                                logger.error(f"UploadKnowledgeBase: 索引 {idx} 的 kb_metadata 值 '{value_str_from_item}' 不是有效的JSON: {e_json_kb}")
                                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"索引 {idx} 的 kb_metadata 值不是有效的JSON: {value_str_from_item}")
                        else:
                            file_meta_dict[key_from_item] = value_str_from_item
                    else:
                        logger.error(f"UploadKnowledgeBase: 索引 {idx} 的元数据项格式错误: '{meta_item_str}'. 期望格式 'key:value'。")
                        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"索引 {idx} 的元数据项格式错误: '{meta_item_str}'. 期望格式 'key:value'。")
            except json.JSONDecodeError as e_json_array:
                logger.error(f"UploadKnowledgeBase: 索引 {idx} 的元数据字符串 '{metadata_json_array_str[:200]}...' 不是有效的JSON数组格式: {e_json_array}")
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"索引 {idx} 的元数据字符串不是有效的JSON数组格式: {metadata_json_array_str}")

        if "title" not in file_meta_dict:
            file_meta_dict["title"] = current_file.filename if current_file.filename else f"Uploaded_File_{idx}"
            logger.debug(f"UploadKnowledgeBase: 索引 {idx} 元数据中未提供标题，使用默认值: '{file_meta_dict['title']}'")

        if "type" not in file_meta_dict:
            file_meta_dict["type"] = "WIKI" # 默认类型
            logger.debug(f"UploadKnowledgeBase: 索引 {idx} 元数据中未提供类型，使用默认值: 'WIKI'")

        parsed_metadata_for_service.append(file_meta_dict)
        logger.info(f"UploadKnowledgeBase: 索引 {idx} 的元数据成功解析为: {file_meta_dict}")

    if not final_files_to_service:
        logger.error("UploadKnowledgeBase: 解析后没有文件可处理 (final_files_to_service 为空)。")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="解析后没有文件可处理。")

    logger.info(f"UploadKnowledgeBase: 准备调用服务层，文件数量: {len(final_files_to_service)}, 元数据数量: {len(parsed_metadata_for_service)}")

    result = await knowledge_service.upload_knowledge_base(
        files=final_files_to_service,
        metadata_list=parsed_metadata_for_service,
        admin_user=current_admin
    )
    return result

# 生成管理员验证码 (保持不变)
@router.post("/generateVerificationCode", response_model=GenerateCodeResponse)
async def generate_verification_code(
        current_admin: User = Depends(is_admin),
        auth_service: AuthService = Depends(get_auth_service)
):
    code = await auth_service.generate_admin_verification_code()
    return {
        "code": code,
        "success": True,
        "message": "验证码生成成功"
    }

# 获取所有验证码 (保持不变)
@router.get("/verificationCodes", response_model=VerificationCodesResponse)
async def get_verification_codes(
        current_admin: User = Depends(is_admin),
        auth_service: AuthService = Depends(get_auth_service)
):
    codes = await auth_service.get_verification_codes()
    return {
        "codes": codes,
        "success": True
    }