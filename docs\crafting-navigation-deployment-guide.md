# 合成导航功能部署升级指南

## 📋 概述

本文档详细说明如何从普通RAG系统升级到支持合成导航功能的完整步骤。合成导航功能为Minecraft相关查询提供了智能物品识别和合成树生成能力。

## 🎯 升级目标

- 支持"普通对话"和"合成导航"两种对话模式
- 智能识别用户消息中的Minecraft物品实体
- 为合成导航对话维护会话级合成树
- 根据物品识别结果动态返回合成卡片或普通文本

## ⚠️ 升级前准备

### 系统要求
- Python 3.8+
- MySQL 5.7+ 或 8.0+
- 现有RAG系统正常运行
- 足够的磁盘空间用于数据库扩展

### 备份检查清单
- [ ] 备份MySQL数据库
- [ ] 备份当前.env配置文件
- [ ] 备份应用程序代码
- [ ] 记录当前系统版本和配置

```bash
# 数据库备份示例
mysqldump -u root -p rag_db > backup_$(date +%Y%m%d_%H%M%S).sql

# 配置文件备份
cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
```

## 🚀 升级步骤

### 第一步：更新代码库

```bash
# 1. 停止当前服务
sudo systemctl stop rag-service  # 或使用其他方式停止服务

# 2. 拉取最新代码（如果使用Git）
git pull origin main

# 3. 安装/更新依赖
pip install -r requirements.txt
```

### 第二步：数据库迁移

#### 2.1 执行Messages表扩展迁移

```bash
# 执行002迁移：添加消息类型和物品名称字段
python scripts/migrations/002_add_message_type_and_item_name.py

# 预期输出：
# 开始执行数据库迁移：添加消息类型和物品名称字段...
# 添加type字段到messages表...
# 为type字段添加索引...
# 添加item_name字段到messages表...
# 为item_name字段添加索引...
# 数据库迁移完成！
# ✓ type字段已成功添加
# ✓ item_name字段已成功添加
# ✓ type字段索引已成功创建
# ✓ item_name字段索引已成功创建
# 所有迁移步骤已成功完成！
```

#### 2.2 执行CraftingContexts表创建迁移

```bash
# 执行003迁移：创建合成上下文表
python scripts/migrations/003_add_crafting_contexts_table.py

# 预期输出：
# 开始执行数据库迁移：创建合成上下文表...
# 创建crafting_contexts表...
# 为conversation_id字段添加索引...
# 为target_item字段添加索引...
# 为created_at字段添加索引...
# 为updated_at字段添加索引...
# 数据库迁移完成！
# ✓ crafting_contexts表已成功创建
# ✓ 所有字段结构正确
# ✓ 所有索引已成功创建
# ✓ 外键约束已成功创建
# 所有迁移步骤已成功完成！
```

#### 2.3 验证数据库结构

```sql
-- 验证Messages表新字段
DESCRIBE messages;
-- 应该看到type和item_name字段

-- 验证CraftingContexts表
DESCRIBE crafting_contexts;
-- 应该看到完整的表结构

-- 检查索引
SHOW INDEX FROM messages WHERE Key_name LIKE '%type%' OR Key_name LIKE '%item_name%';
SHOW INDEX FROM crafting_contexts;
```

### 第三步：环境变量配置更新

#### 3.1 更新.env文件

```bash
# 基于新的.env.example更新配置
cp .env.example .env.new
# 手动合并配置或使用以下模板
```

#### 3.2 添加合成导航配置项

在.env文件中添加以下配置：

```bash
# ========================================
# 合成导航功能配置 (Crafting Navigation)
# ========================================

# 物品实体识别LLM配置
ITEM_EXTRACTION_MODEL=THUDM/GLM-4-9B-0414
ITEM_EXTRACTION_BASE_URL=https://api.siliconflow.cn/v1
ITEM_EXTRACTION_API_KEY=your-siliconflow-api-key
ITEM_EXTRACTION_TEMPERATURE=0.1

# Neo4j图数据库配置（当前阶段保持false）
NEO4J_ENABLED=false
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=your-neo4j-password

```

#### 3.3 配置验证

```bash
# 验证配置加载
python -c "
from src.rag.config import *
print(f'物品识别模型: {ITEM_EXTRACTION_MODEL}')
print(f'Neo4j启用状态: {NEO4J_ENABLED}')
print('配置加载成功！')
"
```

### 第四步：服务重启和验证

#### 4.1 启动服务

```bash
# 开发环境启动
python -m uvicorn src.rag.main:app --reload --host 0.0.0.0 --port 8000

# 生产环境启动（使用systemd）
sudo systemctl start rag-service
sudo systemctl enable rag-service
```

#### 4.2 健康检查

```bash
# 检查服务状态
curl -s http://localhost:8000/docs | grep -q "FastAPI" && echo "✓ 服务正常" || echo "✗ 服务异常"

# 检查API端点
curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/api/conversations
# 应该返回401（未授权，但端点可访问）
```


## 🔧 常见问题排查

### 问题1：数据库迁移失败

**症状**：迁移脚本报错或字段未创建

**解决方案**：
```bash
# 检查数据库连接
python -c "
from src.rag.config import DATABASE_URL
from sqlalchemy import create_engine
engine = create_engine(DATABASE_URL)
with engine.connect() as conn:
    print('数据库连接成功')
"

# 手动检查表结构
mysql -u root -p rag_db -e "DESCRIBE messages;"

# 如果需要回滚迁移
python scripts/migrations/002_add_message_type_and_item_name.py --rollback
python scripts/migrations/003_add_crafting_contexts_table.py --rollback
```

### 问题2：配置加载失败

**症状**：启动时报配置相关错误

**解决方案**：
```bash
# 检查.env文件格式
cat .env | grep -E "^[A-Z_]+=.*$" | wc -l
# 应该返回配置项数量

# 检查特定配置
python -c "
import os
from dotenv import load_dotenv
load_dotenv()
print('ITEM_EXTRACTION_MODEL:', os.getenv('ITEM_EXTRACTION_MODEL'))
"
```

### 问题3：组件初始化失败

**症状**：合成导航组件无法初始化

**解决方案**：
```bash
# 检查LLM客户端
python -c "
from src.rag.llm_clients import create_item_extraction_llm
llm = create_item_extraction_llm()
print('LLM客户端创建成功')
"

# 检查合成导航组件
python -c "
from src.rag.crafting import ItemEntityExtractor, CraftingTreeGenerator
generator = CraftingTreeGenerator(neo4j_enabled=False)
print('合成导航组件初始化成功')
"
```

### 问题4：API端点异常

**症状**：新的API功能不工作

**解决方案**：
```bash
# 检查路由注册
python -c "
from src.rag.main import app
routes = [route.path for route in app.routes]
print('注册的路由:', routes)
"

# 测试API端点
python test_api_endpoints.py
```

## 📊 升级验证清单

升级完成后，请确认以下项目：

### 数据库验证
- [ ] Messages表包含type和item_name字段
- [ ] CraftingContexts表已创建且结构正确
- [ ] 所有索引和外键约束已建立
- [ ] 现有数据完整性未受影响

### 配置验证
- [ ] 所有新配置项已添加到.env文件
- [ ] 配置值符合环境要求
- [ ] 配置加载无错误

### 功能验证
- [ ] 服务正常启动
- [ ] API端点可访问
- [ ] 合成导航组件初始化成功
- [ ] 测试脚本全部通过

### 兼容性验证
- [ ] 现有普通对话功能正常
- [ ] 用户认证系统正常
- [ ] 其他API端点正常

## 🔄 回滚方案

如果升级过程中遇到问题，可以按以下步骤回滚：

```bash
# 1. 停止服务
sudo systemctl stop rag-service

# 2. 回滚数据库迁移
python scripts/migrations/003_add_crafting_contexts_table.py --rollback
python scripts/migrations/002_add_message_type_and_item_name.py --rollback

# 3. 恢复配置文件
cp .env.backup.YYYYMMDD_HHMMSS .env

# 4. 恢复代码（如果需要）
git checkout previous-version-tag

# 5. 重启服务
sudo systemctl start rag-service
```

## 📞 技术支持

如果在升级过程中遇到问题，请：

1. 检查日志文件：`tail -f logs/application.log`
2. 运行诊断脚本：`python test_crafting_phase4_simple.py`
3. 查看详细错误信息并参考本文档的排查指南
4. 如需回滚，请严格按照回滚方案执行

---

**升级完成后，您的RAG系统将支持智能的合成导航功能，为Minecraft相关查询提供更好的用户体验！**
