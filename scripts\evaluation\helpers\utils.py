"""
This implementation is adapted from the RAGEval repository. Original repository: https://github.com/OpenBMB/RAGEval
"""

import json
import logging
import sys
from pathlib import Path


def read_prompt(file_path: str) -> list[dict]:
  """Reads prompts from a JSONL file."""
  prompts = []
  try:
    with open(file_path, 'r', encoding='utf-8') as f:
      for line in f:
        prompts.append(json.loads(line))
  except FileNotFoundError:
    logging.error(f"File not found: {file_path}")
    sys.exit(1)
  return prompts


def read_config_json(json_path: Path) -> dict:
  """Reads a JSON configuration file."""
  try:
    with open(json_path, 'r', encoding='utf-8') as f:
      config = json.load(f)
  except json.decoder.JSONDecodeError as e:
    logging.error(f'JSON Decode Error: {e} in {json_path}')
    sys.exit(1)
  except FileNotFoundError:
    logging.error(f"File not found: {json_path}")
    sys.exit(1)
  return config


def write_config_json(json_path: Path, config: dict) -> None:
  """Writes configuration data to a JSON file."""
  with open(json_path, 'w', encoding='utf-8') as f:
    json.dump(config, f, ensure_ascii=False, indent=4)
