"""
LLM链管理器模块

此模块提供了用于构建和调用LLM链的工具。
"""

from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from typing import Dict, Any, AsyncIterable

class LLMChainManager:
    """
    LLM链管理器，用于构建和调用LLM链
    """
    
    @staticmethod
    def create_rag_chain(llm: ChatOpenAI, prompt: ChatPromptTemplate):
        """
        创建RAG链
        
        Args:
            llm: LLM实例
            prompt: 提示模板
            
        Returns:
            链对象
        """
        return prompt | llm
    
    # 移除了 stream_response 方法，因为 RAGPipeline.stream_llm_response 内部已实现流式调用
    # 移除了 format_sources 方法，因为 RAGPipeline.retrieve_and_format_context 内部已实现上下文格式化
