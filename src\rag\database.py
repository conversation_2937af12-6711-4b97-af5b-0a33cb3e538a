"""
数据库模型和会话管理模块

此模块定义了项目使用的 SQLAlchemy 数据库模型，
以及用于创建数据库表和管理数据库会话的函数。
"""
from sqlalchemy import create_engine, Column, Integer, String, DateTime, ForeignKey, Text, Boolean, Enum, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.sql import func
from . import config
import enum

# 数据库配置

# 创建数据库引擎
engine = create_engine(config.DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建Base类
Base = declarative_base()

# 用户角色枚举
class UserRole(str, enum.Enum):
    """用户角色枚举，定义普通用户和管理员角色"""
    USER = "USER"
    ADMIN = "ADMIN"

# 反馈类型枚举
class FeedbackType(str, enum.Enum):
    BUG = "bug"
    FEATURE = "feature"
    CONTENT = "content"
    OTHER = "other"

# 消息类型枚举
class MessageType(str, enum.Enum):
    """消息类型枚举，定义普通文本和合成导航消息"""
    TEXT = "TEXT"
    CRAFTING = "CRAFTING"

# 反馈状态枚举
class FeedbackStatus(str, enum.Enum):
    PENDING = "pending"
    REVIEWED = "reviewed"
    IMPLEMENTED = "implemented"
    REJECTED = "rejected"

# 知识库类型枚举
class KnowledgeBaseType(str, enum.Enum):
    """知识库类型枚举，定义不同类型的知识库"""
    WIKI = "WIKI"
    POST = "POST"
    BBS = "BBS"
    MODE = "MODE"
    ITEM = "ITEM"
    NEW = "NEW"

# 对话类型枚举
class ConversationType(str, enum.Enum):
    """对话类型枚举，定义不同类型的对话"""
    NORMAL = "NORMAL"  # 普通对话
    SYNTHETIC_NAVIGATION = "SYNTHETIC_NAVIGATION"  # 合成导航

# 用户模型
class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    hashed_password = Column(String(100))
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    is_active = Column(Boolean, default=True, index=True)
    role = Column(Enum(UserRole), default=UserRole.USER, index=True)  # 新增用户角色字段

    # 关系
    queries = relationship("Query", back_populates="user")
    conversations = relationship("Conversation", back_populates="user")
    feedbacks = relationship("Feedback", back_populates="user")

# 查询历史模型
class Query(Base):
    __tablename__ = "queries"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    text = Column(Text)
    answer = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)

    # 关系
    user = relationship("User", back_populates="queries")

# 对话模型
class Conversation(Base):
    __tablename__ = "conversations"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    title = Column(String(255))
    type = Column(Enum(ConversationType), default=ConversationType.NORMAL, nullable=False, index=True)  # 对话类型
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), index=True)

    # 关系
    user = relationship("User", back_populates="conversations")
    messages = relationship("Message", back_populates="conversation", cascade="all, delete-orphan")

# 消息模型
class Message(Base):
    __tablename__ = "messages"

    id = Column(Integer, primary_key=True, index=True)
    conversation_id = Column(Integer, ForeignKey("conversations.id"), index=True)
    role = Column(String(20))  # 'user' 或 'assistant'
    type = Column(Enum(MessageType), default=MessageType.TEXT, nullable=False, index=True)  # 消息类型
    content = Column(Text)
    item_name = Column(String(255), nullable=True, index=True)  # 仅crafting类型消息使用
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)

    # 关系
    conversation = relationship("Conversation", back_populates="messages")

# 反馈模型
class Feedback(Base):
    __tablename__ = "feedbacks"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    type = Column(Enum(FeedbackType), nullable=False)
    content = Column(Text, nullable=False)
    status = Column(Enum(FeedbackStatus), default=FeedbackStatus.PENDING, index=True)
    admin_response = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), index=True)

    # 关系
    user = relationship("User", back_populates="feedbacks")

# 知识库条目模型
class KnowledgeBaseItem(Base):
    """知识库条目模型，用于存储知识库内容"""
    __tablename__ = "knowledge_base_items"

    id = Column(Integer, primary_key=True, index=True)
    type = Column(Enum(KnowledgeBaseType), nullable=False, index=True)
    title = Column(String(255), nullable=False, index=True)
    content = Column(Text, nullable=False)
    kb_metadata = Column(Text, nullable=True)  # 存储JSON格式的元数据
    source_url = Column(String(255), nullable=True)  # 来源网址
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    created_by = Column(Integer, ForeignKey("users.id"), index=True)

    # 关系
    creator = relationship("User")

# 知识库上传记录模型
class KnowledgeBaseUpload(Base):
    """知识库上传记录模型，用于记录知识库文件的上传历史"""
    __tablename__ = "knowledge_base_uploads"

    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String(255), nullable=False)
    file_type = Column(String(50), nullable=False)  # 文件类型，如 pdf, html, md 等
    file_size = Column(Integer, nullable=False)  # 文件大小（字节）
    items_count = Column(Integer, default=0)  # 从该文件提取的知识库条目数量
    processed = Column(Boolean, default=False)  # 是否已处理完成
    kb_metadata  = Column(Text, nullable=True)  # 存储JSON格式的元数据
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    uploaded_by = Column(Integer, ForeignKey("users.id"), index=True)

    # 关系
    uploader = relationship("User")

# 管理员验证码模型
class AdminVerification(Base):
    """管理员验证码模型，用于存储管理员注册时的验证码"""
    __tablename__ = "admin_verifications"

    id = Column(Integer, primary_key=True, index=True)
    code = Column(String(50), unique=True, nullable=False, index=True)
    is_used = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    used_at = Column(DateTime(timezone=True), nullable=True)
    used_by = Column(Integer, ForeignKey("users.id"), nullable=True)

    # 关系
    user = relationship("User")


# 合成上下文模型
class CraftingContext(Base):
    """合成上下文模型，用于存储对话级别的合成树信息"""
    __tablename__ = "crafting_contexts"

    id = Column(Integer, primary_key=True, index=True)
    conversation_id = Column(Integer, ForeignKey("conversations.id"), unique=True, nullable=False, index=True)
    target_item = Column(String(255), nullable=False, index=True)
    shared_tree = Column(JSON, nullable=False)  # 存储完整合成树JSON
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), index=True)

    # 关系
    conversation = relationship("Conversation")


# 初始化数据库
def init_db():
    Base.metadata.create_all(bind=engine)

# 获取数据库会话
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()