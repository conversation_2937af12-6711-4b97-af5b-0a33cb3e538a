"""
反馈服务模块

此模块提供与用户反馈相关的核心业务逻辑服务。
它封装了反馈的创建、查询和管理功能。
"""

from sqlalchemy.orm import Session
from typing import List, Optional
from fastapi import HTTPException, status

from ..database import User, Feedback, FeedbackType, FeedbackStatus


class FeedbackService:
    """
    反馈服务类，提供反馈管理的核心业务逻辑
    """

    def __init__(self, db: Session):
        """
        初始化反馈服务

        Args:
            db: 数据库会话
        """
        self.db = db

    async def create_feedback(self, user_id: int, feedback_type: FeedbackType, content: str) -> Feedback:
        """
        创建新的反馈

        Args:
            user_id: 用户ID
            feedback_type: 反馈类型
            content: 反馈内容

        Returns:
            Feedback: 创建的反馈对象
        """
        try:
            # 创建新的反馈记录
            new_feedback = Feedback(
                user_id=user_id,
                type=feedback_type,
                content=content,
                status=FeedbackStatus.PENDING
            )
            
            self.db.add(new_feedback)
            self.db.commit()
            self.db.refresh(new_feedback)
            
            return new_feedback
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"提交反馈失败: {str(e)}"
            )

    async def get_user_feedback(self, user_id: int) -> List[dict]:
        """
        获取用户的反馈历史

        Args:
            user_id: 用户ID

        Returns:
            List[dict]: 反馈列表，每个反馈包含用户名
        """
        try:
            # 获取用户
            user = self.db.query(User).filter(User.id == user_id).first()
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="用户不存在"
                )
            
            # 获取用户的反馈
            feedbacks = self.db.query(Feedback).filter(Feedback.user_id == user_id).all()
            
            # 为每个反馈添加用户名
            result = []
            for feedback in feedbacks:
                feedback_dict = {
                    "id": feedback.id,
                    "type": feedback.type,
                    "content": feedback.content,
                    "status": feedback.status,
                    "admin_response": feedback.admin_response,
                    "created_at": feedback.created_at,
                    "updated_at": feedback.updated_at,
                    "user_id": feedback.user_id,
                    "username": user.username
                }
                result.append(feedback_dict)
            
            return result
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取反馈失败: {str(e)}"
            )

    async def get_all_feedback(self, status_filter: Optional[FeedbackStatus] = None) -> List[dict]:
        """
        获取所有反馈（管理员功能）

        Args:
            status_filter: 可选的状态过滤器

        Returns:
            List[dict]: 反馈列表，每个反馈包含用户名
        """
        try:
            query = self.db.query(Feedback).join(User)
            if status_filter:
                query = query.filter(Feedback.status == status_filter)
            
            feedbacks = query.order_by(Feedback.created_at.desc()).all()
            
            # 为每个反馈添加用户名
            result = []
            for feedback in feedbacks:
                feedback_dict = {
                    "id": feedback.id,
                    "type": feedback.type,
                    "content": feedback.content,
                    "status": feedback.status,
                    "admin_response": feedback.admin_response,
                    "created_at": feedback.created_at,
                    "updated_at": feedback.updated_at,
                    "user_id": feedback.user_id,
                    "username": feedback.user.username
                }
                result.append(feedback_dict)
            
            return result
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取反馈失败: {str(e)}"
            )

    async def update_feedback(self, feedback_id: int, status: Optional[FeedbackStatus] = None, admin_response: Optional[str] = None) -> Feedback:
        """
        更新反馈状态和管理员回复（管理员功能）

        Args:
            feedback_id: 反馈ID
            status: 新状态，可选
            admin_response: 管理员回复，可选

        Returns:
            Feedback: 更新后的反馈对象
        """
        try:
            # 查找反馈
            feedback = self.db.query(Feedback).filter(Feedback.id == feedback_id).first()
            if not feedback:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="找不到指定反馈"
                )
            
            # 更新状态和响应
            if status:
                feedback.status = status
            if admin_response:
                feedback.admin_response = admin_response
            
            self.db.commit()
            self.db.refresh(feedback)
            
            return feedback
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"更新反馈失败: {str(e)}"
            )
