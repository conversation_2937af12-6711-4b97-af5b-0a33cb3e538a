import json
import os
import re
import time
import random
import logging
import traceback
from bs4 import BeautifulSoup
from urllib.parse import urljoin, unquote
from pathlib import Path

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options as ChromeOptions
from webdriver_manager.chrome import ChromeDriverManager
from selenium.common.exceptions import TimeoutException, WebDriverException

WIKI_OUTPUT_FOLDER = './wiki_total'
LOG_FILE = 'crawl_wiki_from_local_html_log.txt'
REQUEST_TIMEOUT = 30
MAX_RETRIES = 3
LOCAL_HTML_FILE = 'total.html'

WIKI_BASE_URL = "https://zh.minecraft.wiki"

DEFAULT_COOKIES = [
    {"name": "theme", "value": "light"},
    {"name": "VEE", "value": "wikitext"}
]

tutorial_urls = []

logging.basicConfig(filename=LOG_FILE, level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S')

def my_log(msg):
    print(msg)
    logging.info(msg)

def clean_text(text):
    if text:
        text = text.replace('\xa0', ' ')
        text = re.sub(r'\s+', ' ', text)
        return text.strip()
    return ""

def extract_wiki_id(wiki_url):
    match = re.search(r'/w/(.+)$', wiki_url)
    if match:
        path_segment = match.group(1)
        path_segment = unquote(path_segment)
        path_segment = path_segment.replace('/', '_')
        path_segment = path_segment.replace(' ', '_')
        path_segment = path_segment.replace(':', '_')
        path_segment = path_segment.replace('?', '_')
        path_segment = path_segment.replace('*', '_')
        path_segment = path_segment.replace('"', '_')
        path_segment = path_segment.replace('<', '_')
        path_segment = path_segment.replace('>', '_')
        path_segment = path_segment.replace('|', '_')
        
        max_filename_len = 150
        if len(path_segment) > max_filename_len:
            my_log(f"Warning: Extracted wiki_id segment '{path_segment}' is too long ({len(path_segment)} chars). Truncating.")
            path_segment = path_segment[:max_filename_len]
            
        return path_segment
    return None

def clean_html_content(html_content):
    soup = BeautifulSoup(html_content, 'html.parser')

    selectors_to_remove = [
        'script', 'style', '.mw-editsection', '.mw-references-wrap',
        '.reference', '#p-lang-btn', '.vector-menu-content', '#siteNotice',
        '#mw-navigation', '.mw-footer', '#footer', '.noprint', '.toc',
        '#catlinks', '.printfooter', 'div.thumb', 'figure', '.gallery',
        'table.navbox', '.vertical-navbox', '.infobox', '.metadata', '.ambox'
    ]
    for selector in selectors_to_remove:
        for unwanted in soup.select(selector):
            unwanted.decompose()
    
    content = clean_text(soup.get_text(separator=' ', strip=True))
    return content

def load_urls_from_local_html(html_file_path):
    global tutorial_urls
    try:
        my_log(f"正在从本地 HTML 文件读取所有 /w/ 开头的链接: {html_file_path}")
        if not os.path.exists(html_file_path):
            my_log(f"[错误] HTML 文件未找到: {html_file_path}")
            return False
            
        with open(html_file_path, 'r', encoding='utf-8') as f:
            page_source = f.read()

        soup = BeautifulSoup(page_source, 'html.parser')
        urls = []
        link_elements = soup.select('a[href^="/w/"]')

        for link_element in link_elements:
            href = link_element.get('href')
            if href:
                full_url = urljoin(WIKI_BASE_URL, href)
                if full_url not in urls:
                    urls.append(full_url)

        if urls:
            my_log(f"从 {html_file_path} 成功获取到 {len(urls)} 个 /w/ 开头的链接 (无额外过滤)")
            tutorial_urls = list(set(urls))
            my_log(f"Unique links to process: {len(tutorial_urls)}")
            return True
        else:
            my_log(f"[提示] 未能从 {html_file_path} 找到任何以 /w/ 开头的链接。")
            return False

    except FileNotFoundError:
        my_log(f"[致命错误] HTML 文件读取时未找到: {html_file_path}")
        return False
    except Exception as e:
        my_log(f"从 {html_file_path} 读取链接时发生未知错误: {e}")
        traceback.print_exc()
        return False
    

def process_wiki_page(driver, url):
    wiki_id = extract_wiki_id(url)
    if not wiki_id:
        my_log(f"[错误] 无法从 URL 提取 wiki ID: {url}")
        return "error"
    
    safe_filename = f"wiki_{wiki_id}.json"
    output_path = Path(WIKI_OUTPUT_FOLDER) / safe_filename
    if output_path.exists() and output_path.stat().st_size > 0:
        my_log(f"跳过已存在的文件: {output_path}")
        return "skipped"

    my_log(f"Fetching Wiki {wiki_id} (filename: {safe_filename}): {url}")
    
    try:
        driver.get(url)
        time.sleep(random.uniform(0.3, 0.5))

        page_title = driver.title
        page_source = driver.page_source

        if "Weird Gloop" in page_title or "Redirecting" in page_title or "访问被拒绝" in page_source or \
           ("error" in page_title.lower() and "not found" in page_source.lower()) or \
           "Bad Gateway" in page_title or "Service Unavailable" in page_title:
            my_log(f"[Blocked/Error Indicator] Wiki {wiki_id}: {url} - Title: {page_title}")
            if "不存在" in page_title or "Not Found" in page_title or "no article" in page_source.lower() or "页面不存在" in page_source:
                my_log(f"[404 Not Found Suspected by Title/Source] Wiki {wiki_id}: {url}")
                return "not_found"
            return "blocked"

        soup = BeautifulSoup(page_source, 'html.parser')
        if not soup:
            my_log(f"[错误] 无法解析 HTML for Wiki {wiki_id}: {url}")
            return "error"

        title_elem = soup.find('h1', {'id': 'firstHeading', 'class': 'firstHeading'})
        if not title_elem:
            title_elem = soup.find('h1', {'id': 'firstHeading'})
        title_text = clean_text(title_elem.get_text(separator=' ', strip=True)) if title_elem else f"标题未找到 (Wiki {wiki_id})"
        
        if "页面不存在" in title_text or "不存在的页面" in title_text or \
           (title_elem and title_elem.find('span', class_='mw-page-title-namespace')) and "Special" in title_elem.find('span', class_='mw-page-title-namespace').get_text() or \
           (title_elem and "new" in title_elem.get('class', [])):
            my_log(f"[Not Found/Special Page by Title] Wiki {wiki_id}: {url} - Title: {title_text}")
            return "not_found"

        content_div = soup.find('div', {'class': 'mw-body-content'})

        if content_div:
            content_tags_to_extract = ['p', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6']
            
            elements_in_order = content_div.find_all(content_tags_to_extract)
            
            formatted_text_parts = []
            for element in elements_in_order:
                text = element.get_text(strip=True)
                
                text = re.sub(r'\[\s*编辑\s*\|\s*编辑源代码\s*\]', '', text)
                
                if text:
                    tag_name = element.name
                    formatted_line = ""
                    
                    if tag_name == 'h1':
                        formatted_line = f"# {text}"
                    elif tag_name == 'h2':
                        formatted_line = f"## {text}"
                    elif tag_name == 'h3':
                        formatted_line = f"### {text}"
                    elif tag_name == 'h4':
                        formatted_line = f"#### {text}"
                    elif tag_name == 'h5':
                        formatted_line = f"##### {text}"
                    elif tag_name == 'h6':
                        formatted_line = f"###### {text}"
                    elif tag_name == 'li':
                        formatted_line = f"- {text}"
                    elif tag_name == 'p':
                        formatted_line = text
                    else:
                        formatted_line = text
                        
                    formatted_text_parts.append(formatted_line)
                    
            text_content = "\n".join(formatted_text_parts)
            
            navigation_marker = "导航[编辑|编辑源代码]"
            if "导航" in text_content:
                lines = text_content.splitlines()
                nav_index = -1
                for i, line in enumerate(lines):
                    if (line.startswith("# ") or \
                        line.startswith("## ") or \
                        line.startswith("### ") or \
                        line.startswith("#### ") or \
                        line.startswith("##### ") or \
                        line.startswith("###### ")) and "导航" in line:
                        nav_index = i
                        break
                    elif "导航[编辑|编辑源代码]" in line:
                        nav_index = i
                        break

                if nav_index != -1:
                    text_content = "\n".join(lines[:nav_index])
                    print(f"Content truncated at navigation section (index {nav_index}).")

            actual_title = title_text
            if ':' in title_text:
                parts = title_text.split(':', 1)
                if len(parts) > 1 and parts[1].strip():
                    actual_title = parts[1].strip()
                else:
                    actual_title = parts[0].strip()
            
            data = {
                "url": url,
                "title": actual_title,
                "content": text_content.strip()
            }

        output_path.parent.mkdir(parents=True, exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)

        my_log(f"Wiki {wiki_id} saved successfully to {output_path}")
        return True

    except TimeoutException:
        my_log(f"[Timeout] Wiki {wiki_id}: {url} (Page load timed out after {REQUEST_TIMEOUT}s)")
        return "timeout"
    except WebDriverException as e:
        err_msg = str(e).encode('utf-8', 'replace').decode('utf-8')
        my_log(f"[WebDriver Error] Wiki {wiki_id} ({url}): {err_msg[:500]}")
        if "net::ERR_NAME_NOT_RESOLVED" in str(e) or "Reached error page" in str(e):
            my_log(f"Suspected Not Found or Network issue for {wiki_id}: {e}")
            return "not_found"
        return "error"
    except OSError as e:
        my_log(f"[OSError during file operation] Wiki {wiki_id} ({url}), Path {output_path}: {e}")
        traceback.print_exc()
        return "error"
    except Exception as e:
        my_log(f"[Processing Error] Wiki {wiki_id} ({url}): {e}")
        traceback.print_exc()
        return "error"

if __name__ == "__main__":
    start_time_main = time.perf_counter()
    my_log(f"脚本开始执行 - Wiki 内容爬虫 (Selenium版, 从 '{LOCAL_HTML_FILE}' 读取链接)...")

    os.makedirs(WIKI_OUTPUT_FOLDER, exist_ok=True)
    my_log(f"输出目录: {WIKI_OUTPUT_FOLDER}")
    my_log("-" * 30)
    my_log("重要提示:")
    my_log(f"  - 将从本地文件 '{LOCAL_HTML_FILE}' 读取 /w/... 格式的链接")
    my_log(f"  - 对每个链接的目标页面，爬取 title, URL 和 content")
    my_log(f"  - 使用 Selenium WebDriver (Chrome) 进行爬取")
    my_log(f"  - 目标 Wiki 基础 URL: {WIKI_BASE_URL}")
    my_log("-" * 30)

    chrome_options = ChromeOptions()
    chrome_options.add_argument('--headless=new')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36')
    chrome_options.add_argument("--lang=zh-CN,zh;q=0.9,en;q=0.8")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_experimental_option("prefs", {
          "profile.managed_default_content_settings.images": 2,
          "profile.managed_default_content_settings.popups": 2,
    })

    driver = None
    try:
        my_log("正在初始化 Selenium WebDriver...")
        try:
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
        except Exception as e_wd_manager:
            my_log(f"WebDriver Manager 失败: {e_wd_manager}. 尝试使用系统 PATH 中的 chromedriver.")
            driver = webdriver.Chrome(options=chrome_options)

        driver.set_page_load_timeout(REQUEST_TIMEOUT)
        
        my_log(f"导航到 {WIKI_BASE_URL} 以设置初始 Cookies.")
        driver.get(WIKI_BASE_URL)
        time.sleep(random.uniform(0.15, 0.25))
        for cookie_dict in DEFAULT_COOKIES:
            try:
                driver.add_cookie(cookie_dict)
            except Exception as cookie_e:
                my_log(f"警告: 无法添加 Cookie {cookie_dict.get('name', '')}: {cookie_e}")
        my_log(f"默认 Cookies 已为域名 {WIKI_BASE_URL} 处理完毕")
        time.sleep(random.uniform(0.05, 0.1))

        my_log(f"从 '{LOCAL_HTML_FILE}' 加载链接...")
        if not load_urls_from_local_html(LOCAL_HTML_FILE):
            my_log(f"从 '{LOCAL_HTML_FILE}' 加载链接失败。脚本终止。")
            if driver: driver.quit()
            exit(1)
    
        if not tutorial_urls:
            my_log("未找到任何链接进行处理。脚本终止。")
            if driver: driver.quit()
            exit(1)
            
        my_log(f"共获取到 {len(tutorial_urls)} 个链接，准备开始爬取...")
    
        processed_count = 0
        success_count = 0
        fail_count = 0
        skipped_count = 0
        not_found_count = 0
        blocked_count = 0
        timeout_count = 0

        BATCH_SIZE = 5
        
        unique_urls_to_process = list(set(tutorial_urls))
        random.shuffle(unique_urls_to_process)
        total_urls_to_process = len(unique_urls_to_process)
        my_log(f"将处理 {total_urls_to_process} 个独立 URL.")

        for idx, url in enumerate(unique_urls_to_process):
            current_url_num = idx + 1
            
            wiki_id = extract_wiki_id(url)
            
            my_log(f"--- 处理 URL {current_url_num}/{total_urls_to_process}: {url} ---")

            if current_url_num > 1 and (current_url_num -1) % BATCH_SIZE != 0 :
                time.sleep(random.uniform(0.08, 0.25))

            page_processed_this_attempt_cycle = False
            last_result_status = "unknown"

            for attempt in range(MAX_RETRIES):
                if attempt > 0:
                    retry_sleep = random.uniform(0.3, 0.7) * (attempt + 1)
                    my_log(f"重试 {attempt+1}/{MAX_RETRIES} for {wiki_id if wiki_id else url}，等待 {retry_sleep:.2f} 秒...")
                    time.sleep(retry_sleep)

                result = process_wiki_page(driver, url)
                last_result_status = result

                if result is True:
                    success_count += 1
                    page_processed_this_attempt_cycle = True
                    break
                elif result == "skipped":
                    skipped_count += 1
                    page_processed_this_attempt_cycle = True
                    break
                elif result == "not_found":
                    not_found_count += 1
                    page_processed_this_attempt_cycle = True
                    try:
                        Path(WIKI_OUTPUT_FOLDER).mkdir(parents=True, exist_ok=True)
                        placeholder_path = Path(WIKI_OUTPUT_FOLDER) / f"wiki_{wiki_id if wiki_id else 'unknown_id'}_not_found.json"
                        with open(placeholder_path, 'w', encoding='utf-8') as f_nf:
                            json.dump({"id": wiki_id, "url": url, "status": "not_found", "message": "Page indicated as not found or is a non-content page."}, f_nf, ensure_ascii=False, indent=2)
                        my_log(f"记录 'not_found' 状态 for {wiki_id} at {placeholder_path}")
                    except Exception as e_nf_file:
                        my_log(f"无法创建 'not_found' 记录文件 for {wiki_id}: {e_nf_file}")
                    break
                elif result == "blocked":
                    my_log(f"Wiki {wiki_id if wiki_id else url} 尝试 {attempt + 1}/{MAX_RETRIES} - 状态: ({result}).")
                    if attempt == MAX_RETRIES - 1:
                        blocked_count +=1
                    time.sleep(random.uniform(1.0, 2.0))
                elif result == "timeout":
                    my_log(f"Wiki {wiki_id if wiki_id else url} 尝试 {attempt + 1}/{MAX_RETRIES} - 状态: ({result}).")
                    if attempt == MAX_RETRIES - 1:
                        timeout_count +=1
                elif result == "error":
                    my_log(f"Wiki {wiki_id if wiki_id else url} 尝试 {attempt + 1}/{MAX_RETRIES} - 状态: ({result}).")
                    if attempt == MAX_RETRIES - 1:
                        fail_count += 1
            
            processed_count += 1

            if not page_processed_this_attempt_cycle:
                my_log(f"[最终处理失败] Wiki {wiki_id if wiki_id else url} 状态: {last_result_status} (尝试 {MAX_RETRIES} 次). URL: {url}")
                if last_result_status not in ["blocked", "timeout", "error"]:
                    fail_count +=1
                try:
                    Path(WIKI_OUTPUT_FOLDER).mkdir(parents=True, exist_ok=True)
                    error_file_path = Path(WIKI_OUTPUT_FOLDER) / f"wiki_{wiki_id if wiki_id else 'unknown_id'}_failed.json"
                    with open(error_file_path, 'w', encoding='utf-8') as f_err:
                        json.dump({"id": wiki_id, "url": url, "status": "failed_after_retries", "reason": last_result_status}, f_err, ensure_ascii=False, indent=2)
                    my_log(f"记录失败状态 for {wiki_id} at {error_file_path}")
                except Exception as e_fail_file:
                    my_log(f"无法创建失败记录文件 for {wiki_id}: {e_fail_file}")

            if current_url_num % BATCH_SIZE == 0 and current_url_num < total_urls_to_process:
                batch_sleep_time = random.uniform(0.8, 1.5)
                my_log(f"批次 ({BATCH_SIZE} URLs) 完成，休息 {batch_sleep_time:.2f} 秒...")
                time.sleep(batch_sleep_time)

            if processed_count % 10 == 0 or not page_processed_this_attempt_cycle:
                my_log(f"进度 - 已尝试URL: {processed_count}/{total_urls_to_process}, 成功: {success_count}, 跳过: {skipped_count}, 未找到: {not_found_count}, 被阻止: {blocked_count}, 超时: {timeout_count}, 其他失败: {fail_count}")

    except KeyboardInterrupt:
        my_log("脚本被用户中断 (KeyboardInterrupt)。")
    except Exception as e_main:
        my_log(f"主执行流程中发生严重错误: {e_main}")
        traceback.print_exc()
    finally:
        if driver:
            my_log("正在关闭 Selenium WebDriver...")
            driver.quit()
            my_log("WebDriver 已关闭。")

    elapsed_main = time.perf_counter() - start_time_main
    my_log("-" * 30)
    my_log(f"爬虫执行完毕.")
    my_log(f"总耗时: {elapsed_main:.2f} 秒 ({elapsed_main/60:.2f} 分钟)")
    my_log(f"从 '{LOCAL_HTML_FILE}' 读取的初始链接数: {len(tutorial_urls)} (处理前去重)")
    my_log(f"总计独立URL处理目标: {total_urls_to_process}")

    my_log(f"最终统计:")
    my_log(f"  成功抓取并保存: {success_count}")
    my_log(f"  已存在跳过: {skipped_count}")
    my_log(f"  页面未找到/非内容页: {not_found_count}")
    my_log(f"  访问被阻止 (最终): {blocked_count}")
    my_log(f"  处理超时 (最终): {timeout_count}")
    my_log(f"  其他原因处理失败 (最终): {fail_count}")
    
    total_accounted_for = success_count + skipped_count + not_found_count + blocked_count + timeout_count + fail_count
    my_log(f"  总计URL已尝试: {processed_count}")
    my_log(f"  (核对: 成功+跳过+未找到+被阻止+超时+其他失败 = {total_accounted_for})")
    if processed_count != total_accounted_for:
        my_log(f"  [警告] 计数不匹配: processed_count ({processed_count}) vs total_accounted_for ({total_accounted_for})")
    my_log("-" * 30)
    my_log(f"日志文件位于: {LOG_FILE}")
    my_log(f"输出文件位于: {WIKI_OUTPUT_FOLDER}")