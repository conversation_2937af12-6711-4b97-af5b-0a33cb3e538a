"""
认证 API 路由模块

此模块定义了与用户认证相关的 API 端点，
包括用户注册、登录和获取访问令牌等功能。
"""
from fastapi import APIRouter, Depends, HTTPException
from fastapi.security import OAuth2PasswordRequestForm
from pydantic import BaseModel
from typing import Optional
from ..dependencies import get_auth_service
from ..services.auth_service import AuthService
from ..database import UserRole


# 创建路由
router = APIRouter(prefix="/auth", tags=["auth"])

# 请求和响应模型
class UserRegister(BaseModel):
    username: str
    password: str
    email: str

class AdminRegister(BaseModel):
    username: str
    password: str
    email: str
    verification: str

class UserLogin(BaseModel):
    username: str
    password: str

class TokenResponse(BaseModel):
    success: bool
    message: str
    role: UserRole
    token: Optional[str] = None
    user_id: Optional[int] = None
    username: Optional[str] = None

class BasicResponse(BaseModel):
    success: bool
    message: str

# 注册路由
@router.post("/register", response_model=TokenResponse)
async def register(
    user_data: UserRegister,
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    注册新用户

    Args:
        user_data: 用户注册数据
        auth_service: 认证服务实例

    Returns:
        TokenResponse: 包含令牌和用户信息的响应
    """
    # 使用服务层注册用户
    result = await auth_service.register_user(
        username=user_data.username,
        email=user_data.email,
        password=user_data.password
    )

    # 添加角色信息
    result["role"] = UserRole.USER

    return result

# 管理员注册路由
@router.post("/adminRegister", response_model=TokenResponse)
async def admin_register(
    admin_data: AdminRegister,
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    注册新管理员

    Args:
        admin_data: 管理员注册数据
        auth_service: 认证服务实例

    Returns:
        TokenResponse: 包含令牌和用户信息的响应
    """
    # 使用服务层注册管理员
    result = await auth_service.register_admin(
        username=admin_data.username,
        email=admin_data.email,
        password=admin_data.password,
        verification=admin_data.verification
    )

    # 添加角色信息
    result["role"] = UserRole.ADMIN

    return result

# 登录路由
@router.post("/login", response_model=TokenResponse)
async def login(
    user_data: UserLogin,
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    用户登录

    Args:
        user_data: 用户登录数据
        auth_service: 认证服务实例

    Returns:
        TokenResponse: 包含令牌和用户信息的响应
    """
    # 使用服务层登录用户
    result = await auth_service.login_user(
        username=user_data.username,
        password=user_data.password
    )

    return result

# OAuth2兼容登录路由 (用于支持swagger UI)
@router.post("/token")
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    OAuth2兼容的登录端点

    Args:
        form_data: OAuth2表单数据
        auth_service: 认证服务实例

    Returns:
        dict: 包含访问令牌的字典
    """
    try:
        # 使用服务层登录用户
        login_result = await auth_service.login_user(
            username=form_data.username,
            password=form_data.password
        )

        # 返回OAuth2兼容的响应格式
        return {"access_token": login_result["token"], "token_type": "bearer"}
    except HTTPException:
        # 重新抛出异常，保持与原始行为一致
        raise