stages:
  - deploy

deploy_job:
  stage: deploy
  tags:
    - python
  script:
    - rm -rf /opt/fastapi-app/*
    - cp -r ./* /opt/fastapi-app/
    - cd /opt/fastapi-app
    - echo "# 自动生成的环境变量配置 - $(date)" > .env
    - echo "LLM_MODEL=$LLM_MODEL" >> .env
    - echo "BASE_URL=$BASE_URL" >> .env
    - echo "API_KEY=$API_KEY" >> .env
    - echo "BAAI_PATH=$BAAI_PATH" >> .env
    - echo "CHROMA_PATH=$CHROMA_PATH" >> .env
    - echo "DEVICE=$DEVICE" >> .env
    - echo "DATABASE_URL=$DATABASE_URL" >> .env
    - echo "RAG_LLM_MODEL=$RAG_LLM_MODEL" >> .env
    - echo "RAG_BASE_URL=$RAG_BASE_URL" >> .env
    - echo "RAG_API_KEY=$RAG_API_KEY" >> .env
    - echo "RAG_TEMPERATURE=$RAG_TEMPERATURE" >> .env
    - echo "TITLE_LLM_MODEL=$TITLE_LLM_MODEL" >> .env
    - echo "TITLE_BASE_URL=$TITLE_BASE_URL" >> .env
    - echo "TITLE_API_KEY=$TITLE_API_KEY" >> .env
    - echo "TITLE_TEMPERATURE=$TITLE_TEMPERATURE" >> .env
    - echo "METADATA_LLM_MODEL=$METADATA_LLM_MODEL" >> .env
    - echo "METADATA_BASE_URL=$METADATA_BASE_URL" >> .env
    - echo "METADATA_API_KEY=$METADATA_API_KEY" >> .env
    - echo "METADATA_TEMPERATURE=$METADATA_TEMPERATURE" >> .env
    - echo "INTENT_LLM_MODEL=$INTENT_LLM_MODEL" >> .env
    - echo "INTENT_BASE_URL=$INTENT_BASE_URL" >> .env
    - echo "INTENT_API_KEY=$INTENT_API_KEY" >> .env
    - echo "INTENT_TEMPERATURE=$INTENT_TEMPERATURE" >> .env
    - echo "RERANK_MODEL_NAME=$RERANK_MODEL_NAME" >> .env
    - echo "RERANK_TOP_K=$RERANK_TOP_K" >> .env
    - echo "RERANK_BATCH_SIZE=$RERANK_BATCH_SIZE" >> .env
    - echo "QUERY_EXPANSION_ENABLED=$QUERY_EXPANSION_ENABLED" >> .env
    - echo "QUERY_EXPANSION_TEMPERATURE=$QUERY_EXPANSION_TEMPERATURE" >> .env
    - echo "QUERY_EXPANSION_LLM_MODEL=$QUERY_EXPANSION_LLM_MODEL" >> .env
    - echo "QUERY_EXPANSION_BASE_URL=$QUERY_EXPANSION_BASE_URL" >> .env
    - echo "QUERY_EXPANSION_API_KEY=$QUERY_EXPANSION_API_KEY" >> .env
    - sudo systemctl restart fastapi
  only:
    - main