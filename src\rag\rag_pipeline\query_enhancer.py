"""
查询增强模块

此模块提供了查询增强功能，用于提高检索的召回率。
"""

import time
from typing import Optional
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import HumanMessage

# 使用统一的日志配置
from ..logging_config import get_logger
from .. import config

logger = get_logger("query_enhancer")

class QueryEnhancer:
    """
    查询增强器，用于提高检索的召回率
    
    使用LLM生成更丰富的搜索表达，提高检索相关文档的能力。
    """
    
    def __init__(self, llm: BaseChatModel, expansion_prompt_template_str: str):
        """
        初始化查询增强器
        
        Args:
            llm: 用于查询扩展的语言模型
            expansion_prompt_template_str: 查询扩展提示模板
        """
        self.llm = llm
        self.prompt_template = expansion_prompt_template_str
        self.enabled = config.QUERY_EXPANSION_ENABLED
        
        if self.enabled:
            logger.info("查询增强器已启用")
        else:
            logger.info("查询增强器已禁用")
    
    async def expand_query(self, original_query: str) -> str:
        """
        扩展用户查询，生成更优化的搜索查询
        
        Args:
            original_query: 用户原始查询
            
        Returns:
            str: 扩展后的查询
        """
        # 如果查询增强被禁用，直接返回原始查询
        if not self.enabled:
            logger.debug("查询增强已禁用，使用原始查询")
            return original_query
            
        # 如果查询为空，直接返回
        if not original_query or not original_query.strip():
            logger.warning("查询为空，无法进行扩展")
            return original_query
        
        # 记录开始时间
        start_time = time.time()
        logger.info(f"开始扩展查询: '{original_query[:50]}{'...' if len(original_query) > 50 else ''}'")
        
        try:
            # 格式化提示
            formatted_prompt = self.prompt_template.format(original_query=original_query)
            
            # 调用LLM
            messages = [HumanMessage(content=formatted_prompt)]
            response = await self.llm.ainvoke(
                messages,
                temperature=config.QUERY_EXPANSION_TEMPERATURE
            )
            enhanced_query = response.content.strip()
            
            # 清理响应
            if "优化后的搜索查询:" in enhanced_query:
                enhanced_query = enhanced_query.split("优化后的搜索查询:")[-1].strip()
                
            if not enhanced_query:
                logger.warning(f"LLM返回空查询扩展结果，使用原始查询: '{original_query}'")
                return original_query
            
            # 记录扩展时间和结果
            expansion_time = time.time() - start_time
            logger.info(f"查询扩展完成，耗时: {expansion_time:.2f}秒")
            logger.info(f"原始查询: '{original_query}'")
            logger.info(f"扩展查询: '{enhanced_query}'")
            
            return enhanced_query
            
        except Exception as e:
            logger.error(f"查询扩展失败: {e}", exc_info=True)
            return original_query  # 出错时返回原始查询
