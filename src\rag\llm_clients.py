"""
LLM客户端集合

此模块集中管理所有大语言模型客户端实例，为不同任务提供专用的LLM客户端。
这样可以为不同任务配置不同的模型和API端点，而不需要在每个地方重复配置。
"""

from langchain_openai import ChatOpenAI

# 使用统一的日志配置
from .logging_config import get_logger
from . import config # 新增配置导入
logger = get_logger("llm_clients")

# 创建主要RAG系统使用的LLM客户端
def create_rag_llm():
    """创建RAG系统使用的LLM客户端实例"""
    return ChatOpenAI(
        model=config.RAG_LLM_MODEL,
        base_url=config.RAG_BASE_URL,
        api_key=config.RAG_API_KEY,
        temperature=config.RAG_TEMPERATURE
    )

# 创建对话标题生成使用的LLM客户端
def create_title_generation_llm():
    """创建标题生成使用的LLM客户端实例"""
    return ChatOpenAI(
        model=config.TITLE_LLM_MODEL,
        base_url=config.TITLE_BASE_URL,
        api_key=config.TITLE_API_KEY,
        temperature=config.TITLE_TEMPERATURE
    )

# 创建元数据匹配使用的LLM客户端
def create_metadata_matching_llm():
    """创建元数据匹配使用的LLM客户端实例"""
    return ChatOpenAI(
        model=config.METADATA_LLM_MODEL,
        base_url=config.METADATA_BASE_URL,
        api_key=config.METADATA_API_KEY,
        temperature=config.METADATA_TEMPERATURE
    )

# 创建意图分类使用的LLM客户端
def create_intent_classification_llm():
    """创建意图分类使用的LLM客户端实例"""
    return ChatOpenAI(
        model=config.INTENT_LLM_MODEL,
        base_url=config.INTENT_BASE_URL,
        api_key=config.INTENT_API_KEY,
        temperature=config.INTENT_TEMPERATURE
    )

# 创建查询扩展使用的LLM客户端
def create_query_expansion_llm():
    """创建查询扩展使用的LLM客户端实例"""
    return ChatOpenAI(
        model=config.QUERY_EXPANSION_LLM_MODEL,
        base_url=config.QUERY_EXPANSION_BASE_URL,
        api_key=config.QUERY_EXPANSION_API_KEY,
        temperature=config.QUERY_EXPANSION_TEMPERATURE
    )

# 创建物品实体识别使用的LLM客户端
def create_item_extraction_llm():
    """创建物品实体识别使用的LLM客户端实例"""
    return ChatOpenAI(
        model=config.ITEM_EXTRACTION_MODEL,
        base_url=config.ITEM_EXTRACTION_BASE_URL,
        api_key=config.ITEM_EXTRACTION_API_KEY,
        temperature=config.ITEM_EXTRACTION_TEMPERATURE
    )

# 添加一个私有字典来缓存LLM实例
_cached_llm_instances = {}

# 获取当前活跃的LLM实例
def get_active_llm(task_type="rag", llm_dict=None):
    """
    根据任务类型获取相应的LLM客户端实例

    Args:
        task_type: 任务类型，可选值包括 "rag"(默认), "title", "metadata", "intent"
        llm_dict: 可选的LLM字典，如果提供则从中获取LLM实例

    Returns:
        对应任务类型的LLM客户端实例
    """
    global _cached_llm_instances

    # 如果提供了llm_dict，从中获取LLM实例
    if llm_dict is not None:
        if task_type in llm_dict:
            return llm_dict[task_type]

    # 如果没有提供llm_dict，尝试从缓存中获取
    if task_type in _cached_llm_instances:
        return _cached_llm_instances[task_type]

    # 如果缓存中也没有，创建新的实例并缓存
    if task_type == "title":
        _cached_llm_instances[task_type] = create_title_generation_llm()
    elif task_type == "metadata":
        _cached_llm_instances[task_type] = create_metadata_matching_llm()
    elif task_type == "intent":
        _cached_llm_instances[task_type] = create_intent_classification_llm()
    elif task_type == "query_expansion":
        _cached_llm_instances[task_type] = create_query_expansion_llm()
    elif task_type == "item_extraction":
        _cached_llm_instances[task_type] = create_item_extraction_llm()
    else:  # 默认为"rag"
        _cached_llm_instances[task_type] = create_rag_llm()

    return _cached_llm_instances[task_type]
