# 合成导航功能完整后端实现计划

## 📋 项目概述

基于现有RAG系统架构和前端接口规范，实现"合成导航"功能的完整后端支持。

### 🎯 核心功能
1. **对话类型扩展**：支持"普通对话"和"合成导航"两种模式
2. **智能物品识别**：自动检测消息中的Minecraft物品实体
3. **会话级合成树管理**：为合成导航对话维护持久化的合成树
4. **智能响应决策**：根据物品识别结果动态返回合成卡片或普通文本

### 🔄 核心逻辑流程
```
用户消息 → 物品实体识别 → 响应类型决策 → 合成树管理 → 智能响应生成
    ↓              ↓              ↓            ↓            ↓
对话类型判断 → LLM+规则匹配 → crafting/text → 会话级存储 → SSE事件流
```

---

## 📁 文件结构变更

### 🆕 新增文件
```
src/rag/crafting/                           # 合成导航模块
├── __init__.py
├── item_entity_extractor.py               # 🔍 物品实体识别器
├── tree_generator.py                      # 🌳 合成树生成器
├── crafting_rag_pipeline.py              # 🔄 合成导航RAG Pipeline
└── prompts.py                             # 📝 合成导航提示模板

scripts/migrations/                         # 数据库迁移
├── 002_add_message_type_and_item_name.py  # 扩展Message模型
└── 003_add_crafting_contexts_table.py     # 新增CraftingContext表
```

### 🔧 修改文件
```
src/rag/database.py                        # 扩展数据库模型
src/rag/services/conversation_service.py   # 扩展对话服务
src/rag/routes/conversation.py             # 修改API路由
src/rag/dependencies.py                    # 新增依赖注入
src/rag/config.py                          # 添加配置项
.env                                       # 环境变量配置
```

---

## 🗄️ 数据库模型变更

### 📊 扩展Message模型
```sql
-- 🔄 核心逻辑：消息类型区分
ALTER TABLE messages
ADD COLUMN type ENUM('text', 'crafting') DEFAULT 'text' NOT NULL,
ADD COLUMN item_name VARCHAR(255) NULL,
ADD INDEX idx_messages_type (type),
ADD INDEX idx_messages_item_name (item_name);
```

### 🆕 新增CraftingContext模型
```sql
-- 🔄 核心逻辑：会话级合成上下文
CREATE TABLE crafting_contexts (
  id INT PRIMARY KEY AUTO_INCREMENT,
  conversation_id INT UNIQUE NOT NULL,
  target_item VARCHAR(255) NOT NULL,
  shared_tree JSON NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE
);
```

---

## 🚀 实施阶段计划

## 🏗️ 第一阶段：基础架构（1-2天）
**优先级：🔴 高**

### ✅ 任务清单
- [ ] **数据库迁移**
  - [ ] 创建 `scripts/migrations/002_add_message_type_and_item_name.py`
  - [ ] 创建 `scripts/migrations/003_add_crafting_contexts_table.py`
  - [ ] 执行迁移并验证数据库结构

- [ ] **基础模型扩展**
  - [ ] 修改 `src/rag/database.py`
    - [ ] 添加 `MessageType` 枚举
    - [ ] 扩展 `Message` 模型（type, item_name字段）
    - [ ] 新增 `CraftingContext` 模型
  - [ ] 验证模型定义正确性

### 🔄 核心逻辑实现点
- **消息类型区分**：通过 `MessageType` 枚举区分 `text` 和 `crafting` 消息
- **会话级上下文**：`CraftingContext` 与 `Conversation` 一对一关系

---

## 🧠 第二阶段：核心组件实现（3-4天）
**优先级：🔴 高**

### ✅ 任务清单
- [ ] **物品实体识别器** (`src/rag/crafting/item_entity_extractor.py`)
  - [ ] 实现 `ItemEntityExtractor` 类
  - [ ] 基于LLM的实体识别逻辑
  - [ ] Minecraft物品数据库加载
  - [ ] 识别结果验证机制

- [ ] **合成树生成器** (`src/rag/crafting/tree_generator.py`)
  - [ ] 实现 `CraftingTreeGenerator` 类
  - [ ] Mock数据生成逻辑（开发阶段）
  - [ ] Neo4j接口预留（标记但不实现）
  - [ ] 确保数据格式符合前端规范

- [ ] **合成导航RAG Pipeline** (`src/rag/crafting/crafting_rag_pipeline.py`)
  - [ ] 实现 `CraftingRAGPipeline` 类
  - [ ] 集成物品识别和响应生成
  - [ ] 智能响应类型决策逻辑
  - [ ] 与现有RAG系统兼容性

- [ ] **提示模板** (`src/rag/crafting/prompts.py`)
  - [ ] 物品实体识别提示模板
  - [ ] 合成导航响应生成模板

### 🔄 核心逻辑实现点
- **智能识别**：`LLM + 规则匹配` 混合识别策略
- **响应决策**：`extracted_items ? CRAFTING : TEXT` 逻辑
- **Pipeline集成**：继承现有RAG功能，扩展合成导航能力

---

## 🔧 第三阶段：服务层集成（2-3天）
**优先级：🟡 中**

### ✅ 任务清单
- [ ] **ConversationService扩展** (`src/rag/services/conversation_service.py`)
  - [ ] 添加合成导航消息处理方法
  - [ ] 实现合成上下文管理逻辑
  - [ ] 确保向后兼容性
  - [ ] 新增方法：
    - [ ] `handle_crafting_conversation()`
    - [ ] `save_crafting_context()`
    - [ ] `get_crafting_context()`

- [ ] **依赖注入配置** (`src/rag/dependencies.py`)
  - [ ] 新增 `get_item_entity_extractor()`
  - [ ] 新增 `get_crafting_tree_generator()`
  - [ ] 新增 `get_crafting_rag_pipeline()`
  - [ ] 修改 `get_conversation_service()` 支持合成导航

### 🔄 核心逻辑实现点
- **服务层路由**：根据对话类型选择处理逻辑
- **上下文管理**：会话级合成树的创建、存储、查询
- **依赖注入**：确保组件间正确的依赖关系

---

## 🌐 第四阶段：API接口实现（1-2天）
**优先级：🟡 中**

### ✅ 任务清单
- [ ] **路由修改** (`src/rag/routes/conversation.py`)
  - [ ] 修改 `NewMessageRequest` 支持对话类型
  - [ ] 更新 `stream_new_conversation()` 端点
  - [ ] 更新 `stream_message()` 端点
  - [ ] 实现新的SSE事件格式

- [ ] **后台任务扩展**
  - [ ] 修改 `save_assistant_message_task()` 支持消息类型
  - [ ] 新增合成上下文保存逻辑

- [ ] **配置文件扩展** (`src/rag/config.py`)
  - [ ] 添加物品识别相关配置
  - [ ] 添加Neo4j配置（预留）

### 🔄 核心逻辑实现点
- **SSE事件扩展**：新增 `crafting_context` 和 `message_complete` 事件
- **智能路由**：根据对话类型和物品识别结果选择响应方式
- **后台处理**：异步保存合成上下文，不阻塞响应流

---

## 🧪 第五阶段：测试和优化（1-2天）
**优先级：🟢 低**

### ✅ 任务清单
- [ ] **功能测试**
  - [ ] 端到端测试合成导航对话创建流程
  - [ ] 验证物品识别准确性
  - [ ] 确保SSE事件格式正确
  - [ ] 测试向后兼容性

- [ ] **性能优化**
  - [ ] 优化物品识别响应时间
  - [ ] 确保合成树生成不阻塞响应
  - [ ] 数据库查询优化

- [ ] **错误处理**
  - [ ] 物品识别失败的降级机制
  - [ ] 合成树生成异常处理
  - [ ] 数据一致性保障

### 🔄 核心逻辑验证点
- **识别准确性**：物品实体识别的准确率和召回率
- **响应性能**：合成导航模式下的响应时间
- **系统稳定性**：异常情况下的降级和恢复机制

---

## ⚙️ 配置管理

### 🆕 新增配置项
```python
# 物品实体识别配置
ITEM_EXTRACTION_MODEL = "THUDM/GLM-4-9B-0414"
ITEM_EXTRACTION_TEMPERATURE = 0.1

# Neo4j配置（预留）
NEO4J_ENABLED = False
NEO4J_URI = "bolt://localhost:7687"
NEO4J_USERNAME = "neo4j"
NEO4J_PASSWORD = "password"
```

---

## ⚠️ 风险评估和缓解策略

### 🚨 主要风险
1. **物品识别准确性**：可能存在误判或漏判
2. **性能影响**：新增处理逻辑可能影响响应速度
3. **数据一致性**：合成上下文与对话状态同步

### 🛡️ 缓解策略
1. **渐进式实现**：先用简单规则，后续优化为复杂NLP
2. **异步处理**：确保耗时操作不阻塞响应
3. **降级机制**：识别失败时自动回退到普通模式
4. **事务管理**：使用数据库事务确保一致性

---

## 📊 实施时间估算

| 阶段 | 时间估算 | 累计时间 | 关键里程碑 |
|------|----------|----------|------------|
| 第一阶段 | 1-2天 | 1-2天 | 数据库结构就绪 |
| 第二阶段 | 3-4天 | 4-6天 | 核心组件完成 |
| 第三阶段 | 2-3天 | 6-9天 | 服务层集成完成 |
| 第四阶段 | 1-2天 | 7-11天 | API接口就绪 |
| 第五阶段 | 1-2天 | 8-13天 | 功能完整可用 |

**总计**：8-13天

---

## 🎯 成功标准

### ✅ 功能完整性
- [ ] 支持合成导航对话类型创建
- [ ] 智能物品实体识别工作正常
- [ ] 会话级合成树管理功能完整
- [ ] SSE事件格式符合前端规范

### ✅ 兼容性保证
- [ ] 现有普通对话功能完全不受影响
- [ ] 数据库迁移无数据丢失
- [ ] API接口向后兼容

### ✅ 性能要求
- [ ] 物品识别响应时间 < 2秒
- [ ] 合成导航模式响应时间与普通模式相当
- [ ] 系统整体稳定性不受影响

---

## 📝 备注

- **Neo4j集成**：当前阶段标记但不实现，为未来扩展预留接口
- **物品数据库**：初期使用静态配置，后续可扩展为动态加载
- **测试策略**：优先手动测试，后续可补充自动化测试
- **部署策略**：建议在开发环境完整测试后再部署到生产环境

---

## 🔄 核心逻辑详细说明

### 1. 物品实体识别逻辑
```python
# 🔄 核心逻辑：智能物品识别
async def extract_item_entities(message: str) -> List[str]:
    """
    混合识别策略：
    1. LLM实体提取 → 候选物品列表
    2. 规则匹配验证 → 过滤无效物品
    3. 置信度排序 → 返回最佳匹配
    """
    llm_candidates = await llm_extract_entities(message)
    validated_items = validate_against_item_db(llm_candidates)
    return sort_by_confidence(validated_items)
```

### 2. 响应类型决策逻辑
```python
# 🔄 核心逻辑：智能响应决策
async def determine_response_type(message: str, conversation: Conversation):
    """
    决策流程：
    1. 物品实体识别 → extracted_items
    2. 如果识别到物品 → CRAFTING类型 + item_name
    3. 如果未识别到物品 → TEXT类型
    4. 检查合成上下文 → 决定是否生成新合成树
    """
    extracted_items = await extract_item_entities(message)

    if extracted_items:
        primary_item = extracted_items[0]
        needs_new_tree = not conversation.crafting_context
        return MessageType.CRAFTING, primary_item, needs_new_tree
    else:
        return MessageType.TEXT, None, False
```

### 3. 会话级合成树管理逻辑
```python
# 🔄 核心逻辑：会话级合成上下文管理
async def manage_crafting_context(conversation_id: int, target_item: str):
    """
    上下文管理策略：
    1. 检查现有上下文 → 如果存在则复用
    2. 如果不存在 → 生成新合成树
    3. 持久化存储 → 确保会话级别的一致性
    4. 后续消息 → 直接使用已存储的合成树
    """
    existing_context = await get_crafting_context(conversation_id)

    if not existing_context:
        crafting_tree = await generate_crafting_tree(target_item)
        await save_crafting_context(conversation_id, target_item, crafting_tree)
        return crafting_tree
    else:
        return existing_context.shared_tree
```

### 4. SSE事件流逻辑
```python
# 🔄 核心逻辑：智能SSE事件生成
async def generate_crafting_events(message_type: MessageType, item_name: str, is_new_conversation: bool):
    """
    事件生成策略：
    1. 新对话 + 合成导航 → conversation_start + crafting_context
    2. 识别到物品 → message + message_complete(crafting)
    3. 未识别到物品 → message + message_complete(text)
    4. 错误处理 → error事件
    """
    if is_new_conversation and message_type == MessageType.CRAFTING:
        yield conversation_start_event()
        yield crafting_context_event(target_item, shared_tree)

    # 流式消息内容
    async for chunk in generate_response_stream():
        yield message_event(chunk)

    # 消息完成事件
    yield message_complete_event(message_type, item_name)
    yield end_event()
```

---

## 🚀 快速开始指南

### 第一步：准备工作
1. 确保MySQL服务运行
2. 备份现有数据库
3. 检查.env配置文件

### 第二步：执行第一阶段
```bash
# 1. 创建迁移脚本
python scripts/migrations/002_add_message_type_and_item_name.py

# 2. 创建合成上下文表
python scripts/migrations/003_add_crafting_contexts_table.py

# 3. 验证数据库结构
python -c "from src.rag.database import init_db; init_db()"
```

### 第三步：验证基础功能
```bash
# 启动应用
python -m uvicorn src.rag.main:app --reload

# 测试现有功能是否正常
curl -X GET "http://localhost:8000/api/conversations"
```

### 第四步：分阶段实施
按照计划文档中的任务清单，逐个阶段完成实施。

---

## 📞 支持和反馈

如果在实施过程中遇到问题，请：
1. 检查核心逻辑实现点是否正确
2. 验证数据库迁移是否成功
3. 确认依赖注入配置是否正确
4. 测试向后兼容性

**记住**：保持现有功能的完整性是最高优先级！