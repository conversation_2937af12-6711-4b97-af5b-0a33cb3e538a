"""
日志配置模块

此模块提供统一的日志配置，确保整个应用中的日志格式和级别一致。
"""

import logging
import sys
from . import config

# 全局日志格式
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# 配置根日志器
logging.basicConfig(
    level=config.LOG_LEVEL,
    format=LOG_FORMAT,
    stream=sys.stdout
)

def get_logger(name):
    """
    获取指定名称的logger实例
    
    Args:
        name: logger名称，通常使用模块名称
        
    Returns:
        logging.Logger: 配置好的logger实例
    """
    logger = logging.getLogger(name)
    logger.setLevel(config.LOG_LEVEL)
    
    # 确保不会重复添加处理器
    if not logger.handlers:
        # 如果需要特殊处理，可以在这里添加
        pass
        
    return logger
