import logging
import os
import random
import re
import time
import requests
from bs4 import BeautifulSoup
import json
from urllib.parse import urljoin
from pathlib import Path
import traceback
from concurrent.futures import ThreadPoolExecutor, as_completed

NUM_WORKERS = 50
MAX_BBS_ID = 40000

PROXY_API_URL = "https://dps.kdlapi.com/api/getdps/?secret_id=o908aykc7vhhaftk9iz6&signature=5818amln1aur09moioq1sie91299nzlv&num={num}&pt=1&sep=1"
PROXY_USER = "d3758262232"
PROXY_PASS = "e6upmnib"

BBS_OUTPUT_FOLDER = './bbs'
LOG_FILE = 'crawl_bbs_log.txt'
REQUEST_TIMEOUT = 20
MAX_RETRIES = 2

USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) RAG-Data-Collector/1.0'
]

proxies = []

logging.basicConfig(filename=LOG_FILE, level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S')

def my_log(msg):
    print(msg)
    logging.info(msg)

def extract_thread_id(url):
    """Extract thread ID from URL using regex pattern"""
    match = re.search(r'thread-(\d+)', url)
    if match:
        return match.group(1)
    return None

def proxy_init():
    global proxies
    api_url = PROXY_API_URL.format(num=NUM_WORKERS)
    my_log(f"初始化代理 (数量: {NUM_WORKERS}) from {api_url}...")
    try:
        response = requests.get(api_url, timeout=30)
        response.raise_for_status()
        proxy_ips_text = response.text.strip()

        if not proxy_ips_text:
             raise ValueError("获取代理失败，API 返回空内容")

        proxy_list = [ip.strip() for ip in proxy_ips_text.split('\n') if ip.strip()]

        if len(proxy_list) < NUM_WORKERS:
            my_log(f"警告: 获取代理数量不足 {NUM_WORKERS} 个，实际获取 {len(proxy_list)} 个。可能会导致 Worker 失败。")
            if not proxy_list:
                 raise ValueError("获取代理失败，未获取到任何有效 IP")
        proxies_temp = [
             {
                 "http":  f"http://{PROXY_USER}:{PROXY_PASS}@{ip}/",
                 "https": f"http://{PROXY_USER}:{PROXY_PASS}@{ip}/"
             } for ip in proxy_list
        ]
        proxies = proxies_temp + [None] * (NUM_WORKERS - len(proxies_temp))

        my_log(f"成功初始化 {len(proxy_list)} 个代理。")
        return True
    except requests.exceptions.RequestException as e:
        my_log(f"代理初始化请求失败: {e}")
    except ValueError as e:
         my_log(f"代理初始化值错误: {e}")
    except Exception as e:
        my_log(f"代理初始化时发生未知错误: {e}")
        traceback.print_exc()
    proxies = [None] * NUM_WORKERS
    return False

def refresh_proxy(worker_id):
    global proxies
    if worker_id < 0 or worker_id >= len(proxies):
        my_log(f"错误: 尝试刷新无效的 Worker ID {worker_id} 的代理。")
        return False

    api_url = PROXY_API_URL.format(num=1)
    my_log(f"Worker {worker_id}: 正在刷新代理 from {api_url}...")
    try:
        response = requests.get(api_url, timeout=30)
        response.raise_for_status()
        proxy_ip = response.text.strip()

        if proxy_ip:
            new_proxy = {
                "http":  f"http://{PROXY_USER}:{PROXY_PASS}@{proxy_ip}/",
                "https": f"http://{PROXY_USER}:{PROXY_PASS}@{proxy_ip}/"
            }
            proxies[worker_id] = new_proxy
            my_log(f"Worker {worker_id}: 代理已更新为 {proxy_ip}")
            return True
        else:
            my_log(f"Worker {worker_id}: 获取新代理失败，返回空IP。保留旧代理 (如果存在)。")
            return False
    except requests.exceptions.RequestException as e:
        my_log(f"Worker {worker_id}: 刷新代理请求失败: {e}")
    except Exception as e:
        my_log(f"Worker {worker_id}: 刷新代理时出错: {e}")
        traceback.print_exc()

    my_log(f"Worker {worker_id}: 代理刷新失败，将继续使用旧代理 (如果存在).")
    return False

def clean_text(text):
    if text:
        text = text.replace('\xa0', ' ')
        text = re.sub(r'\s+', ' ', text)
        return text.strip()
    return ""

def process_bbs_thread(thread_id, session, proxy, worker_id):
    url = f"https://bbs.mcmod.cn/thread-{thread_id}-1-1.html"
    output_path = Path(BBS_OUTPUT_FOLDER) / f"thread{thread_id}.json"

    my_log(f"Worker {worker_id}: Fetching BBS Thread {thread_id}: {url} using proxy {proxy.get('http', 'N/A').split('@')[-1] if proxy else 'None'}")
    headers = {'User-Agent': random.choice(USER_AGENTS)}

    if not proxy:
        my_log(f"Worker {worker_id}: [错误] Thread {thread_id} - 无可用代理.")
        return "error"

    try:
        response = session.get(url, headers=headers, timeout=REQUEST_TIMEOUT, proxies=proxy)

        if response.status_code == 403:
            my_log(f"Worker {worker_id}: [403 Blocked] BBS Thread {thread_id}: {url}")
            return "blocked"
        if response.status_code == 404:
             my_log(f"Worker {worker_id}: [404 Not Found] BBS Thread {thread_id}: {url}")
             return "not_found"

        response.raise_for_status()

        response.encoding = response.apparent_encoding if response.apparent_encoding else 'utf-8'

        soup = BeautifulSoup(response.text, 'html.parser')
        if not soup:
            my_log(f"Worker {worker_id}: [错误] 无法解析 BBS HTML for Thread {thread_id}: {url}")
            return "error"

        # Try multiple selector patterns for more robust extraction
        title_tag = soup.select_one("h1.ts span#thread_subject")
        if not title_tag:
            title_tag = soup.select_one("h1.ts.bluelink span#thread_subject")
        if not title_tag:
            title_tag = soup.select_one("h1.ts")
        title = clean_text(title_tag.get_text()) if title_tag else f"标题未找到 (Thread {thread_id})"

        # Try both methods to extract content - first the t_fsz method (old code)
        content = ""
        replies = []
        t_fsz_divs = soup.find_all('div', class_='t_fsz')
        
        if t_fsz_divs and len(t_fsz_divs) > 0:
            content_td = t_fsz_divs[0].select_one("td.t_f")
            if content_td:
                for quote in content_td.select('div.quote'):
                    quote.decompose()
                for sig in content_td.select('div.signatures'):
                    sig.decompose()
                content = clean_text(content_td.get_text())
                
            for i in range(1, len(t_fsz_divs)):
                reply_td = t_fsz_divs[i].select_one("td.t_f")
                if reply_td:
                    for quote in reply_td.select('div.quote'):
                        quote.decompose()
                    for sig in reply_td.select('div.signatures'):
                        sig.decompose()
                    reply_content = clean_text(reply_td.get_text())
                    if reply_content:
                        replies.append(reply_content)
        
        # If the above method didn't work, fall back to the post container method
        if not content:
            post_contents = []
            post_containers = soup.find_all('div', id=lambda x: x and x.startswith('post_'))

            for container in post_containers:
                content_td = container.select_one("td.t_f")
                if content_td:
                    for quote in content_td.select('div.quote'):
                        quote.decompose()
                    for sig in content_td.select('div.signatures'):
                        sig.decompose()

                    post_text = clean_text(content_td.get_text())
                    if post_text:
                        post_contents.append(post_text)

            content = post_contents[0] if post_contents else ""
            replies = post_contents[1:] if len(post_contents) > 1 else []

        data = {
            "id": str(thread_id),
            "url": url,
            "title": title,
            "content": content,
            "replies": replies
        }

        output_path.parent.mkdir(parents=True, exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        my_log(f"Thread {thread_id} saved successfully to {output_path}")
        return True

    except requests.exceptions.Timeout:
        my_log(f"Worker {worker_id}: [Timeout] BBS Thread {thread_id}: {url}")
        return "timeout"
    except requests.exceptions.RequestException as e:
        my_log(f"Worker {worker_id}: [Request Error] BBS Thread {thread_id} ({url}): {e}")
        return "error"
    except Exception as e:
        my_log(f"Worker {worker_id}: [Processing Error] BBS Thread {thread_id} ({url}): {e}")
        traceback.print_exc()
        return "error"

def worker_task(worker_id):
    my_log(f"Worker {worker_id}: 开始执行...")
    processed_count = 0
    success_count = 0
    fail_count = 0
    skipped_count = 0
    not_found_count = 0

    current_thread_id = worker_id + 1

    session = requests.Session()
    adapter = requests.adapters.HTTPAdapter(pool_connections=5, pool_maxsize=5)
    session.mount('http://', adapter)
    session.mount('https://', adapter)

    while current_thread_id <= MAX_BBS_ID:
        processed_count += 1
        output_path = Path(BBS_OUTPUT_FOLDER) / f"thread{current_thread_id}.json"
        if output_path.exists():
            skipped_count += 1
            current_thread_id += NUM_WORKERS
            continue

        success = False
        for attempt in range(MAX_RETRIES):
            proxy = proxies[worker_id] if worker_id < len(proxies) else None

            if not proxy and attempt == 0:
                 my_log(f"Worker {worker_id}: [警告] Thread {current_thread_id} - 第 {attempt+1} 次尝试 - 无代理分配给此 Worker.")
                 break

            time.sleep(random.uniform(0.5, 1.5) * (attempt + 1))

            result = process_bbs_thread(current_thread_id, session, proxy, worker_id)

            if result is True:
                success = True
                success_count += 1
                break
            elif result == "not_found":
                 not_found_count += 1
                 success = True
                 break
            elif result == "blocked":
                my_log(f"Worker {worker_id}: Thread {current_thread_id} 尝试 {attempt + 1}/{MAX_RETRIES} - 被阻止，刷新代理...")
                refresh_proxy(worker_id)
            elif result == "timeout":
                my_log(f"Worker {worker_id}: Thread {current_thread_id} 尝试 {attempt + 1}/{MAX_RETRIES} - 超时，刷新代理...")
                refresh_proxy(worker_id)
            elif result == "error":
                my_log(f"Worker {worker_id}: Thread {current_thread_id} 尝试 {attempt + 1}/{MAX_RETRIES} - 发生错误，刷新代理...")
                refresh_proxy(worker_id)
            else:
                 my_log(f"Worker {worker_id}: Thread {current_thread_id} 尝试 {attempt + 1}/{MAX_RETRIES} - 处理失败 (未知原因)。")

        if not success:
            fail_count += 1
            my_log(f"Worker {worker_id}: [失败] Thread {current_thread_id} 处理最终失败 (尝试 {MAX_RETRIES} 次).")

        current_thread_id += NUM_WORKERS

        if processed_count % 10 == 0:
            my_log(f"Worker {worker_id}: 进度 - 处理: {processed_count}, 成功: {success_count}, 跳过: {skipped_count}, 未找到: {not_found_count}, 失败: {fail_count}")

    session.close()
    my_log(f"Worker {worker_id}: 完成. 总处理: {processed_count}, 成功: {success_count}, 跳过: {skipped_count}, 未找到: {not_found_count}, 失败: {fail_count}")
    return {"processed": processed_count, "success": success_count, "skipped": skipped_count, "not_found": not_found_count, "failed": fail_count}

if __name__ == "__main__":
    start_time_main = time.perf_counter()
    my_log("脚本开始执行 - BBS 爬虫...")

    os.makedirs(BBS_OUTPUT_FOLDER, exist_ok=True)
    my_log(f"输出目录: {BBS_OUTPUT_FOLDER}")

    if not proxy_init():
        my_log("代理初始化失败，无法继续。请检查 API URL/凭据和网络连接。")
        exit(1)
    if all(p is None for p in proxies):
         my_log("错误: 未能获取任何有效代理，退出。")
         exit(1)

    total_processed = 0
    total_success = 0
    total_skipped = 0
    total_not_found = 0
    total_failed = 0

    my_log(f"启动 {NUM_WORKERS} 个 Worker...")
    with ThreadPoolExecutor(max_workers=NUM_WORKERS) as executor:
        futures = {executor.submit(worker_task, wid): wid for wid in range(NUM_WORKERS)}

        for future in as_completed(futures):
            worker_id = futures[future]
            try:
                result = future.result()
                total_processed += result["processed"]
                total_success += result["success"]
                total_skipped += result["skipped"]
                total_not_found += result["not_found"]
                total_failed += result["failed"]
                my_log(f"Worker {worker_id} 完成. (部分统计: 成功={result['success']}, 跳过={result['skipped']}, 未找到={result['not_found']}, 失败={result['failed']})")
            except Exception as e:
                my_log(f"Worker {worker_id} 执行时发生严重异常: {e}")
                traceback.print_exc()

    elapsed_main = time.perf_counter() - start_time_main
    my_log("-" * 30)
    my_log(f"所有 Worker 执行完毕.")
    my_log(f"总耗时: {elapsed_main:.2f} 秒")
    my_log(f"目标范围: BBS Threads 1 - {MAX_BBS_ID}")
    total_accounted = total_success + total_skipped + total_not_found + total_failed
    my_log(f"总计尝试/跳过: {total_accounted}")
    my_log(f"  成功抓取: {total_success}")
    my_log(f"  已存在跳过: {total_skipped}")
    my_log(f"  未找到 (404): {total_not_found}")
    my_log(f"  最终失败: {total_failed}")
    my_log("-" * 30)