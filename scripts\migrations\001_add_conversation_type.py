"""
数据库迁移脚本：为对话表添加类型字段
Migration: Add conversation type field to conversations table

此脚本为现有的conversations表添加type字段，支持"普通对话"和"合成导航"两种类型。
"""

import sys
import os
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.rag.config import DATABASE_URL
from src.rag.database import ConversationType

def run_migration():
    """执行数据库迁移"""
    print("开始执行数据库迁移：添加对话类型字段...")

    # 创建数据库连接
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    with SessionLocal() as session:
        try:
            # 检查type字段是否已存在
            result = session.execute(text("""
                SELECT COUNT(*) as count
                FROM information_schema.columns
                WHERE table_name = 'conversations'
                AND column_name = 'type'
            """))

            column_exists = result.fetchone()[0] > 0

            if column_exists:
                print("type字段已存在，跳过迁移")
                return

            # 添加type字段
            print("添加type字段到conversations表...")
            session.execute(text("""
                ALTER TABLE conversations
                ADD COLUMN type ENUM('NORMAL', 'SYNTHETIC_NAVIGATION')
                NOT NULL DEFAULT 'NORMAL'
            """))

            # 为type字段添加索引
            print("为type字段添加索引...")
            session.execute(text("""
                CREATE INDEX idx_conversations_type ON conversations(type)
            """))

            # 提交更改
            session.commit()
            print("数据库迁移完成！")

            # 验证迁移结果
            print("验证迁移结果...")
            result = session.execute(text("""
                SELECT COUNT(*) as count
                FROM information_schema.columns
                WHERE table_name = 'conversations'
                AND column_name = 'type'
            """))

            if result.fetchone()[0] > 0:
                print("✓ type字段已成功添加")
            else:
                print("✗ type字段添加失败")
                return False

            # 检查索引是否创建成功
            result = session.execute(text("""
                SHOW INDEX FROM conversations WHERE Key_name = 'idx_conversations_type'
            """))

            if result.fetchone():
                print("✓ type字段索引已成功创建")
            else:
                print("✗ type字段索引创建失败")
                return False

            print("所有迁移步骤已成功完成！")
            return True

        except Exception as e:
            print(f"迁移过程中发生错误: {e}")
            session.rollback()
            return False

def rollback_migration():
    """回滚数据库迁移"""
    print("开始回滚数据库迁移：移除对话类型字段...")

    # 创建数据库连接
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    with SessionLocal() as session:
        try:
            # 检查type字段是否存在
            result = session.execute(text("""
                SELECT COUNT(*) as count
                FROM information_schema.columns
                WHERE table_name = 'conversations'
                AND column_name = 'type'
            """))

            column_exists = result.fetchone()[0] > 0

            if not column_exists:
                print("type字段不存在，无需回滚")
                return

            # 删除索引
            print("删除type字段索引...")
            session.execute(text("""
                DROP INDEX idx_conversations_type ON conversations
            """))

            # 删除type字段
            print("删除type字段...")
            session.execute(text("""
                ALTER TABLE conversations DROP COLUMN type
            """))

            # 提交更改
            session.commit()
            print("数据库迁移回滚完成！")
            return True

        except Exception as e:
            print(f"回滚过程中发生错误: {e}")
            session.rollback()
            return False

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="对话类型字段数据库迁移脚本")
    parser.add_argument("--rollback", action="store_true", help="回滚迁移")

    args = parser.parse_args()

    if args.rollback:
        success = rollback_migration()
    else:
        success = run_migration()

    if success:
        print("操作成功完成！")
        sys.exit(0)
    else:
        print("操作失败！")
        sys.exit(1)
