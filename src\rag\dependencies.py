"""
依赖注入模块

此模块提供FastAPI依赖注入函数，用于创建和提供应用程序所需的各种组件实例。
这些函数使用FastAPI的Depends机制，实现了组件的懒加载和依赖关系管理。
"""

import os

from functools import lru_cache
from fastapi import Depends, Request, HTTPException, Security, status
from fastapi.security import OAuth2PasswordBearer
from fastapi.security.api_key import APIKeyHeader
from sqlalchemy.orm import Session
from langchain_openai import ChatOpenAI
from langchain.retrievers import EnsembleRetriever
from langchain_community.document_transformers import EmbeddingsRedundantFilter
from langchain_core.prompts import ChatPromptTemplate

from .database import get_db, User
from .services.conversation_service import ConversationService
from .services.feedback_service import FeedbackService
from .services.auth_service import AuthService
from .services.knowledge_service import KnowledgeService
from .services.document_service import DocumentService
from .rag_pipeline.pipeline import RAGPipeline
from .rag_pipeline.intent_processing import IntentClassifier
from .crafting import ItemEntityExtractor, CraftingTreeGenerator, CraftingRAGPipeline

# OAuth2密码Bearer令牌
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/auth/token")

DUMP_KEY_NAME = "X-Dump-Key"
dump_key_header = APIKeyHeader(name=DUMP_KEY_NAME, auto_error=True)


def get_dump_key(key: str = Security(dump_key_header)):
  if key == os.getenv("DUMP_KEY", None):
    return key
  raise HTTPException(status_code=403, detail="Couldn't validate dump key.")


def get_rag_llm(request: Request) -> ChatOpenAI:
    """
    获取RAG系统使用的LLM客户端

    Args:
        request: FastAPI请求对象，用于访问应用状态

    Returns:
        ChatOpenAI: RAG系统使用的LLM客户端实例
    """
    return request.app.state.llm["rag"]


def get_title_llm(request: Request) -> ChatOpenAI:
    """
    获取标题生成使用的LLM客户端

    Args:
        request: FastAPI请求对象，用于访问应用状态

    Returns:
        ChatOpenAI: 标题生成使用的LLM客户端实例
    """
    return request.app.state.llm["title"]


def get_redundant_filter(request: Request) -> EmbeddingsRedundantFilter:
    """
    获取冗余过滤器

    Args:
        request: FastAPI请求对象，用于访问应用状态

    Returns:
        EmbeddingsRedundantFilter: 冗余过滤器实例
    """
    return request.app.state.filter


def get_rag_prompt(request: Request) -> ChatPromptTemplate:
    """
    获取RAG提示模板

    Args:
        request: FastAPI请求对象，用于访问应用状态

    Returns:
        ChatPromptTemplate: RAG提示模板实例
    """
    return request.app.state.prompt


def get_intent_classifier(request: Request) -> IntentClassifier:
    """
    获取意图分类器

    Args:
        request: FastAPI请求对象，用于访问应用状态

    Returns:
        IntentClassifier: 意图分类器实例
    """
    return request.app.state.intent_classifier


def get_rag_pipeline(request: Request) -> RAGPipeline:
    """
    获取RAG Pipeline

    Args:
        request: FastAPI请求对象，用于访问应用状态

    Returns:
        RAGPipeline: RAG Pipeline实例
    """
    return request.app.state.rag_pipeline


def get_item_entity_extractor(request: Request) -> ItemEntityExtractor:
    """
    获取物品实体识别器

    Args:
        request: FastAPI请求对象，用于访问应用状态

    Returns:
        ItemEntityExtractor: 物品实体识别器实例
    """
    return request.app.state.item_entity_extractor


def get_crafting_tree_generator(request: Request) -> CraftingTreeGenerator:
    """
    获取合成树生成器

    Args:
        request: FastAPI请求对象，用于访问应用状态

    Returns:
        CraftingTreeGenerator: 合成树生成器实例
    """
    return request.app.state.crafting_tree_generator


def get_crafting_rag_pipeline(request: Request) -> CraftingRAGPipeline:
    """
    获取合成导航RAG Pipeline

    Args:
        request: FastAPI请求对象，用于访问应用状态

    Returns:
        CraftingRAGPipeline: 合成导航RAG Pipeline实例
    """
    return request.app.state.crafting_rag_pipeline


def get_document_service(request: Request) -> DocumentService:
    """
    获取文档服务

    Args:
        request: FastAPI请求对象，用于访问应用状态

    Returns:
        DocumentService: 文档服务实例
    """
    return DocumentService(
        vector_db=request.app.state.vector_db,
        embedding=request.app.state.embedding
    )


def get_conversation_service(
    db: Session = Depends(get_db),
    rag_pipeline: RAGPipeline = Depends(get_rag_pipeline),
    crafting_rag_pipeline: CraftingRAGPipeline = Depends(get_crafting_rag_pipeline),
) -> ConversationService:
    """
    创建并提供ConversationService实例

    Args:
        db: 数据库会话
        rag_pipeline: RAG Pipeline实例
        crafting_rag_pipeline: 合成导航RAG Pipeline实例

    Returns:
        ConversationService: 对话服务实例
    """
    return ConversationService(
        db=db,
        rag_pipeline=rag_pipeline,
        crafting_rag_pipeline=crafting_rag_pipeline,
    )


def get_feedback_service(
    db: Session = Depends(get_db),
) -> FeedbackService:
    """
    创建并提供FeedbackService实例

    Args:
        db: 数据库会话

    Returns:
        FeedbackService: 反馈服务实例
    """
    return FeedbackService(db=db)


def get_auth_service(
    db: Session = Depends(get_db),
) -> AuthService:
    """
    创建并提供AuthService实例

    Args:
        db: 数据库会话

    Returns:
        AuthService: 认证服务实例
    """
    return AuthService(db=db)


def get_knowledge_service(
    db: Session = Depends(get_db),
) -> KnowledgeService:
    """
    创建并提供KnowledgeService实例

    Args:
        db: 数据库会话

    Returns:
        KnowledgeService: 知识库服务实例
    """
    return KnowledgeService(db=db)


# 验证当前用户的依赖项
async def get_current_user(
    token: str = Depends(oauth2_scheme),
    auth_service: AuthService = Depends(get_auth_service)
) -> User:
    """
    验证当前用户的依赖函数

    Args:
        token: JWT令牌
        auth_service: 认证服务实例

    Returns:
        User: 当前用户对象

    Raises:
        HTTPException: 如果令牌无效或用户不存在
    """
    # 使用服务层验证令牌并获取用户
    user = await auth_service.get_user_by_token(token)

    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的身份验证凭据或用户不存在",
            headers={"WWW-Authenticate": "Bearer"},
        )

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户已被禁用",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return user


# 检查管理员权限的依赖项
async def is_admin(
    current_user: User = Depends(get_current_user),
    auth_service: AuthService = Depends(get_auth_service)
) -> User:
    """
    验证用户是否为管理员的依赖函数

    Args:
        current_user: 当前用户对象
        auth_service: 认证服务实例

    Returns:
        User: 当前用户对象（如果是管理员）

    Raises:
        HTTPException: 如果用户不是管理员
    """
    # 使用服务层验证管理员权限
    is_admin_user = await auth_service.validate_admin(current_user)

    if not is_admin_user:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要管理员权限",
        )

    return current_user