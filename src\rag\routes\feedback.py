"""
反馈 API 路由模块

此模块定义了与用户反馈相关的 API 端点，
包括提交反馈、获取反馈列表（用户或管理员）以及更新反馈状态（管理员）。
"""
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime
from ..database import User, FeedbackType, FeedbackStatus
from ..dependencies import get_feedback_service, get_current_user, is_admin
from ..services.feedback_service import FeedbackService

# 创建路由
router = APIRouter(prefix="/feedback", tags=["feedback"])

# 请求和响应模型
class FeedbackCreate(BaseModel):
    type: FeedbackType
    content: str

class FeedbackUpdate(BaseModel):
    status: Optional[FeedbackStatus] = None
    admin_response: Optional[str] = None

class FeedbackResponse(BaseModel):
    id: int
    type: str
    content: str
    status: str
    admin_response: Optional[str]
    created_at: datetime
    updated_at: datetime
    user_id: int
    username: str

    class Config:
        from_attributes = True

class BasicResponse(BaseModel):
    success: bool
    message: str
    feedback_id: Optional[int] = None

# 提交反馈
@router.post("", response_model=BasicResponse)
async def create_feedback(
    feedback_data: FeedbackCreate,
    current_user: User = Depends(get_current_user),
    service: FeedbackService = Depends(get_feedback_service)
):
    # 使用服务层创建反馈
    new_feedback = await service.create_feedback(
        user_id=current_user.id,
        feedback_type=feedback_data.type,
        content=feedback_data.content
    )

    return {
        "success": True,
        "message": "反馈提交成功",
        "feedback_id": new_feedback.id
    }

# 获取当前用户的反馈历史
@router.get("/my", response_model=List[FeedbackResponse])
async def get_my_feedback(
    current_user: User = Depends(get_current_user),
    service: FeedbackService = Depends(get_feedback_service)
):
    # 使用服务层获取用户反馈
    return await service.get_user_feedback(user_id=current_user.id)

# 获取所有反馈（仅限管理员）
@router.get("/all", response_model=List[FeedbackResponse])
async def get_all_feedback(
    status: Optional[FeedbackStatus] = None,
    current_user: User = Depends(is_admin),  # 验证管理员权限
    service: FeedbackService = Depends(get_feedback_service)
):
    # 使用服务层获取所有反馈
    return await service.get_all_feedback(status_filter=status)

# 更新反馈状态（仅限管理员）
@router.put("/{feedback_id}", response_model=BasicResponse)
async def update_feedback(
    feedback_id: int,
    feedback_update: FeedbackUpdate,
    current_user: User = Depends(is_admin),  # 验证管理员权限
    service: FeedbackService = Depends(get_feedback_service)
):
    # 使用服务层更新反馈
    updated_feedback = await service.update_feedback(
        feedback_id=feedback_id,
        status=feedback_update.status,
        admin_response=feedback_update.admin_response
    )

    return {
        "success": True,
        "message": "反馈更新成功",
        "feedback_id": updated_feedback.id
    }