"""
来源标记检索器

此模块提供了一个检索器包装器，用于为检索结果添加来源标记。
"""

from langchain_core.runnables.base import Runnable

class SourcedRetriever(Runnable):
    """
    来源标记检索器，为检索结果添加来源标记的包装器

    用于跟踪文档的检索来源，在多种检索策略中标记文档来源。
    """

    def __init__(self, retriever, source_name, intent=None):
        """
        初始化来源标记检索器

        Args:
            retriever: 被包装的检索器
            source_name: 来源名称
            intent: 检索意图，用于标记文档是由哪个意图检索出来的
        """
        self.retriever = retriever
        self.source_name = source_name
        self.intent = intent

    def invoke(self, query, config=None, **kwargs):
        """
        执行检索并添加来源标记

        Args:
            query: 用户查询
            config: 配置参数
            **kwargs: 其他参数

        Returns:
            list: 添加了来源标记的文档列表
        """
        docs = self.retriever.invoke(query, config=config, **kwargs)
        for doc in docs:
            # 添加检索器来源
            if "retriever_source" in doc.metadata and isinstance(doc.metadata["retriever_source"], list):
                if self.source_name not in doc.metadata["retriever_source"]:
                    doc.metadata["retriever_source"].append(self.source_name)
            elif "retriever_source" in doc.metadata:
                doc.metadata["retriever_source"] = [doc.metadata["retriever_source"], self.source_name]
            else:
                doc.metadata["retriever_source"] = [self.source_name]

            # 添加检索意图
            if self.intent:
                doc.metadata["retrieved_by_intent"] = self.intent
            elif "retrieved_by_intent" not in doc.metadata:
                doc.metadata["retrieved_by_intent"] = "未知意图"
        return docs

    # 移除了 get_relevant_documents 方法，因为 invoke 方法已提供核心功能，
    # 且 RAGPipeline 优先使用 invoke/ainvoke。
    # 来源标记逻辑已在 invoke 方法中处理。

    def with_config(self, **kwargs):
        """传递配置到底层检索器"""
        self.retriever = self.retriever.with_config(**kwargs) if hasattr(self.retriever, 'with_config') else self.retriever
        return self

    def with_retry(self, **kwargs):
        """传递重试配置到底层检索器"""
        if hasattr(self.retriever, 'with_retry'):
            self.retriever = self.retriever.with_retry(**kwargs)
        return self

    def with_timeout(self, timeout, **kwargs):
        """传递超时配置到底层检索器"""
        if hasattr(self.retriever, 'with_timeout'):
            self.retriever = self.retriever.with_timeout(timeout, **kwargs)
        return self
