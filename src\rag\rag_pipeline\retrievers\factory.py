"""
检索器工厂模块

此模块提供了创建和组合检索器的工厂类。
"""

from langchain_core.language_models.chat_models import BaseChatModel # 导入 BaseChatModel
from langchain_chroma import Chroma
from typing import List, Optional
from langchain_core.retrievers import BaseRetriever

import logging 
logger = logging.getLogger(__name__)

from .metadata_matching import ItemMetadataRetriever, ModeMetadataRetriever, WikiMetadataRetriever, TUTORIALMetadataRetriever
from .sourced import SourcedRetriever
from .specialized_retrievers import GenericDenseRetriever

class RetrieverFactory:
    """
    检索器工厂类，用于创建和组合检索器
    """

    def __init__(
        self,
        vector_db: Chroma,
        metadata_llm: BaseChatModel,
        k: int
    ):
        """
        初始化 RetrieverFactory。

        参数:
            vector_db: Chroma 向量数据库实例。
            metadata_llm: 用于元数据匹配的语言模型。
            k: 检索器返回的最大文档数量。
        """
        self.vector_db = vector_db
        self.metadata_llm = metadata_llm
        self.k = k
        logger.info(f"RetrieverFactory 初始化完成，k={self.k}")

    def get_retrievers_by_intent(
        self,
        intents: List[str]
    ) -> List[SourcedRetriever]:
        """
        根据提供的意图获取 SourcedRetriever 实例列表。
        使用实例属性 (vector_db, metadata_llm, k) 创建检索器。

        Args:
            intents: 意图标签列表。

        Returns:
            List[SourcedRetriever]: 对应有效意图的 SourcedRetriever 实例列表。
        """
        retrievers: List[SourcedRetriever] = []
        processed_intents = set()

        for intent in intents:
            if intent in processed_intents or intent == "chit_chat":
                logger.debug(f"跳过意图: {intent} (已处理或为闲聊)")
                continue

            base_retriever: Optional[BaseRetriever] = None
            source_name: Optional[str] = None

            logger.debug(f"处理意图: {intent}")

            if intent == "find_item":
                base_retriever = ItemMetadataRetriever(vector_db=self.vector_db, llm=self.metadata_llm, k=self.k)
                source_name = "metadata_query_item"
            elif intent == "find_mod_info":
                base_retriever = ModeMetadataRetriever(vector_db=self.vector_db, llm=self.metadata_llm, k=self.k)
                source_name = "metadata_query_mod"
            elif intent == "find_tutorial":
                base_retriever = TUTORIALMetadataRetriever(vector_db=self.vector_db, llm=self.metadata_llm, k=self.k)
                source_name = "metadata_query_tutorial"
            elif intent == "find_vanilla_wiki":
                base_retriever = WikiMetadataRetriever(vector_db=self.vector_db, llm=self.metadata_llm, k=self.k)
                source_name = "metadata_query_vanilla_wiki"
            elif intent == "comprehensive_answer":
                base_retriever = GenericDenseRetriever(vector_db=self.vector_db, k=self.k)
                source_name = "dense_comprehensive"
            elif intent == "find_crafting_recipe":
                #  logger.warning(f"意图 '{intent}' 未被特别处理，使用通用密集检索器作为回退。")
                 base_retriever = GenericDenseRetriever(vector_db=self.vector_db, k=self.k)
                 source_name = "dense_fallback_crafting_recipe"
            else:
                # logger.warning(f"遇到未知意图 '{intent}'。使用通用密集检索器作为回退。")
                base_retriever = GenericDenseRetriever(vector_db=self.vector_db, k=self.k)
                source_name = f"dense_fallback_unknown_{intent}"

            if base_retriever and source_name:
                logger.debug(f"意图 '{intent}' 映射到检索器 '{source_name}'")
                sourced_retriever = SourcedRetriever(base_retriever, source_name, intent=intent)
                retrievers.append(sourced_retriever)
                processed_intents.add(intent)
            else:
                 logger.warning(f"意图 '{intent}' 未能成功创建检索器实例。")

        has_non_chit_chat_intent = any(i != "chit_chat" for i in intents)

        if not retrievers and intents and has_non_chit_chat_intent:
            logger.warning("未能为提供的意图成功创建任何特定检索器。返回通用密集检索器作为回退。")
            fallback_retriever = GenericDenseRetriever(vector_db=self.vector_db, k=self.k)
            fallback_intent = next((i for i in intents if i != "chit_chat"), "未知意图")
            sourced_fallback = SourcedRetriever(fallback_retriever, "dense_fallback_default", intent=fallback_intent)
            retrievers.append(sourced_fallback)
        elif not intents:
            logger.warning("未提供意图。返回通用密集检索器作为默认。")
            default_retriever = GenericDenseRetriever(vector_db=self.vector_db, k=self.k)
            sourced_default = SourcedRetriever(default_retriever, "dense_fallback_default", intent="综合查询")
            retrievers.append(sourced_default)

        selected_retriever_names = [r.source_name for r in retrievers]

        logger.info(f"RetrieverFactory: 输入意图 {intents}。最终选择的检索器名称: {selected_retriever_names}")

        return retrievers
