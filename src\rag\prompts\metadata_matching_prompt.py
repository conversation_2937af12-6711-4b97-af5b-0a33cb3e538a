"""
元数据匹配的prompt模板

此模块定义了用于元数据匹配检索器的prompt模板，用于从查询中提取实体。
"""

METADATA_PROMPT_TEMPLATE = """
你是一个专业的Minecraft模组专家，请从以下查询中精确提取模组名称（mod_names）和物品名称（item_names）。

**重要定义：**
*   **模组名称 (mod_names):** 指添加新内容到Minecraft的模组的完整名称，包括中文名、英文名或常用简称
*   **物品名称 (item_names):** 指Minecraft游戏内的具体实体，包括：物品/方块、群系/群落、世界/维度、生物/实体、附魔/魔咒、BUFF/DEBUFF、多方块结构、游戏机制等

**提取规约：**
1. **完整性原则**：提取完整的名称，不要截断或省略关键词
2. **多语言处理**：优先提取中文名，如果中英文都出现，则都提取
3. **上下文判断**：根据查询语境判断词语是否确实指代模组或物品
4. **精确匹配**：只提取明确指代模组或物品的词语，避免泛化
5. **去重原则**：相同含义的不同表达只保留一个最准确的
6. **保持原文**：尽量保持原查询中的表达方式和大小写

**查询:** {query}

**提取示例：**

```json
// 查询： "我的世界怎么获得潮涌核心"
// 分析：潮涌核心是原版物品
{{
    "mod_names": [],
    "item_names": ["潮涌核心"]
}}

// 查询： "工业2的采矿机怎么用？"
// 分析：工业2是模组名，采矿机是该模组的物品
{{
    "mod_names": ["工业2"],
    "item_names": ["采矿机"]
}}

// 查询： "介绍一下能源转换模组 Energy Conversion mod 功能特性"
// 分析：包含中英文模组名，都需要提取
{{
    "mod_names": ["能源转换", "Energy Conversion"],
    "item_names": []
}}

// 查询： "暮色森林有什么好玩的？"
// 分析：暮色森林是模组名
{{
    "mod_names": ["暮色森林"],
    "item_names": []
}}

// 查询： "热力膨胀的能量导管和红石能量导管有什么区别"
// 分析：热力膨胀是模组，能量导管和红石能量导管是物品
{{
    "mod_names": ["热力膨胀"],
    "item_names": ["能量导管", "红石能量导管"]
}}

// 查询： "Applied Energistics 2 应用能源2的ME系统怎么搭建"
// 分析：包含英文和中文模组名，ME系统是该模组的核心概念
{{
    "mod_names": ["Applied Energistics 2", "应用能源2"],
    "item_names": ["ME系统"]
}}

// 查询： "村民交易有哪些好东西"
// 分析：村民是生物实体，交易是游戏机制
{{
    "mod_names": [],
    "item_names": ["村民", "交易"]
}}

// 查询： "怎么做一个自动化农场"
// 分析：自动化农场是概念性描述，不是具体物品名
{{
    "mod_names": [],
    "item_names": []
}}

// 查询： "JEI怎么查看合成表"
// 分析：JEI是模组名，合成表是游戏功能
{{
    "mod_names": ["JEI"],
    "item_names": ["合成表"]
}}

// 查询： "Tinkers' Construct 匠魂模组的工具锻造台怎么用"
// 分析：包含英文和中文模组名，工具锻造台是该模组的物品
{{
    "mod_names": ["Tinkers' Construct", "匠魂"],
    "item_names": ["工具锻造台"]
}}

// 查询： "BuildCraft建筑模组的石油怎么开采"
// 分析：BuildCraft和建筑是同一个模组的不同表达，石油是该模组的资源
{{
    "mod_names": ["BuildCraft", "建筑"],
    "item_names": ["石油"]
}}

// 查询： "Mekanism通用机械的数字采矿机配置教程"
// 分析：Mekanism和通用机械是同一模组，数字采矿机是该模组物品
{{
    "mod_names": ["Mekanism", "通用机械"],
    "item_names": ["数字采矿机"]
}}

// 查询： "怎么在创造模式下飞行"
// 分析：创造模式和飞行都是原版游戏机制
{{
    "mod_names": [],
    "item_names": ["创造模式", "飞行"]
}}

// 查询： "RF能量系统是什么"
// 分析：RF是能量系统的简称，属于游戏机制概念
{{
    "mod_names": [],
    "item_names": ["RF", "能量系统"]
}}

// 查询： "神秘时代4的研究台怎么用"
// 分析：神秘时代4是模组名（包含版本号），研究台是该模组物品
{{
    "mod_names": ["神秘时代4"],
    "item_names": ["研究台"]
}}
```
"""

WIKI_METADATA_PROMPT_TEMPLATE = """
请从以下Minecraft相关的查询中提取可能的wiki关键词（wiki_keywords）。
**重要定义：**
*   **Wiki关键词 (wiki_keywords):** 指的是与Minecraft相关的、可能出现在Wiki中的关键词。

如果无法确定，请返回空列表。请严格以JSON格式返回，包含一个字段：wiki_keywords。

**查询:** {query}

**JSON格式和示例:**

```json
// 查询： "我的世界怎么获得潮涌核心"
// JSON 输出：
{{
    "wiki_keywords": ["潮涌核心"]
}}

// 查询： "给我看看钻石剑"
// JSON 输出：
{{
    "wiki_keywords": ["钻石剑"]
}}

// 查询： "暮色森林有什么好玩的？"
// JSON 输出：
{{
    "wiki_keywords": ["暮色森林"]
}}

// 查询： "我想了解一下村民交易"
// JSON 输出：
{{
    "wiki_keywords": ["村民", "交易"]
}}
```
"""

TUTORIAL_METADATA_PROMPT_TEMPLATE = """
请从以下Minecraft相关的查询中提取可能的教程关键词（tutorial_keywords）。
**重要定义：**
*   **教程关键词 (tutorial_keywords):** 指的是与Minecraft相关的、可能出现在教程中的关键词。

如果无法确定，请返回空列表。请严格以JSON格式返回，包含一个字段：tutorial_keywords。

**查询:** {query}

**JSON格式和示例:**

```json
// 查询： "我的世界怎么获得潮涌核心"
// JSON 输出：
{{
    "tutorial_keywords": ["潮涌核心"]
}}

// 查询： "暮色森林有什么好玩的？"
// JSON 输出：
{{
    "tutorial_keywords": ["暮色森林"]
}}

// 查询： "我想了解一下村民交易"
// JSON 输出：
{{
    "tutorial_keywords": ["村民", "交易"]
}}
```
"""
