# src/rag/prompts/unified_rag_prompt.py
"""
该模块定义了统一的 RAG 提示模板以及一个用于格式化该模板的辅助函数。
此提示旨在处理来自用户的多个意图以及从不同检索器聚合的上下文信息。
"""

from typing import List

UNIFIED_RAG_PROMPT_TEMPLATE = """你是一个专业的Minecraft模组专家，请根据用户问题和相关上下文信息，提供一个清晰、准确且全面的回答。

用户问题: "{original_query}"

用户意图包括: {list_of_intents_as_string}

相关上下文信息:
{aggregated_context_string}

回答要求：
1. **直接回答用户问题**：围绕用户的具体问题展开，避免过度扩展
2. **结构清晰**：使用清晰的结构组织信息，可以在必要的时候使用markdown表格等
3. **聚焦相关性**：只包含与用户问题直接相关的信息，过滤掉边缘内容，要包含用户的所有意图，但只补充必要的额外内容

如果上下文中没有足够信息回答问题，请直接说明你没有足够的信息回答那部分问题。
请使用中文 Markdown 格式回答，并在适当位置注明引用的来源链接，如果多处引用相同来源，请只在最后给出一次链接。

{intent_specific_instructions}
"""

def format_unified_rag_prompt(
    original_query: str,
    intents: List[str],
    aggregated_context: str
) -> str:
    """
    使用给定的原始查询、识别出的意图列表和聚合的上下文字符串来格式化统一 RAG 提示模板。

    参数:
        original_query: 用户的原始查询字符串。
        intents: 由意图分类器识别出的意图字符串（标识符）列表。
                 例如: ["find_item", "find_tutorial"]
        aggregated_context: 一个包含所有从特化检索器聚合而来的相关上下文信息的字符串，
                            可能带有来源标记。

    返回:
        一个格式化后的字符串，可以直接发送给大语言模型 (LLM)。
    """
    list_of_intents_as_string: str

    if not intents or ("chit_chat" in intents and len(intents) == 1): # 处理闲聊或意图列表为空的情况
        # 对于闲聊意图，可能根本不会使用此完整提示，或者会使用一个更简单的提示。
        # 但如果确实使用此提示，意图描述应保持简洁。
        if "chit_chat" in intents:
            list_of_intents_as_string = "进行一般性的闲聊。"
        else: # 空的意图列表
            list_of_intents_as_string = "用户意图不明确或需要综合理解。"
    else:
        # 如果存在其他意图，则过滤掉闲聊意图
        active_intents = [intent for intent in intents if intent != "chit_chat"]
        # 此处不再需要 if not active_intents 检查，因为如果 active_intents 为空，
        # 意味着原始 intents 为空或仅包含 chit_chat，这些情况已在上面的 if 块中处理。

        # 尝试为意图创建一个更自然的语言描述字符串。
        # 这是一个简化的启发式方法，有很大的改进空间。
        # 它假设 `original_query` 可以作为意图的通用主题。
        # 一个更健壮的解决方案会涉及将提取的实体与特定意图相关联。

        intent_phrases = []
        # 为演示目的，使用基于查询的简化主题。
        # 在实际场景中，与每个意图相关的特定实体会更好。
        subject_placeholder = original_query
        for intent in active_intents:
            if intent == "find_item":
                intent_phrases.append(f"查询关于 {subject_placeholder} 的物品信息")
            elif intent == "find_tutorial":
                intent_phrases.append(f"查找关于 {subject_placeholder} 的教程")
            elif intent == "find_mod_info":
                intent_phrases.append(f"查找关于 {subject_placeholder} 的模组信息")
            elif intent == "find_crafting_recipe":
                intent_phrases.append(f"查找 {subject_placeholder} 的合成配方")
            elif intent == "find_vanilla_wiki":
                intent_phrases.append(f"从Minecraft Wiki获取关于 {subject_placeholder} 的信息")
            elif intent == "comprehensive_answer":
                intent_phrases.append(f"对 {subject_placeholder} 提供一个综合性的解答")
            else:
                # 未知或新意图的备用处理方式
                intent_phrases.append(f"处理与 {intent} 相关的请求，特别是关于 {subject_placeholder}")

        # 此处不再需要 if not intent_phrases 检查，
        # 因为如果 active_intents 非空，intent_phrases 也必然非空（至少会进入 fallback）。
            # 根据 intent_phrases 的数量格式化字符串
            if len(intent_phrases) == 1:
                list_of_intents_as_string = intent_phrases[0] + "。"
            elif len(intent_phrases) == 2:
                # 格式: "意图A，并且意图B。" (保持 .lower() 行为)
                list_of_intents_as_string = f"{intent_phrases[0]}，并且{intent_phrases[1].lower()}。"
            else: # len(intent_phrases) > 2 (因为 intent_phrases 不会为空，且已处理 len=1 和 len=2)
                # 格式: "意图A、意图B，并且意图C。" (保持 .lower() 行为)
                list_of_intents_as_string = f"{'、'.join(intent_phrases[:-1])}，并且{intent_phrases[-1].lower()}。"

    # 添加意图特定的指令
    intent_specific_instructions = generate_intent_specific_instructions(intents, original_query)

    return UNIFIED_RAG_PROMPT_TEMPLATE.format(
        original_query=original_query,
        list_of_intents_as_string=list_of_intents_as_string,
        aggregated_context_string=aggregated_context,
        intent_specific_instructions=intent_specific_instructions
    )

def generate_intent_specific_instructions(intents: List[str], query: str) -> str:
    """
    根据识别出的意图生成简洁的特定指令。

    参数:
        intents: 意图列表
        query: 原始查询

    返回:
        特定意图的指令字符串
    """
    if not intents or ("chit_chat" in intents and len(intents) == 1):
        return ""

    # 过滤掉闲聊意图
    active_intents = [intent for intent in intents if intent != "chit_chat"]
    if not active_intents:
        return ""

    instructions = []

    # 检查是否只有原版内容
    only_vanilla = "find_vanilla_wiki" in active_intents and "find_mod_info" not in active_intents
    both_vanilla_and_mod = "find_vanilla_wiki" in active_intents and "find_mod_info" in active_intents

    # 1. 原版内容优先，但允许模组内容作为补充
    if only_vanilla:
        instructions.append("请优先提供原版Minecraft的信息。如果原版中没有相关内容，可以提供模组信息作为补充，但请明确标注这是模组内容。")
    elif both_vanilla_and_mod:
        instructions.append("请同时搜索原版和模组内容。如果两者都有相关信息，请分别说明哪些是原版内容，哪些是模组内容。")

    if "find_tutorial" in active_intents:
        instructions.append("请提供清晰的步骤说明，使用有序列表格式（1. 2. 3.），确保步骤易于理解和执行。如果有多种方法，请分别列出。")

    # 3. 物品类需要给出物品信息
    if "find_item" in active_intents:
        instructions.append("对于物品信息，请包括其基本属性、用途和获取方式。如果是工具或武器，请说明其耐久、伤害或特殊效果。")

    if "find_crafting_recipe" in active_intents:
        instructions.append("对于合成配方，请明确列出所需材料及其数量，并简要描述合成方法（工作台、熔炉等）。如果可能，用文字描述合成表格布局。")

    # 5. 模组信息需要明确指出
    if "find_mod_info" in active_intents:
        instructions.append("请明确指出哪些内容来自模组，并注明模组名称。如果涉及多个模组，请分别说明各个模组的相关内容。")

    # 6. 综合性回答需要全面且有条理
    if "comprehensive_answer" in active_intents:
        instructions.append("请提供一个全面且有条理的回答，涵盖问题的各个方面。使用小标题组织不同部分，确保逻辑清晰，并在适当的地方提供示例。")

    # 组合多个意图的特殊指令
    if len(active_intents) > 1:
        instructions.append("由于问题涉及多个方面，请确保回答涵盖所有相关意图，并使用适当的结构组织不同部分的内容，但仍需保持回答简洁聚焦核心内容。")

    # 格式化最终指令
    if instructions:
        return "特别说明：\n" + "\n".join(f"- {instr}" for instr in instructions)
    else:
        return ""
