"""
认证服务模块

此模块提供与用户认证相关的核心业务逻辑服务。
它封装了用户注册、登录和令牌验证等功能，以及密码处理和JWT令牌处理。
"""

from sqlalchemy.orm import Session
from fastapi import HTTPException, status
from typing import Optional, Dict, Any
from passlib.context import CryptContext
import jwt
from datetime import datetime, timedelta, timezone
import secrets
import string

from ..database import User, AdminVerification, UserRole
from .. import config


class AuthService:
    """
    认证服务类，提供用户认证和授权的核心业务逻辑
    """

    # 加载环境变量

    # 密码哈希工具
    _pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

    # JWT配置
    _SECRET_KEY = config.SECRET_KEY
    _ALGORITHM = "HS256"
    _ACCESS_TOKEN_EXPIRE_MINUTES = config.ACCESS_TOKEN_EXPIRE_MINUTES

    def __init__(self, db: Session):
        """
        初始化认证服务

        Args:
            db: 数据库会话
        """
        self.db = db

    # 密码处理私有方法
    @classmethod
    def _verify_password(cls, plain_password, hashed_password):
        """
        验证密码

        Args:
            plain_password: 明文密码
            hashed_password: 哈希密码

        Returns:
            bool: 密码是否匹配
        """
        return cls._pwd_context.verify(plain_password, hashed_password)

    @classmethod
    def _get_password_hash(cls, password):
        """
        获取密码哈希

        Args:
            password: 明文密码

        Returns:
            str: 哈希密码
        """
        return cls._pwd_context.hash(password)

    # JWT Token处理私有方法
    @classmethod
    def _create_access_token(cls, data: dict):
        """
        创建访问令牌

        Args:
            data: 要编码的数据

        Returns:
            str: JWT令牌
        """
        to_encode = data.copy()
        if "sub" in to_encode and to_encode["sub"] is not None:
            to_encode["sub"] = str(to_encode["sub"])  # 确保 sub 是字符串
        expire = datetime.now(timezone.utc) + timedelta(minutes=cls._ACCESS_TOKEN_EXPIRE_MINUTES)
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, cls._SECRET_KEY, algorithm=cls._ALGORITHM)
        return encoded_jwt

    @classmethod
    def _verify_token(cls, token: str):
        """
        验证令牌

        Args:
            token: JWT令牌

        Returns:
            int: 用户ID，如果令牌无效则为None
        """
        try:
            payload = jwt.decode(token, cls._SECRET_KEY, algorithms=[cls._ALGORITHM])
            user_id: int = payload.get("sub")
            if user_id is None:
                return None
            return user_id
        except jwt.PyJWTError as e:
            print(f"JWT 解码错误: {e.__class__.__name__} - {e}")  # 打印错误类型和消息
            return None

    async def register_user(self, username: str, email: str, password: str) -> Dict[str, Any]:
        """
        注册新用户

        Args:
            username: 用户名
            email: 电子邮件
            password: 密码

        Returns:
            Dict[str, Any]: 包含成功状态、消息、令牌和用户信息的字典
        """
        # 检查用户名是否已存在
        db_user = self.db.query(User).filter(User.username == username).first()
        if db_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已被注册"
            )

        # 检查邮箱是否已存在
        db_email = self.db.query(User).filter(User.email == email).first()
        if db_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已被注册"
            )

        # 创建新用户
        hashed_password = self._get_password_hash(password)
        new_user = User(
            username=username,
            email=email,
            hashed_password=hashed_password,
            role=UserRole.USER  # 默认为普通用户
        )
        self.db.add(new_user)
        self.db.commit()
        self.db.refresh(new_user)

        # 生成访问令牌
        token = self._create_access_token(data={"sub": new_user.id})

        return {
            "success": True,
            "message": "注册成功",
            "token": token,
            "user_id": new_user.id,
            "username": new_user.username,
            "role": new_user.role
        }

    async def register_admin(self, username: str, email: str, password: str, verification: str) -> Dict[str, Any]:
        """
        注册新管理员

        Args:
            username: 用户名
            email: 电子邮件
            password: 密码
            verification: 管理员验证码

        Returns:
            Dict[str, Any]: 包含成功状态、消息、令牌和用户信息的字典
        """
        # 检查用户名是否已存在
        db_user = self.db.query(User).filter(User.username == username).first()
        if db_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已被注册"
            )

        # 检查邮箱是否已存在
        db_email = self.db.query(User).filter(User.email == email).first()
        if db_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已被注册"
            )

        # 验证管理员验证码
        verification_code = self.db.query(AdminVerification).filter(
            AdminVerification.code == verification,
            AdminVerification.is_used == False
        ).first()

        if not verification_code:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="管理员验证码无效或已被使用"
            )

        # 创建新管理员用户
        hashed_password = self._get_password_hash(password)
        new_admin = User(
            username=username,
            email=email,
            hashed_password=hashed_password,
            role=UserRole.ADMIN  # 设置为管理员角色
        )
        self.db.add(new_admin)
        self.db.commit()
        self.db.refresh(new_admin)

        # 标记验证码为已使用
        verification_code.is_used = True
        verification_code.used_at = datetime.now()
        verification_code.used_by = new_admin.id
        self.db.commit()

        # 生成访问令牌
        token = self._create_access_token(data={"sub": new_admin.id})

        return {
            "success": True,
            "message": "管理员注册成功",
            "token": token,
            "user_id": new_admin.id,
            "username": new_admin.username,
            "role": new_admin.role
        }

    async def login_user(self, username: str, password: str) -> Dict[str, Any]:
        """
        用户登录

        Args:
            username: 用户名
            password: 密码

        Returns:
            Dict[str, Any]: 包含成功状态、消息、令牌和用户信息的字典
        """
        # 根据用户名查找用户
        user = self.db.query(User).filter(User.username == username).first()

        # 验证用户和密码
        if not user or not self._verify_password(password, user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 生成访问令牌
        token = self._create_access_token(data={"sub": user.id})

        return {
            "success": True,
            "message": "登录成功",
            "token": token,
            "user_id": user.id,
            "username": user.username,
            "role": user.role
        }

    async def get_user_by_token(self, token: str) -> Optional[User]:
        """
        通过令牌获取用户

        Args:
            token: JWT令牌

        Returns:
            Optional[User]: 用户对象，如果令牌无效则为None
        """
        # 验证令牌
        user_id = self._verify_token(token)
        if user_id is None:
            return None

        # 获取用户
        user = self.db.query(User).filter(User.id == user_id).first()
        if user is None or not user.is_active:
            return None

        return user

    async def validate_admin(self, user: User) -> bool:
        """
        验证用户是否为管理员

        Args:
            user: 用户对象

        Returns:
            bool: 如果用户是管理员则为True，否则为False
        """
        # 检查用户角色是否为ADMIN
        return user.role == UserRole.ADMIN

    async def generate_admin_verification_code(self) -> str:
        """
        生成管理员验证码

        Returns:
            str: 生成的验证码
        """
        # 生成随机验证码（字母+数字，长度12位）
        alphabet = string.ascii_letters + string.digits
        code = ''.join(secrets.choice(alphabet) for _ in range(12))

        # 保存验证码到数据库
        verification = AdminVerification(code=code)
        self.db.add(verification)
        self.db.commit()

        return code

    async def get_verification_codes(self) -> list:
        """
        获取所有验证码

        Returns:
            list: 验证码列表
        """
        verifications = self.db.query(AdminVerification).all()
        return [
            {
                "id": v.id,
                "code": v.code,
                "is_used": v.is_used,
                "created_at": v.created_at,
                "used_at": v.used_at,
                "used_by": v.used_by
            }
            for v in verifications
        ]