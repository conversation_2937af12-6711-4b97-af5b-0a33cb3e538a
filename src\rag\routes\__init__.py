"""
API 路由聚合模块

此模块将所有子路由模块 (auth, users, conversation, feedback, admin, document) 导入并聚合到一个主 APIRouter 实例中，
以便在 FastAPI 应用中统一注册。
"""
from fastapi import APIRouter
from ..routes.auth import router as auth_router
from ..routes.users import router as users_router
from ..routes.conversation import router as conversation_router
from ..routes.feedback import router as feedback_router
from ..routes.admin import router as admin_router  # 新增管理员路由
from ..routes.document import router as document_router

router = APIRouter()
router.include_router(auth_router)
router.include_router(users_router)
router.include_router(conversation_router)
router.include_router(feedback_router)
router.include_router(admin_router)  # 注册管理员路由
router.include_router(document_router)
