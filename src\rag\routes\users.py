"""
用户 API 路由模块

此模块定义了与用户账户相关的 API 端点，
例如获取当前用户信息和更新用户信息。
"""
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime
from ..database import get_db, User, UserRole
from ..services.auth_service import AuthService
from ..dependencies import get_current_user, get_auth_service

# 创建路由
router = APIRouter(prefix="/users", tags=["users"])

# 请求和响应模型
class UserUpdateRequest(BaseModel):
    username: Optional[str] = None
    email: Optional[str] = None
    password: Optional[str] = None

class UserResponse(BaseModel):
    user_id: int
    username: str
    email: str
    created_at: datetime
    role: UserRole

    class Config:
        from_attributes = True

class BasicResponse(BaseModel):
    success: bool
    message: str

# 获取当前用户信息路由
@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """
    获取当前登录用户的信息

    Args:
        current_user: 当前用户对象（通过依赖项获取）

    Returns:
        UserResponse: 用户信息响应
    """
    return {
        "user_id": current_user.id,
        "username": current_user.username,
        "email": current_user.email,
        "created_at": current_user.created_at,
        "role": current_user.role
    }

# 更新用户信息路由
@router.put("/me", response_model=BasicResponse)
async def update_user_info(
    user_update: UserUpdateRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    更新当前用户的信息

    Args:
        user_update: 要更新的用户信息
        current_user: 当前用户对象（通过依赖项获取）
        db: 数据库会话

    Returns:
        BasicResponse: 基本响应信息
    """
    # 获取要更新的用户
    user = db.query(User).filter(User.id == current_user.id).first()
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")

    # 如果更新用户名，检查是否重复
    if user_update.username and user_update.username != user.username:
        db_user = db.query(User).filter(User.username == user_update.username).first()
        if db_user:
            raise HTTPException(status_code=400, detail="用户名已被使用")
        user.username = user_update.username

    # 如果更新邮箱，检查是否重复
    if user_update.email and user_update.email != user.email:
        db_email = db.query(User).filter(User.email == user_update.email).first()
        if db_email:
            raise HTTPException(status_code=400, detail="邮箱已被使用")
        user.email = user_update.email

    # 如果更新密码
    if user_update.password:
        auth_service = get_auth_service(db)
        user.hashed_password = auth_service._get_password_hash(user_update.password)

    # 保存更新
    db.commit()

    return {"success": True, "message": "用户信息已更新"}