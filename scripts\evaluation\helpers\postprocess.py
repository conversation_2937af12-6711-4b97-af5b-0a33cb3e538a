"""
This implementation is adapted from the RAGEval repository. Original repository: https://github.com/OpenBMB/RAGEval
"""

import json
import os

from helpers.client import OpenAIClient as Client

# Load environment variables
api_key = os.getenv('API_KEY')
base_url = os.getenv('BASE_URL')


def postprocess_zh(response: str, system_prompt: str, user_prompt: str, model_name: str) -> list:
  '''
  Remove common extra characters in gpt-4o, check the question type and array format to avoid errors when saving as a JSON file.
  '''
  client = Client(api_key=api_key, base_url=base_url, model_name=model_name)

  output = []
  flag = True
  while True:
    if '```json\n' in response:
      response = response.replace('```json\n', '').replace('```', '')
    else:
      try:
        response = json.loads(response)
        for item in response:
          if 'ref' in item:
            try:
              if '无法回答' in item['答案'] or item['ref'] == []:
                item['问题类型'] = '无关无解问'
                item['ref'] = []
              json_obj = {
                  '问题类型': item['问题类型'],
                  '问题': item['问题'],
                  '答案': item['答案'],
                  'ref': item['ref'],
              }
              output.append(json_obj)
            except KeyError:
              flag = False
              break
          else:
            try:
              if '无法回答' in item['答案']:
                item['问题类型'] = '无关无解问'
              json_obj = {
                  '问题类型': item['问题类型'],
                  '问题': item['问题'],
                  '答案': item['答案'],
              }
              output.append(json_obj)
            except KeyError:
              flag = False
              break
      except json.JSONDecodeError:
        flag = False

      if not flag:
        response = client.generate([{'system_prompt': system_prompt, 'user_prompt': user_prompt}], 'Regenerating')[0]
        flag = True
      else:
        return output


def postprocess_irrelevant_zh(response: str, system_prompt: str, user_prompt: str, model_name: str, domain: str, name: str) -> list:
  '''
  Remove common extra characters in gpt-4o, check the question type and array format to avoid errors when saving as a JSON file.
  '''
  client = Client(api_key=api_key, base_url=base_url, model_name=model_name)

  output = []
  flag = True
  while True:
    if '```json\n' in response:
      response = response.replace('```json\n', '').replace('```', '')
    else:
      try:
        response = json.loads(response)
        for item in response:
          try:
            if '无法回答' in item['答案'] or item['ref'] == []:
              item['问题类型'] = '无关无解问'
              item['ref'] = []
            if domain == 'law':
              if item['法院名'] != name:
                flag = False
                break
              else:
                json_obj = {
                    '法院名': item['法院名'],
                    '问题类型': item['问题类型'],
                    '问题': item['问题'],
                    '答案': item['答案'],
                    'ref': item['ref'],
                }
                output.append(json_obj)
            if domain == 'medical':
              if item['医院_病人名'] != name:
                flag = False
                break
              else:
                json_obj = {
                    '医院_病人名': item['医院_病人名'],
                    '问题类型': item['问题类型'],
                    '问题': item['问题'],
                    '答案': item['答案'],
                    'ref': item['ref'],
                }
                output.append(json_obj)
          except KeyError:
            flag = False
            break
      except json.JSONDecodeError:
        flag = False

      if not flag:
        response = client.generate([{'system_prompt': system_prompt, 'user_prompt': user_prompt}])[0]
        flag = True
      else:
        return output


def postprocess_reference_check_single_doc_zh(response: str, new_sentences: list, system_prompt: str, user_prompt: str, model_name: str) -> list:
  '''
  Remove common extra characters in gpt-4o, check the question type and array format, transform number to sentences to avoid errors when saving as a JSON file and maybe sometimes gpt-4o will generate ref without any number.
  '''
  client = Client(api_key=api_key, base_url=base_url, model_name=model_name)

  flag = True
  while True:
    if '```json\n' in response:
      response = response.replace('```json\n', '').replace('```', '')
    else:
      try:
        response = json.loads(response)
        for item in response:
          refs = []
          try:
            if item['ref'] == [] and item['问题类型'] != '无关无解问':
              flag = False
            else:
              for ref in item['ref']:
                refs.append(new_sentences[int(ref) - 1].split('] ')[-1])
              item['ref'] = refs
          except KeyError:
            flag = False
            break
      except json.JSONDecodeError:
        flag = False

      if not flag:
        response = client.generate([{'system_prompt': system_prompt, 'user_prompt': user_prompt}])[0]
        flag = True
      else:
        return response


def postprocess_reference_check_multi_doc_zh(response: str, domain: str, new_sentences_1: list, new_sentences_2: list, system_prompt: str, user_prompt: str, model_name: str) -> list:
  '''
  Remove common extra characters in gpt-4o, check the question type and array format, transform number to sentences to avoid errors when saving as a JSON file and maybe sometimes gpt-4o will generate ref without any number.
  '''
  client = Client(api_key=api_key, base_url=base_url, model_name=model_name)

  flag = True
  while True:
    if '```json\n' in response:
      response = response.replace('```json\n', '').replace('```', '')
    else:
      try:
        response = json.loads(response)
        for item in response:
          try:
            for ref in item['ref']:
              if ref['content'] == [] and item['问题类型'] != '无关无解问':
                flag = False
                break
              else:
                if domain == 'finance':
                  for sentence in new_sentences_1:
                    if ref['公司名'] in sentence:
                      refs = []
                      for r in ref['content']:
                        refs.append(new_sentences_1[int(r) - 1].split('] ')[-1])
                      ref['content'] = refs
                      break

                  for sentence in new_sentences_2:
                    if ref['公司名'] in sentence:
                      refs = []
                      for r in ref['content']:
                        refs.append(new_sentences_2[int(r) - 1].split('] ')[-1])
                      ref['content'] = refs
                      break

                if domain == 'law':
                  for sentence in new_sentences_1:
                    if ref['法院名'] in sentence:
                      refs = []
                      for r in ref['content']:
                        refs.append(new_sentences_1[int(r) - 1].split('] ')[-1])
                      ref['content'] = refs
                      break
                  for sentence in new_sentences_2:
                    if ref['法院名'] in sentence:
                      refs = []
                      for r in ref['content']:
                        refs.append(new_sentences_2[int(r) - 1].split('] ')[-1])
                      ref['content'] = refs
                      break

                if domain == 'medical':
                  for sentence in new_sentences_1:
                    if ref['医院_病人名'] in sentence:
                      refs = []
                      for r in ref['content']:
                        refs.append(new_sentences_1[int(r) - 1].split('] ')[-1])
                      ref['content'] = refs
                      break
                  for sentence in new_sentences_2:
                    if ref['医院_病人名'] in sentence:
                      refs = []
                      for r in ref['content']:
                        refs.append(new_sentences_2[int(r) - 1].split('] ')[-1])
                      ref['content'] = refs
                      break
          except KeyError:
            flag = False
            break
      except json.JSONDecodeError:
        flag = False

      if not flag:
        response = client.generate([{'system_prompt': system_prompt, 'user_prompt': user_prompt}])[0]
        flag = True
      else:
        return response
