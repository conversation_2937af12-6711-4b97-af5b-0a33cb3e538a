import json
import re
import os
from pathlib import Path

from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_chroma import Chroma
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_core.documents import Document
from langchain_text_splitters import MarkdownHeaderTextSplitter
from transformers import AutoTokenizer

os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'
os.environ['HF_HUB_DISABLE_SYMLINKS_WARNING'] = '1'

# ----------------- 初始化嵌入模型 -----------------
# 使用直接的模型名称而非本地路径
model_name = "BAAI/bge-large-zh-v1.5"
model_kwargs = {'device': 'cuda'}
encode_kwargs = {'normalize_embeddings': True}

embedding = HuggingFaceEmbeddings(
    model_name=model_name,
    model_kwargs=model_kwargs,
    encode_kwargs=encode_kwargs
)

# ----------------- 数据加载与预处理 -----------------
class ModDataLoader:
    def __init__(self):
        self.main_splitter = RecursiveCharacterTextSplitter(
            chunk_size=500,  
            chunk_overlap=50, 
            separators=["\n\n", "\n", "。", "；", ",", " "]
        )

        headers_to_split_on = [
        ("#", "Header 1")
        ]

        self.markdown_splitter = MarkdownHeaderTextSplitter(headers_to_split_on=headers_to_split_on, strip_headers=False)

    def load_single_mod(self, mod_path):
        """增强数据校验的加载方法"""
        global item_total_count
        try:
            with open(mod_path, encoding='utf-8') as f:
                mod_data = json.load(f)
            modename = mod_data.get("chinese_name", "")+"("+mod_data.get("english_name", "")  + ")"
            # ================= 第一步：加载物品信息 =================
            items = []
            for i in range(1, 11):
                item_field = f"data_item{i}_block" if i == 1 else f"data_item{i}_{['block', 'biome', 'dimension', 'entity', 'enchantment', 'buff', 'multiblock', 'natural_generation', 'hotkey', 'game_setting'][i - 1]}"
                for entry in mod_data.get(item_field, []):
                    try:
                        item_id = entry["id"]
                        item_file = Path(f"./itemnew/item{item_id}.json")
                        if item_file.exists():
                            item_total_count += 1
                            with open(item_file, encoding='utf-8') as f_item:
                                item_data = json.load(f_item)
                                if item_data is None:
                                    print(f"警告：物品文件 {item_file} 内容为空")
                                    continue

                                synthesis_strings = ""
                                for synth_entry in item_data.get("synthesis", []):
                                    if synthesis_strings:
                                        synthesis_strings += "\n"
                                    for recipe in synth_entry.get("recipe", []):
                                        recipe_str = f"配方是[{recipe.get('inputs', '')}]在[{recipe.get('workbench', '')}]合成[{recipe.get('outputs', '')}]"
                                        synthesis_strings += recipe_str
                                    for mod_req in synth_entry.get("mod_requirements", []):
                                        mod_str = f"模组要求是[{mod_req.get('name', '')}][{mod_req.get('url', '')}]"
                                        synthesis_strings += "," + mod_str
                                    for ver_req in synth_entry.get("version_requirements", []):
                                        ver_str = f"版本要求是[{ver_req}]"
                                        synthesis_strings += "," + ver_str
                                    for add_info in synth_entry.get("additional_info", []):
                                        links_str = ""
                                        for link in add_info.get("links", []):
                                            links_str += f"{link.get('name', '')}: {link.get('url', '')}"
                                            if link != add_info["links"][-1]:
                                                links_str += ", "
                                        add_str = f"额外信息是[{add_info.get('text', '')}]"
                                        if links_str:
                                            add_str += f"[{links_str}]"
                                        synthesis_strings += "," + add_str

                                items.append({
                                    "id": item_id,
                                    "chinese_name": item_data.get("chinese_name", ""),
                                    "english_name": item_data.get("english_name", ""),
                                    "give_command": item_data.get("give_command", ""),
                                    "content": item_data.get("content", "").replace(" ", ""),
                                    "synthesis": synthesis_strings.replace("\xa0", ""),
                                    "url": "https://www.mcmod.cn/item/" + str(item_id) + ".html",
                                    "nav1": item_data.get("navigation", {}).get("nav1", ""),
                                    "nav2": item_data.get("navigation", {}).get("nav2", ""),
                                    "nav3": item_data.get("navigation", {}).get("nav3", ""),
                                    "nav4": item_data.get("navigation", {}).get("nav4", ""),
                                })
                        else:
                            print(f"警告：物品文件 {item_file} 不存在")
                    except KeyError as e:
                        print(f"物品字段 {item_field} 数据格式错误：{str(e)}")
                    except Exception as e:
                        print(f"加载物品 {entry} 失败：{str(e)}")

            # ================= 第二步：加载教程文章 =================
            posts = []
            post_pattern = r"https://www\.mcmod\.cn/post/(\d+)\.html"
            for match in re.finditer(post_pattern, mod_data.get("mod_tutorial", "")):
                post_id = match.group(1)
                post_file = Path(f"./post/post{post_id}.json")
                try:
                    if post_file.exists():
                        with open(post_file, encoding='utf-8') as f_post:
                            post_data = json.load(f_post)
                            posts.append({
                                "id": post_id,
                                "content": post_data.get("content", ""),
                                "url": post_data.get("url", ""),
                                "summary": post_data.get("summary", ""),
                            })
                    else:
                        print(f"警告：教程文件 {post_file} 不存在")
                except Exception as e:
                    print(f"加载教程 {post_id} 失败：{str(e)}")

            # ================= 第三步：加载论坛讨论 =================
            threads = []
            thread_pattern = r"https://bbs\.mcmod\.cn/thread-(\d+)-\d+-\d+\.html"
            for match in re.finditer(thread_pattern, mod_data.get("mod_discussion", "")):
                thread_id = match.group(1)
                thread_file = Path(f"./bbs/thread{thread_id}.json")
                try:
                    if thread_file.exists():
                        with open(thread_file, encoding='utf-8') as f_thread:
                            thread_data = json.load(f_thread)
                            threads.append({
                                "id": thread_id,
                                "title": thread_data.get("title", ""),
                                "content": thread_data.get("content", ""),
                                "replies": thread_data.get("replies", []),
                                "url": thread_data.get("url", ""),
                                "summary": thread_data.get("summary", ""),
                            })
                    else:
                        print(f"警告：讨论帖文件 {thread_file} 不存在")
                except Exception as e:
                    print(f"加载讨论帖 {thread_id} 失败：{str(e)}")

            support_version = mod_data.get("supported_versions", "")

            content = " [状态] " + mod_data['status'] + "\n"
            content += "[来源] " + mod_data['source'] + "\n"
            content += "[主类别] " + mod_data['categoryMain'] + "\n"
            content += "[子类别] " + ",".join(mod_data['categorys']) + "\n"
            content += "[支持版本] " + support_version + "\n"
            content += "[介绍] " + mod_data.get('mod_intro', '').replace(" ", "") + "\n"

            # ================= 构建四个类型的文档 =================
            all_docs = []
            mod_id = mod_path.stem.replace("mode", "")  # Extract the mod ID from filename
            mod_url = f"https://www.mcmod.cn/class/{mod_id}.html"
            main_metadata = {
                "类型": "模组主体",
                "模组名称": modename,
                "源网站": mod_url,
                "主类别": mod_data['categoryMain'],
                "子类别": ",".join(mod_data['categorys']),
            }
            main_docs = self.main_splitter.create_documents([content], [main_metadata])
            all_docs.extend(main_docs)

            # 2. 物品文档
            for item in items:
                item_content = f"""
[源网站] {item['url']}
[所属模组] {modename}
[获取命令] {item['give_command']}
[模组分类] {item['nav1']}
[模组全称] {item['nav2']}
[物品分类] {item['nav3']}
[配方列表] {item['synthesis']}
[物品描述] {item['content']}
                """
                item_metadata = {
                    "类型": "物品",
                    "物品名称": item['chinese_name'] + "(" + item['english_name'] + ")",
                    "模组名称": modename,
                    "模组分类": item['nav1'],
                    "模组全称": item['nav2'],
                    "物品分类": item['nav3'],
                    "源网站": item['url'],
                }
                # all_docs.append(Document(page_content=item_content, metadata=item_metadata))
                item_doc_splits = self.main_splitter.create_documents([item_content], [item_metadata])
                all_docs.extend(item_doc_splits)

            # 3. 教程文档
            for post in posts:
                post_content = f"""
[所属模组] {modename}
[源网站] {post['url']}
[摘要] {post['summary']}
[教程内容] {post['content']}
                """
                post_metadata = {
                    "类型": "教程",
                    "模组名称": modename,
                    "源网站": post['url'],
                    "摘要": post['summary'],
                }
                if(post['content'] == ""):
                    continue
                # all_docs.append(Document(page_content=post_content, metadata=post_metadata))
                post_docs = self.main_splitter.create_documents([post_content], [post_metadata])
                all_docs.extend(post_docs)

            # 4. 讨论文档
            for thread in threads:
                replies = "\n".join([f"回复{i + 1}: {r}" for i, r in enumerate(thread['replies'])])
                thread_content = f"""
[所属模组] {modename}
[源网站] {thread['url']}
[标题] {thread['title']}
[摘要] {thread['summary']}
[内容] {thread['content']}
[最新回复] {replies}
                """
                thread_metadata = {
                    "类型": "讨论",
                    "模组名称": modename,
                    "源网站": thread['url'],
                    "标题": thread['title'],
                    "摘要": thread['summary'],
                }
                if thread['content'] == "" and replies == "":
                    continue
                # all_docs.append(Document(page_content=thread_content, metadata=thread_metadata))
                thread_docs = self.main_splitter.create_documents([thread_content], [thread_metadata])
                all_docs.extend(thread_docs)
            return all_docs

        except Exception as e:
            print(f"加载 {mod_path} 失败: {str(e)}")
            return []
        
    def copy_single_mod_item(self, mod_path):
        try:
            with open(mod_path, encoding='utf-8') as f:
                mod_data = json.load(f)
            missing_item_ids = []
            for i in range(1, 11):
                item_field = f"data_item{i}_block" if i == 1 else f"data_item{i}_{['block', 'biome', 'dimension', 'entity', 'enchantment', 'buff', 'multiblock', 'natural_generation', 'hotkey', 'game_setting'][i - 1]}"
                for entry in mod_data.get(item_field, []):
                    try:
                        item_id = entry["id"]
                        item_file = Path(f"./itemtotal/item{item_id}.json")
                        if item_file.exists():
                            item_file_copy = Path(f"./item/item{item_id}.json")
                            if not item_file_copy.exists():
                                with open(item_file, encoding='utf-8') as f_item:
                                    item_data = json.load(f_item)
                                    with open(item_file_copy, 'w', encoding='utf-8') as f_copy:
                                        json.dump(item_data, f_copy, ensure_ascii=False, indent=4)
                                print(f"复制物品文件 {item_file} 到 {item_file_copy}")
                        else:
                            # print(f"警告：物品文件 {item_file} 不存在")
                            missing_item_ids.append(int(item_id))
                    except KeyError as e:
                        print(f"物品字段 {item_field} 数据格式错误：{str(e)}")
                    except Exception as e:
                        print(f"加载物品 {entry} 失败：{str(e)}")
            if missing_item_ids:
                print(f"最大缺失物品ID: {max(missing_item_ids)}")
            return missing_item_ids
    
        except Exception as e:
            print(f"加载 {mod_path} 失败: {str(e)}")
            return []

# ----------------- 向量数据库构建 -----------------
def load_documents(mod_start, mod_end):
    loader = ModDataLoader()
    all_docs = []

    # # 加载模组数据
    # mod_dir = Path("./mode")
    # for i in range(mod_start, mod_end + 1):
    #     mod_file = mod_dir / f"mode{i}.json"
    #     if mod_file.exists():
    #         # loader.copy_single_mod_item(mod_file)
    #         all_docs.extend(loader.load_single_mod(mod_file))

    # 加载wiki数据
    wiki_dir = Path("./wiki")
    for wiki_file in wiki_dir.glob("*.json"):
        with open(wiki_file, encoding='utf-8') as f:
            wiki_data = json.load(f)
            wiki_content_raw = f"""
[源网站] {wiki_data.get('url', '')}
[标题] {wiki_data.get('title', '')}
[摘要] {wiki_data.get('summary', '')}
[内容] {wiki_data.get('content', '')}
            """
            base_wiki_metadata = {
                "类型": "wiki",
                "标题": wiki_data.get('title', ''),
                "源网站": wiki_data.get('url', ''),
                "摘要": wiki_data.get('summary', ''),
            }
            markdown_splitted_docs = loader.markdown_splitter.split_text(wiki_content_raw)
            for md_doc in markdown_splitted_docs:
                if not md_doc.page_content.strip():
                    continue
                token_splitted_docs = loader.main_splitter.create_documents(
                    [md_doc.page_content],
                    [base_wiki_metadata]
                )
                all_docs.extend(token_splitted_docs)            

    return all_docs


def build_vector_store(chroma_path, mod_start, mod_end):
    global item_total_count
    item_total_count = 0 
    print(f"开始load从{mod_start}到{mod_end}")
    all_docs = load_documents(mod_start, mod_end)

    print(f"item一共有 {item_total_count} 个物品")

    with open("doc.txt", "w", encoding="utf-8") as f:
        for idx, doc in enumerate(all_docs):
            f.write(f"{idx + 1}:\n")
            f.write(f"{len(doc.page_content)}\n")
            f.write(f"{doc.metadata}\n")
            f.write(f"{doc.page_content}\n")
            f.write("\n\n\n")

    vector_db = Chroma.from_documents(
        documents=all_docs[:1],
        embedding=embedding,
        persist_directory=chroma_path,
        collection_metadata={"hnsw:space": "cosine"}
    )
    print(f"向量数据库创建完成，共包含 {len(all_docs)} 个文档")
    print(f"{mod_start}~{mod_end}号模组数据已加载")
    return vector_db

# ----------------- 主程序 -----------------
if __name__ == "__main__":
    chroma_path = "./chroma_db_test"
    recreate_chroma_db = True
    if not Path(chroma_path).exists() or recreate_chroma_db:
        print("正在构建向量数据库")
        vector_db = build_vector_store(chroma_path, 0, 20000)
    else:
        print("正在加载现有的向量数据库")
        vector_db = Chroma(
            persist_directory=chroma_path,
            embedding_function=embedding
        )
    print("向量数据库包含向量的个数", vector_db._collection.count())