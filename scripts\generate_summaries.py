"""
Generate summaries for JSON files in the bbs, wiki, and post folders.

This script:
1. Reads all JSON files from the specified folders
2. Calls an LLM to generate summaries for the content
3. Adds a 'summary' field to each JSON file
4. Saves the updated JSON files
"""

import os
import json
import glob
from typing import Dict, List, Any, Optional
from pathlib import Path
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging

# Setup langchain components
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("summary_generation.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables (for API keys)
load_dotenv()

# Path configuration
BASE_DIR = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
BBS_DIR = BASE_DIR / "bbs"
WIKI_DIR = BASE_DIR / "data" / "wiki"
POST_DIR = BASE_DIR / "data" / "post"

# Initialize the LLM
model = ChatOpenAI(
    model=os.getenv("NEWLLM_MODEL"),
    api_key=os.getenv("NEWAPI_KEY"),
    base_url=os.getenv("NEWBASE_URL")
)

def setup_summary_chain():
    """Set up a chain to generate summaries using an LLM."""
    prompt = ChatPromptTemplate.from_template(
        """你是一个专业的摘要生成助手。
        请用与中文生成简洁的摘要。
        重点关注核心主题、关键要点和整体信息。
        保持摘要简明扼要（2-3句话）。

        待摘要文本：
        {content}

        摘要：
        """
    )
    
    chain = prompt | model | StrOutputParser()
    return chain

def generate_summary(content: str, summary_chain) -> str:
    """Generate a summary for the given content using the LLM chain."""
    try:
        summary = summary_chain.invoke({"content": content})
        return summary.strip()
    except Exception as e:
        logger.error(f"Error generating summary: {e}")
        return "Error generating summary."

def process_bbs_file(filepath: str, summary_chain) -> None:
    """Process a BBS JSON file: combine content and replies, generate summary, and save."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Combine content and replies
        full_content = data.get('content', '')
        replies = data.get('replies', [])
        if replies:
            reply_text = "\n".join(replies)
            full_content = f"{full_content}\n\nReplies:\n{reply_text}"
        
        # Generate summary
        summary = generate_summary(full_content, summary_chain)
        
        # Add summary to data
        data['summary'] = summary
        
        # Save updated file
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        
        logger.info(f"Successfully processed BBS file: {filepath}")
    except Exception as e:
        logger.error(f"Error processing BBS file {filepath}: {e}")

def process_wiki_or_post_file(filepath: str, summary_chain) -> None:
    """Process a Wiki or Post JSON file: generate summary and save."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Generate summary from content field
        content = data.get('content', '')
        summary = generate_summary(content, summary_chain)
        
        # Add summary to data
        data['summary'] = summary
        
        # Save updated file
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        
        logger.info(f"Successfully processed file: {filepath}")
    except Exception as e:
        logger.error(f"Error processing file {filepath}: {e}")

def process_files_with_threading(files_to_process, process_func, summary_chain, max_workers=10):
    """Process multiple files in parallel using threading."""
    success_count = 0
    error_count = 0
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_file = {
            executor.submit(process_func, file, summary_chain): file 
            for file in files_to_process
        }
        
        for future in as_completed(future_to_file):
            file = future_to_file[future]
            try:
                future.result()
                success_count += 1
                if success_count % 100 == 0:
                    logger.info(f"Progress: {success_count} files processed successfully")
            except Exception as e:
                error_count += 1
                logger.error(f"Exception processing {file}: {e}")
    
    return success_count, error_count

def main():
    """Main function to process all files."""
    start_time = time.time()
    logger.info("Starting summary generation process")
    
    # Setup the summary generation chain
    summary_chain = setup_summary_chain()
    
    # Get all JSON files
    bbs_files = glob.glob(str(BBS_DIR / "*.json"))
    wiki_files = glob.glob(str(WIKI_DIR / "*.json"))
    post_files = glob.glob(str(POST_DIR / "*.json"))
    
    logger.info(f"Found {len(bbs_files)} BBS files, {len(wiki_files)} Wiki files, and {len(post_files)} Post files")
    
    # Process BBS files
    logger.info("Processing BBS files...")
    bbs_success, bbs_error = process_files_with_threading(bbs_files, process_bbs_file, summary_chain)
    
    # # Process Wiki files
    # logger.info("Processing Wiki files...")
    # wiki_success, wiki_error = process_files_with_threading(wiki_files, process_wiki_or_post_file, summary_chain)
    
    # # Process Post files
    # logger.info("Processing Post files...")
    # post_success, post_error = process_files_with_threading(post_files, process_wiki_or_post_file, summary_chain)
    
    # Summary
    total_time = time.time() - start_time
    logger.info(f"Summary generation completed in {total_time:.2f} seconds")
    logger.info(f"BBS files: {bbs_success} succeeded, {bbs_error} failed")
    # logger.info(f"Wiki files: {wiki_success} succeeded, {wiki_error} failed")
    # logger.info(f"Post files: {post_success} succeeded, {post_error} failed")

if __name__ == "__main__":
    main()
