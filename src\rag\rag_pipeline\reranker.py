"""
重排序模块

此模块提供了文档重排序功能，用于提高检索结果的相关性。
"""

import os
import logging
from typing import List, Dict, Any, Optional, Tuple
import time
from pathlib import Path
from langchain_core.documents import Document

# 使用统一的日志配置
from ..logging_config import get_logger
from .. import config

logger = get_logger("reranker")

class Reranker:
    """
    文档重排序器，用于提高检索结果的相关性

    使用交叉编码器模型对文档进行重排序，提高与查询的相关性。
    """

    def __init__(self, model_name: str = config.RERANK_MODEL_NAME, device_str: str = config.DEVICE):
        """
        初始化重排序器

        Args:
            model_name: 重排序模型名称或路径
            device_str: 设备类型 ('cpu' 或 'cuda')
        """
        self.model_name = model_name
        self.device = device_str
        self.tokenizer = None
        self.model = None

        # 检查模型路径
        self._resolve_model_path()

        # 初始化模型
        self._initialize_model()

    def _resolve_model_path(self):
        """解析模型路径，支持本地路径或模型名称"""
        # 检查模型名称是否是本地路径
        if os.path.exists(self.model_name):
            logger.info(f"使用本地重排序模型路径: {self.model_name}")
            return

        # 检查相对于当前文件的路径
        local_model_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                                       "models", os.path.basename(self.model_name))
        if os.path.exists(local_model_path):
            logger.info(f"在 {local_model_path} 找到重排序模型")
            self.model_name = local_model_path
            return

        # 检查项目根目录下的models文件夹
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        root_model_path = os.path.join(project_root, "models", os.path.basename(self.model_name))
        if os.path.exists(root_model_path):
            logger.info(f"在项目根目录下找到重排序模型: {root_model_path}")
            self.model_name = root_model_path
            return

        # 如果本地没有找到，将使用模型名称从HuggingFace下载
        logger.info(f"未找到本地重排序模型，将直接使用模型名称 {self.model_name} 从HuggingFace下载")

    def _initialize_model(self):
        """初始化重排序模型"""
        try:
            # 记录开始时间
            start_time = time.time()
            logger.info(f"开始初始化重排序模型: {self.model_name}，设备: {self.device}")

            # 导入必要的库
            try:
                from transformers import AutoModelForSequenceClassification, AutoTokenizer
                import torch
            except ImportError:
                logger.error("未安装必要的库: transformers 或 torch，请使用 pip install transformers torch 安装")
                logger.warning("将使用基于文档长度的简单排序作为回退策略")
                return

            # 加载模型和分词器
            try:
                self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)

                # 设置设备
                self.device = torch.device(self.device if torch.cuda.is_available() and "cuda" in self.device else "cpu")

                # 根据设备类型选择加载方式
                if self.device.type == "cuda":
                    # 在GPU上使用半精度加载模型
                    try:
                        logger.info(f"GPU环境: 尝试使用半精度(FP16)加载模型: {self.model_name}...")
                        self.model = AutoModelForSequenceClassification.from_pretrained(
                            self.model_name,
                            torch_dtype=torch.float16
                        )
                        logger.info("成功使用半精度加载模型")
                    except Exception as fp16_error:
                        logger.warning(f"半精度加载失败: {fp16_error}，使用标准精度")
                        self.model = AutoModelForSequenceClassification.from_pretrained(self.model_name)
                else:
                    # 在CPU上使用标准精度加载模型
                    logger.info(f"CPU环境: 使用标准精度加载模型: {self.model_name}...")
                    self.model = AutoModelForSequenceClassification.from_pretrained(self.model_name)

                # 将模型移动到设备
                self.model.to(self.device)
                logger.info(f"模型已加载到设备: {self.device}")

                # 确保模型处于评估模式
                self.model.eval()

                # 简单预热模型
                try:
                    logger.info("预热模型以提高首次推理速度...")
                    with torch.no_grad():
                        # 创建一个小批量的假数据进行预热
                        dummy_input = self.tokenizer(
                            [("预热查询", "预热文档")],
                            padding=True,
                            truncation=True,
                            return_tensors='pt',
                            max_length=64
                        ).to(self.device)
                        _ = self.model(**dummy_input)
                    logger.info("模型预热完成")
                except Exception as warmup_error:
                    logger.warning(f"模型预热失败: {warmup_error}，跳过预热步骤")

                # 记录初始化时间
                init_time = time.time() - start_time
                logger.info(f"重排序模型初始化完成，耗时: {init_time:.2f}秒，使用设备: {self.device}")
            except Exception as model_error:
                logger.error(f"加载重排序模型失败: {model_error}", exc_info=True)
                logger.warning("将使用基于文档长度的简单排序作为回退策略")
                self.tokenizer = None
                self.model = None

        except Exception as e:
            logger.error(f"初始化重排序模型失败: {e}", exc_info=True)
            logger.warning("将使用基于文档长度的简单排序作为回退策略")

    async def rerank_documents(self, query: str, documents: List[Document]) -> List[Document]:
        """
        对文档列表进行重排序，提高与查询的相关性

        Args:
            query: 用户查询
            documents: 待重排序的文档列表

        Returns:
            List[Document]: 重排序后的文档列表
        """
        if not documents:
            logger.warning("无文档可供重排序")
            return documents

        # 记录开始时间
        start_time = time.time()
        logger.info(f"开始重排序 {len(documents)} 个文档，查询: '{query[:30]}{'...' if len(query) > 30 else ''}'")

        try:
            # 如果模型已加载，使用模型进行重排序
            if self.model and self.tokenizer:
                try:
                    # 导入必要的库
                    import torch
                    import asyncio
                    from concurrent.futures import ThreadPoolExecutor
                    import numpy as np

                    
                    # 使用配置的批处理大小
                    batch_size = config.RERANK_BATCH_SIZE  # 可以通过环境变量调整

                    # 准备查询-文档对
                    pairs = []
                    for doc in documents:
                        # 限制文档内容长度，避免超出模型最大长度限制
                        doc_content = doc.page_content[:1024]
                        pairs.append((query, doc_content))

                    # 创建一个线程池执行器，利用多核CPU
                    import os
                    # 使用CPU核心数的一半作为线程数，但至少2个，最多4个
                    cpu_count = os.cpu_count() or 2
                    max_workers = max(2, min(cpu_count // 2, 4))
                    logger.info(f"使用 {max_workers} 个线程进行并行处理")
                    executor = ThreadPoolExecutor(max_workers=max_workers)
                    loop = asyncio.get_event_loop()

                    # 定义批处理推理函数
                    def process_batch(batch_pairs):
                        with torch.no_grad():
                            # 简化的动态最大长度计算
                            # 根据查询和文档长度估算所需的最大长度
                            query_len = len(batch_pairs[0][0])  # 所有对中查询都相同
                            max_doc_len = max(len(pair[1]) for pair in batch_pairs)

                            # 估算token数量 (每个字符约1.5个token)
                            estimated_tokens = int((query_len + max_doc_len) * 1.5)

                            # 设置动态最大长度，但限制在合理范围内
                            dynamic_max_length = min(512, max(128, min(estimated_tokens, 384)))

                            # 对批次进行编码
                            batch_inputs = self.tokenizer(
                                batch_pairs,
                                padding=True,
                                truncation=True,
                                return_tensors='pt',
                                max_length=dynamic_max_length
                            ).to(self.device)

                            # 执行推理
                            batch_outputs = self.model(**batch_inputs)

                            # 提取得分
                            if hasattr(batch_outputs, 'logits'):
                                batch_scores = batch_outputs.logits.squeeze(-1).cpu().numpy()
                            else:
                                batch_scores = batch_outputs[0].squeeze(-1).cpu().numpy()

                            # 确保返回的是一维数组
                            if len(batch_scores.shape) == 0:
                                batch_scores = np.array([float(batch_scores)])

                            return batch_scores

                    # 分批处理所有文档
                    all_scores = []
                    total_batches = (len(pairs) + batch_size - 1) // batch_size

                    # 如果文档数量少，直接处理
                    if len(pairs) <= batch_size:
                        logger.info(f"文档数量较少 ({len(pairs)}), 单批次处理")
                        batch_scores = await loop.run_in_executor(executor, process_batch, pairs)
                        all_scores = batch_scores.tolist()
                    else:
                        # 分批处理
                        logger.info(f"文档数量较多 ({len(pairs)}), 分 {total_batches} 批处理")
                        for i in range(0, len(pairs), batch_size):
                            batch_start_time = time.time()
                            batch_pairs = pairs[i:i+batch_size]
                            current_batch = (i // batch_size) + 1

                            logger.info(f"处理批次 {current_batch}/{total_batches}, 大小: {len(batch_pairs)}")
                            batch_scores = await loop.run_in_executor(executor, process_batch, batch_pairs)

                            all_scores.extend(batch_scores.tolist())
                            batch_time = time.time() - batch_start_time
                            logger.info(f"批次 {current_batch}/{total_batches} 完成, 耗时: {batch_time:.2f}秒")

                    # 关闭线程池
                    executor.shutdown(wait=False)

                    if len(all_scores) != len(documents):
                        logger.warning(f"得分数量 ({len(all_scores)}) 与过滤后文档数量 ({len(documents)}) 不匹配，使用回退策略")
                        return self._mock_rerank_by_length(documents)

                    # 按得分降序排序
                    scored_docs = sorted(zip(documents, all_scores), key=lambda x: x[1], reverse=True)

                    # 记录得分分布情况
                    if scored_docs:
                        scores_array = np.array([score for _, score in scored_docs])
                        top_score = scored_docs[0][1]
                        min_score = scored_docs[-1][1]
                        mean_score = np.mean(scores_array)
                        median_score = np.median(scores_array)
                        logger.info(f"得分统计 | 最高: {top_score:.4f} | 最低: {min_score:.4f} | 平均: {mean_score:.4f} | 中位数: {median_score:.4f}")

                    # 选取前K个文档
                    reranked_docs = [doc for doc, _ in scored_docs[:config.RERANK_TOP_K]]

                    # 记录重排序时间
                    rerank_time = time.time() - start_time
                    logger.info(f"模型重排序完成，耗时: {rerank_time:.2f}秒，选取了 {len(reranked_docs)}/{len(documents)} 个文档")

                    # 记录每个选中文档的得分
                    if logger.isEnabledFor(logging.DEBUG):
                        for i, (doc, score) in enumerate(scored_docs[:config.RERANK_TOP_K]):
                            doc_title = getattr(doc.metadata, 'title', None) or doc.page_content[:30]
                            logger.debug(f"Top {i+1}: 得分={score:.4f}, 文档='{doc_title}...'")

                    return reranked_docs

                except Exception as rerank_error:
                    logger.error(f"使用模型重排序时发生错误: {rerank_error}", exc_info=True)
                    logger.warning("由于重排序错误，使用基于文档长度的简单排序作为回退")
                    reranked_docs = self._mock_rerank_by_length(documents)
            else:
                # 模型未加载，使用模拟实现
                logger.warning("重排序模型未加载，使用基于文档长度的简单排序")
                reranked_docs = self._mock_rerank_by_length(documents)

            # 记录重排序时间
            rerank_time = time.time() - start_time
            logger.info(f"重排序完成，耗时: {rerank_time:.2f}秒，选取了 {len(reranked_docs)} 个文档")

            return reranked_docs

        except Exception as e:
            logger.error(f"重排序过程中发生错误: {e}", exc_info=True)

            # 出错时使用回退策略
            logger.warning("由于重排序错误，使用基于文档长度的简单排序作为回退")
            fallback_docs = self._mock_rerank_by_length(documents)
            return fallback_docs

    def _mock_rerank_by_length(self, documents: List[Document]) -> List[Document]:
        """
        简单的基于文档长度的排序，作为重排序失败时的回退策略

        Args:
            documents: 待排序的文档列表

        Returns:
            List[Document]: 排序后的文档列表
        """
        # 按文档内容长度降序排序
        sorted_docs = sorted(documents, key=lambda d: len(d.page_content), reverse=True)

        # 选取前K个文档
        top_k = min(len(sorted_docs), config.RERANK_TOP_K)
        result = sorted_docs[:top_k]

        logger.info(f"使用基于长度的简单排序，选取了前 {top_k} 个文档")
        return result
