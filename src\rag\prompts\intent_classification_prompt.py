"""
意图分类的prompt模板

此模块定义了用于意图分类的prompt模板，用于从用户查询中识别意图。
"""

# 意图分类的prompt模板
INTENT_CLASSIFICATION_PROMPT_TEMPLATE = """
请分析以下用户提出的Minecraft相关问题，并判断用户的意图。

首先，你需要判断问题是关于原版Minecraft还是模组：
- 如果问题明确涉及原版Minecraft内容，包含"find_vanilla_wiki"意图
- 如果问题明确涉及模组内容，包含"find_mod_info"意图
- 如果不确定是原版还是模组内容，或问题可能涉及两者，同时包含"find_vanilla_wiki"和"find_mod_info"意图

可用意图列表：
1. find_item: 用户想查询某个具体“物品/方块”、“群系/群落”、“世界/维度”、“生物/实体”、“附魔/魔咒”、“BUFF/DEBUFF”、“多方块结构”、“自然生成”、“绑定热键”、“游戏设定”信息。
2. find_tutorial: 用户想查找某个操作、事件或模组内容的教程，或者询问是不是、如何等问题
3. find_mod_info: 用户想了解某个模组的整体信息或推荐。此意图必须包含在所有与模组相关的问题中。
4. find_crafting_recipe: 用户想知道如何合成某个物品。
5. find_vanilla_wiki: 用户的问题与原版Minecraft内容相关。此意图必须包含在所有与原版Minecraft相关的问题中。
6. comprehensive_answer: 用户的问题比较抽象、开放，或需要结合多方面知识进行推理和回答。
7. chit_chat: 用户只是在打招呼或进行无具体Minecraft内容的闲聊。

以下是一些用户查询及其对应意图的示例：

用户查询: "你好。"
意图: ["chit_chat"]

用户查询: "怎么打败虚空守望者。"
意图: ["find_item", "find_tutorial", "find_mod_info"]

用户查询: "有没有种菜模组。"
意图: ["find_mod_info"]

用户查询: "如果我想在1.21建造一个袭击塔要如何做，利用哪些机制。"
意图: ["find_tutorial", "comprehensive_answer", "find_vanilla_wiki"]

用户查询: "在Minecraft 1.9中如何合成一个盾牌？"
意图: ["find_crafting_recipe", "find_item", "find_vanilla_wiki"]

用户查询: "tnt复制是不是在最近的一个快照版被修复了？"
意图: ["comprehensive_answer", "find_vanilla_wiki"]

用户查询: "虚无世界3最好的武器是什么"
意图: ["comprehensive_answer", "find_item", "find_mod_info"]

用户查询: "潜影贝的瞬移要满足哪些条件？"
意图: ["find_item", "find_vanilla_wiki"]

用户查询: "冰火传说的龙一般情况龙食吃了是不是直接加一天成长天数？"
意图: ["find_item", "find_mod_info"]

用户查询: "有神奇宝贝为主题的模组推荐吗？"
意图: ["find_mod_info"]

用户查询: "怎么进入暮色森林？"
意图: ["find_tutorial", "find_vanilla_wiki", "find_mod_info"]

请分析以下用户最新提出的问题，并根据上述意图列表和示例，判断用户的意图。
请记住以下重要规则：
1. 如果问题明确涉及原版Minecraft内容，包含"find_vanilla_wiki"意图
2. 如果问题明确涉及模组内容，包含"find_mod_info"意图
3. 如果不确定是原版还是模组，或问题可能涉及两者，同时包含"find_vanilla_wiki"和"find_mod_info"意图
4. 一个问题可以同时包含多个意图
5. 当遇到不熟悉的内容名称时，倾向于同时包含原版和模组意图以确保完整搜索

请使用以下格式返回结果，确保返回的是有效的JSON格式：

{{"intents": ["意图1", "意图2", ...]}}

例如：{{"intents": ["find_item", "find_tutorial", "find_mod_info"]}}

如果用户只是闲聊或意图不明，则返回 {{"intents": ["chit_chat"]}} 或 {{"intents": []}}

用户最新问题: {query}
分析结果 (JSON格式):
"""
