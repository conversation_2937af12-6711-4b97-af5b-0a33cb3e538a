"""
对话 API 路由模块

此模块定义了与用户对话相关的 API 端点，
包括创建、获取、更新、删除对话，以及处理对话消息和流式响应。
"""
from fastapi import APIRouter, Depends, BackgroundTasks
from sse_starlette.sse import EventSourceResponse
from pydantic import BaseModel
from typing import List, AsyncIterable, Dict, Any, Optional
from datetime import datetime
import json

from ..database import User, SessionLocal, ConversationType, MessageType
from ..dependencies import get_conversation_service, get_current_user, get_dump_key
from ..services.conversation_service import ConversationService
from ..rag_pipeline.pipeline import RAGPipeline

# 创建路由
router = APIRouter(prefix="/conversations", tags=["conversations"])

# 请求和响应模型
class MessageCreate(BaseModel):
    content: str

class MessageResponse(BaseModel):
    id: int
    role: str
    type: MessageType = MessageType.TEXT  # 默认为文本类型，保持向后兼容
    content: str
    item_name: Optional[str] = None  # 仅crafting类型消息使用
    created_at: datetime

    class Config:
        from_attributes = True

class ConversationCreate(BaseModel):
    title: str
    type: Optional[ConversationType] = ConversationType.NORMAL

class ConversationUpdate(BaseModel):
    title: str
    type: Optional[ConversationType] = None

class ConversationResponse(BaseModel):
    id: int
    title: str
    type: ConversationType
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class ConversationDetailResponse(BaseModel):
    id: int
    title: str
    type: ConversationType
    created_at: datetime
    updated_at: datetime
    messages: List[MessageResponse]
    craftingContext: Optional[Dict[str, Any]] = None  # 合成上下文，仅合成导航对话使用

    class Config:
        from_attributes = True

class NewMessageRequest(BaseModel):
    message: str
    type: Optional[ConversationType] = ConversationType.NORMAL

class NewMessageResponse(BaseModel):
    conversation_id: int
    messages: List[MessageResponse]
    title: str

    class Config:
        from_attributes = True

# 创建新对话 (手动方式，保留旧有API但不在前端使用)
@router.post("", response_model=ConversationResponse)
async def create_conversation(
    conversation_data: ConversationCreate,
    current_user: User = Depends(get_current_user),
    service: ConversationService = Depends(get_conversation_service)
):
    return await service.create_conversation(
        user_id=current_user.id,
        title=conversation_data.title,
        conversation_type=conversation_data.type
    )

# 获取用户的所有对话
@router.get("", response_model=List[ConversationResponse])
async def get_conversations(
    current_user: User = Depends(get_current_user),
    service: ConversationService = Depends(get_conversation_service)
):
    return await service.get_user_conversations(user_id=current_user.id)

# 获取单个对话详情
@router.get("/{conversation_id}", response_model=ConversationDetailResponse)
async def get_conversation(
    conversation_id: int,
    current_user: User = Depends(get_current_user),
    service: ConversationService = Depends(get_conversation_service)
):
    return await service.get_conversation(
        conversation_id=conversation_id,
        user_id=current_user.id
    )

# 更新对话标题和类型
@router.put("/{conversation_id}", response_model=ConversationResponse)
async def update_conversation(
    conversation_id: int,
    conversation_data: ConversationUpdate,
    current_user: User = Depends(get_current_user),
    service: ConversationService = Depends(get_conversation_service)
):
    return await service.update_conversation(
        conversation_id=conversation_id,
        user_id=current_user.id,
        title=conversation_data.title,
        conversation_type=conversation_data.type
    )

# 删除对话
@router.delete("/{conversation_id}")
async def delete_conversation(
    conversation_id: int,
    current_user: User = Depends(get_current_user),
    service: ConversationService = Depends(get_conversation_service)
):
    await service.delete_conversation(
        conversation_id=conversation_id,
        user_id=current_user.id
    )
    return {"message": "对话已删除"}

# 后台任务：保存助手消息
async def save_assistant_message_task(conversation_id: int, assistant_content: str):
    """后台任务：保存完整的助手消息"""
    db = SessionLocal()  # 为后台任务创建新的数据库会话
    try:
        # 创建服务实例 (后台任务中不能使用Depends，需要手动创建服务实例)
        # 注意：在后台任务中，我们不需要RAG Pipeline，因为我们只是保存消息
        # 创建一个空的RAG Pipeline占位符
        dummy_pipeline = None
        service = ConversationService(
            db=db,
            rag_pipeline=dummy_pipeline,  # 这里不需要RAG Pipeline
        )
        await service.save_assistant_message(conversation_id, assistant_content)
    except Exception as e:
        print(f"后台任务错误：保存助手消息失败 (对话ID: {conversation_id}): {e}")
    finally:
        db.close()

# 后台任务：保存带类型的助手消息
async def save_assistant_message_with_type_task(
    conversation_id: int, assistant_content: str, message_type: MessageType = MessageType.TEXT, item_name: Optional[str] = None
):
    """后台任务：保存完整的助手消息（支持消息类型和物品名称）"""
    db = SessionLocal()
    try:
        dummy_pipeline = None
        service = ConversationService(
            db=db,
            rag_pipeline=dummy_pipeline,
        )
        await service.save_assistant_message_with_type(conversation_id, assistant_content, message_type, item_name)
    except Exception as e:
        print(f"后台任务错误：保存助手消息失败 (对话ID: {conversation_id}): {e}")
    finally:
        db.close()

# 后台任务：保存合成上下文
async def save_crafting_context_task(conversation_id: int, crafting_context: Dict[str, Any]):
    """后台任务：保存合成上下文"""
    db = SessionLocal()
    try:
        dummy_pipeline = None
        service = ConversationService(
            db=db,
            rag_pipeline=dummy_pipeline,
        )
        await service.save_crafting_context(conversation_id, crafting_context)
    except Exception as e:
        print(f"后台任务错误：保存合成上下文失败 (对话ID: {conversation_id}): {e}")
    finally:
        db.close()

# 后台任务：更新对话标题
async def update_conversation_title_task(conversation_id: int, title: str):
    """后台任务：更新对话标题"""
    db = SessionLocal()
    try:
        # 创建服务实例 (后台任务中不能使用Depends，需要手动创建服务实例)
        # 注意：在后台任务中，我们不需要RAG Pipeline，因为我们只是更新标题
        dummy_pipeline = None
        service = ConversationService(
            db=db,
            rag_pipeline=dummy_pipeline,  # 这里不需要RAG Pipeline
        )
        await service.update_conversation_title(conversation_id, title)
    except Exception as e:
        print(f"后台任务错误：更新标题失败 (对话ID: {conversation_id}): {e}")
    finally:
        db.close()

# 在对话中添加新消息并获取流式回复
@router.post("/{conversation_id}/messages")
async def stream_message(
    conversation_id: int,
    message_data: MessageCreate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    service: ConversationService = Depends(get_conversation_service)
):
    # 使用服务层处理新消息
    async def event_generator() -> AsyncIterable[Dict[str, Any]]:
        full_response = ""
        message_type = MessageType.TEXT
        item_name = None

        async for event in service.handle_new_message(
            conversation_id=conversation_id,
            user_id=current_user.id,
            user_content=message_data.content
        ):
            # 先处理事件内容，再传递事件
            if event["event"] == "message":
                if isinstance(event["data"], str):
                    full_response += event["data"]
                elif isinstance(event["data"], dict):
                    chunk_data = event["data"]
                    if "chunk" in chunk_data:
                        full_response += chunk_data["chunk"]
            elif event["event"] == "message_complete":
                # 解析消息完成事件，获取消息类型和物品名称
                try:
                    complete_data = json.loads(event["data"])
                    message_type = MessageType(complete_data.get("type", "TEXT"))
                    item_name = complete_data.get("item_name")
                except (json.JSONDecodeError, ValueError) as e:
                    print(f"解析message_complete事件失败: {e}")
            elif event["event"] == "end":
                # 在传递end事件之前，先安排后台任务保存助手消息
                background_tasks.add_task(
                    save_assistant_message_with_type_task,
                    conversation_id,
                    full_response,
                    message_type,
                    item_name
                )

            # 传递所有事件
            yield event

    return EventSourceResponse(event_generator())

# 新增：自动创建对话并获取流式回复
@router.post("/new")
async def stream_new_conversation(
    message_data: NewMessageRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    service: ConversationService = Depends(get_conversation_service)
):
    try:
        # 使用服务层创建新对话并处理第一条消息
        conversation_id, _, event_generator_stream = await service.handle_new_conversation_message(
            user_id=current_user.id,
            user_content=message_data.message,
            conversation_type=message_data.type
        )

        # 包装事件生成器，在适当的时候添加后台任务
        async def wrapped_event_generator() -> AsyncIterable[Dict[str, Any]]:
            full_response = ""
            message_type = MessageType.TEXT
            item_name = None
            crafting_context = None

            async for event in event_generator_stream:
                # 先处理事件内容，再传递事件
                if event["event"] == "message":
                    if isinstance(event["data"], str):
                        full_response += event["data"]
                    elif isinstance(event["data"], dict):
                        chunk_data = event["data"]
                        if "chunk" in chunk_data:
                            full_response += chunk_data["chunk"]
                elif event["event"] == "message_complete":
                    # 解析消息完成事件，获取消息类型和物品名称
                    try:
                        complete_data = json.loads(event["data"])
                        message_type = MessageType(complete_data.get("type", "TEXT"))
                        item_name = complete_data.get("item_name")
                    except (json.JSONDecodeError, ValueError) as e:
                        print(f"解析message_complete事件失败: {e}")
                elif event["event"] == "crafting_context":
                    # 解析合成上下文事件
                    try:
                        crafting_context = json.loads(event["data"])
                    except json.JSONDecodeError as e:
                        print(f"解析crafting_context事件失败: {e}")
                elif event["event"] == "end":
                    # 在传递end事件之前，先安排后台任务
                    background_tasks.add_task(
                        save_assistant_message_with_type_task,
                        conversation_id,
                        full_response,
                        message_type,
                        item_name
                    )

                    # 如果有合成上下文，也安排保存任务
                    if crafting_context:
                        background_tasks.add_task(save_crafting_context_task, conversation_id, crafting_context)
                elif event["event"] == "title_update":
                    # 标题更新，解析新标题并安排更新标题的后台任务
                    title_data = json.loads(event["data"])
                    generated_title = title_data["title"]
                    background_tasks.add_task(update_conversation_title_task, conversation_id, generated_title)

                # 传递所有事件
                yield event

        return EventSourceResponse(wrapped_event_generator())

    except Exception as e:
        # 处理创建对话或保存用户消息时的初始错误
        print(f"创建对话或保存用户消息时出错: {e}")
        async def initial_error_stream():
            yield {"event": "error", "data": json.dumps({"message": "无法创建新对话"})}
        return EventSourceResponse(initial_error_stream())

@router.post("/dump")
async def dump_message(
    user_content: str,
    dump_key: str = Depends(get_dump_key),
    service: ConversationService = Depends(get_conversation_service)
):
    return await service.handle_dump_message(user_content)
