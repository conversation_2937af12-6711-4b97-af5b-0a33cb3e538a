"""
数据库迁移脚本：创建合成上下文表
Migration: Create crafting_contexts table

此脚本创建crafting_contexts表，用于存储对话级别的合成树信息。
"""

import sys
import os
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.rag.config import DATABASE_URL

def run_migration():
    """执行数据库迁移"""
    print("开始执行数据库迁移：创建合成上下文表...")

    # 创建数据库连接
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    with SessionLocal() as session:
        try:
            # 检查表是否已存在
            result = session.execute(text("""
                SELECT COUNT(*) as count
                FROM information_schema.tables
                WHERE table_name = 'crafting_contexts'
            """))

            table_exists = result.fetchone()[0] > 0

            if table_exists:
                print("crafting_contexts表已存在，跳过迁移")
                return True

            # 创建crafting_contexts表
            print("创建crafting_contexts表...")
            session.execute(text("""
                CREATE TABLE crafting_contexts (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    conversation_id INT UNIQUE NOT NULL,
                    target_item VARCHAR(255) NOT NULL,
                    shared_tree JSON NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE
                )
            """))

            # 为conversation_id字段添加索引
            print("为conversation_id字段添加索引...")
            session.execute(text("""
                CREATE INDEX idx_crafting_contexts_conversation ON crafting_contexts(conversation_id)
            """))

            # 为target_item字段添加索引
            print("为target_item字段添加索引...")
            session.execute(text("""
                CREATE INDEX idx_crafting_contexts_target_item ON crafting_contexts(target_item)
            """))

            # 为created_at字段添加索引
            print("为created_at字段添加索引...")
            session.execute(text("""
                CREATE INDEX idx_crafting_contexts_created_at ON crafting_contexts(created_at)
            """))

            # 为updated_at字段添加索引
            print("为updated_at字段添加索引...")
            session.execute(text("""
                CREATE INDEX idx_crafting_contexts_updated_at ON crafting_contexts(updated_at)
            """))

            # 提交更改
            session.commit()
            print("数据库迁移完成！")

            # 验证迁移结果
            print("验证迁移结果...")
            
            # 验证表是否创建成功
            result = session.execute(text("""
                SELECT COUNT(*) as count
                FROM information_schema.tables
                WHERE table_name = 'crafting_contexts'
            """))

            if result.fetchone()[0] > 0:
                print("✓ crafting_contexts表已成功创建")
            else:
                print("✗ crafting_contexts表创建失败")
                return False

            # 验证字段是否正确创建
            result = session.execute(text("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_name = 'crafting_contexts'
                ORDER BY ordinal_position
            """))

            columns = result.fetchall()
            expected_columns = [
                ('id', 'int', 'NO'),
                ('conversation_id', 'int', 'NO'),
                ('target_item', 'varchar', 'NO'),
                ('shared_tree', 'json', 'NO'),
                ('created_at', 'timestamp', 'YES'),
                ('updated_at', 'timestamp', 'YES')
            ]

            print("验证表结构...")
            for expected in expected_columns:
                found = False
                for actual in columns:
                    if actual[0] == expected[0]:
                        found = True
                        if actual[1] == expected[1] and actual[2] == expected[2]:
                            print(f"✓ 字段 {expected[0]} 结构正确")
                        else:
                            print(f"✗ 字段 {expected[0]} 结构不正确: 期望 {expected[1]}/{expected[2]}, 实际 {actual[1]}/{actual[2]}")
                        break
                if not found:
                    print(f"✗ 字段 {expected[0]} 未找到")
                    return False

            # 检查索引是否创建成功
            result = session.execute(text("""
                SHOW INDEX FROM crafting_contexts
            """))

            indexes = result.fetchall()
            expected_indexes = [
                'idx_crafting_contexts_conversation',
                'idx_crafting_contexts_target_item',
                'idx_crafting_contexts_created_at',
                'idx_crafting_contexts_updated_at'
            ]

            print("验证索引...")
            for expected_index in expected_indexes:
                found = False
                for index in indexes:
                    if index[2] == expected_index:  # Key_name is at index 2
                        found = True
                        print(f"✓ 索引 {expected_index} 已成功创建")
                        break
                if not found:
                    print(f"✗ 索引 {expected_index} 创建失败")
                    return False

            # 验证外键约束
            result = session.execute(text("""
                SELECT CONSTRAINT_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME
                FROM information_schema.KEY_COLUMN_USAGE
                WHERE table_name = 'crafting_contexts'
                AND REFERENCED_TABLE_NAME IS NOT NULL
            """))

            foreign_keys = result.fetchall()
            if foreign_keys:
                for fk in foreign_keys:
                    if fk[1] == 'conversations' and fk[2] == 'id':
                        print("✓ 外键约束已成功创建")
                        break
                else:
                    print("✗ 外键约束创建失败")
                    return False
            else:
                print("✗ 未找到外键约束")
                return False

            print("所有迁移步骤已成功完成！")
            return True

        except Exception as e:
            print(f"迁移过程中发生错误: {e}")
            session.rollback()
            return False

def rollback_migration():
    """回滚数据库迁移"""
    print("开始回滚数据库迁移：删除合成上下文表...")

    # 创建数据库连接
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    with SessionLocal() as session:
        try:
            # 检查表是否存在
            result = session.execute(text("""
                SELECT COUNT(*) as count
                FROM information_schema.tables
                WHERE table_name = 'crafting_contexts'
            """))

            table_exists = result.fetchone()[0] > 0

            if not table_exists:
                print("crafting_contexts表不存在，无需回滚")
                return True

            # 删除表（会自动删除所有索引和约束）
            print("删除crafting_contexts表...")
            session.execute(text("""
                DROP TABLE crafting_contexts
            """))

            # 提交更改
            session.commit()
            print("数据库迁移回滚完成！")
            return True

        except Exception as e:
            print(f"回滚过程中发生错误: {e}")
            session.rollback()
            return False

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="合成上下文表数据库迁移脚本")
    parser.add_argument("--rollback", action="store_true", help="回滚迁移")

    args = parser.parse_args()

    if args.rollback:
        success = rollback_migration()
    else:
        success = run_migration()

    if success:
        print("操作成功完成！")
        sys.exit(0)
    else:
        print("操作失败！")
        sys.exit(1)
