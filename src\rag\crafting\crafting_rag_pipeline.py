"""
合成导航RAG Pipeline

此模块提供合成导航功能的核心处理逻辑，集成物品识别、合成树生成和智能响应决策。
"""

import json
import time
from typing import Dict, Any, List, Optional, AsyncIterable, Tuple
from enum import Enum

from .item_entity_extractor import ItemEntityExtractor
from .tree_generator import CraftingTreeGenerator
from .prompts import CRAFTING_RESPONSE_PROMPT_TEMPLATE
from ..rag_pipeline.pipeline import RAGPipeline
from ..database import MessageType
from ..logging_config import get_logger

logger = get_logger("crafting_rag_pipeline")

class ResponseType(str, Enum):
    """响应类型枚举"""
    TEXT = "TEXT"
    CRAFTING = "CRAFTING"

class CraftingRAGPipeline:
    """
    合成导航RAG Pipeline

    集成物品识别、合成树生成和智能响应决策的核心处理器。
    """

    def __init__(
        self,
        item_extractor: ItemEntityExtractor,
        tree_generator: CraftingTreeGenerator,
        base_rag_pipeline: RAGPipeline
    ):
        """
        初始化合成导航RAG Pipeline

        Args:
            item_extractor: 物品实体识别器
            tree_generator: 合成树生成器
            base_rag_pipeline: 基础RAG Pipeline
        """
        self.item_extractor = item_extractor
        self.tree_generator = tree_generator
        self.base_rag_pipeline = base_rag_pipeline

        logger.info("合成导航RAG Pipeline初始化完成")

    async def process_crafting_message(
        self,
        message: str,
        chat_history_str: str = "",
        existing_crafting_context: Optional[Dict[str, Any]] = None
    ) -> Tuple[ResponseType, Optional[str], Optional[Dict[str, Any]], AsyncIterable[Dict[str, Any]]]:
        """
        处理合成导航消息

        Args:
            message: 用户消息
            chat_history_str: 对话历史
            existing_crafting_context: 现有的合成上下文

        Returns:
            Tuple[ResponseType, Optional[str], Optional[Dict[str, Any]], AsyncIterable[Dict[str, Any]]]:
            (响应类型, 识别的物品名称, 合成上下文, 事件流)
        """
        start_time = time.time()
        logger.info(f"开始处理合成导航消息: '{message[:50]}{'...' if len(message) > 50 else ''}'")

        try:
            # 1. 物品实体识别
            extracted_items = await self.item_extractor.extract_items(message)
            logger.info(f"识别到的物品: {extracted_items}")

            # 2. 确定响应类型和主要物品
            response_type, primary_item = self._determine_response_type(extracted_items)
            logger.info(f"响应类型: {response_type}, 主要物品: {primary_item}")

            # 3. 处理合成上下文
            crafting_context = await self._handle_crafting_context(
                primary_item, existing_crafting_context
            )
            logger.info(f"合成上下文处理结果: {crafting_context}")

            # 4. 生成响应事件流
            event_stream = self._generate_response_stream(
                message, chat_history_str, response_type, primary_item, crafting_context
            )

            processing_time = time.time() - start_time
            logger.info(f"合成导航消息处理完成，耗时: {processing_time:.2f}秒")

            return response_type, primary_item, crafting_context, event_stream

        except Exception as e:
            logger.error(f"合成导航消息处理失败: {e}")
            # 降级到普通文本模式
            event_stream = self.base_rag_pipeline.process_query(message, chat_history_str)
            return ResponseType.TEXT, None, None, event_stream

    def _determine_response_type(self, extracted_items: List[str]) -> Tuple[ResponseType, Optional[str]]:
        """
        确定响应类型和主要物品

        Args:
            extracted_items: 识别到的物品列表

        Returns:
            Tuple[ResponseType, Optional[str]]: (响应类型, 主要物品名称)
        """
        if extracted_items:
            # 选择最重要的物品作为主要物品
            primary_item = self._select_primary_item(extracted_items)
            return ResponseType.CRAFTING, primary_item
        else:
            return ResponseType.TEXT, None

    def _select_primary_item(self, extracted_items: List[str]) -> str:
        """
        从识别到的物品列表中选择主要物品

        选择策略：
        1. 优先选择更长的物品名称（更具体）
        2. 优先选择工具、武器、装备等成品
        3. 如果都是材料，选择第一个

        Args:
            extracted_items: 识别到的物品列表

        Returns:
            str: 选择的主要物品名称
        """
        if not extracted_items:
            return ""

        if len(extracted_items) == 1:
            return extracted_items[0]

        # 按照优先级排序
        def get_item_priority(item: str) -> tuple:
            """计算物品优先级分数"""
            # 1. 长度分数（更长的物品名称更具体）
            length_score = len(item)

            # 2. 物品类型分数
            type_score = self._get_item_type_priority(item)

            return (type_score, length_score)

        # 按照优先级排序，选择最高优先级的物品
        sorted_items = sorted(extracted_items, key=get_item_priority, reverse=True)
        selected_item = sorted_items[0]

        logger.info(f"从 {extracted_items} 中选择主要物品: {selected_item}")
        return selected_item

    def _get_item_type_priority(self, item: str) -> int:
        """
        根据物品类型获取优先级分数

        Args:
            item: 物品名称

        Returns:
            int: 类型分数，越高优先级越高
        """
        # 武器和工具（最高优先级）
        if any(keyword in item for keyword in ["剑", "镐", "斧", "锹", "锄", "弓", "弩"]):
            return 100

        # 装备（高优先级）
        if any(keyword in item for keyword in ["头盔", "胸甲", "护腿", "靴子", "盔甲"]):
            return 90

        # 特殊物品（中高优先级）
        if any(keyword in item for keyword in ["药水", "附魔", "书", "地图"]):
            return 80

        # 食物（中等优先级）
        if any(keyword in item for keyword in ["面包", "苹果", "肉", "鱼", "蛋糕", "派"]):
            return 70

        # 方块物品（中等优先级）
        if any(keyword in item for keyword in ["石", "木", "玻璃", "砖"]):
            return 60

        # 基础材料（较低优先级）
        if any(keyword in item for keyword in ["锭", "粉", "线", "皮革"]):
            return 50

        # 原材料（最低优先级）
        if item in ["钻石", "铁", "金", "煤炭", "红石"]:
            return 30

        # 默认分数
        return 40

    async def _handle_crafting_context(
        self,
        primary_item: Optional[str],
        existing_context: Optional[Dict[str, Any]]
    ) -> Optional[Dict[str, Any]]:
        """处理合成上下文：生成新树或复用现有树"""
        if not primary_item:
            logger.info(f"主要物品为空，返回现有上下文: {existing_context}")
            return existing_context

        # 首次查询：生成新的合成树
        if not existing_context:
            logger.info(f"首次查询，为物品 '{primary_item}' 生成新的合成树")
            shared_tree = await self.tree_generator.generate_crafting_tree(primary_item)
            if shared_tree:
                crafting_context = {
                    "targetItem": primary_item,
                    "sharedTree": shared_tree,
                    "created_at": time.time()
                }
                logger.info(f"成功生成合成上下文: targetItem={primary_item}, sharedTree存在={bool(shared_tree)}")
                return crafting_context
            else:
                logger.warning(f"合成树生成失败，物品: {primary_item}")
                return None

        # 检查物品是否在现有合成树中
        shared_tree = existing_context.get("sharedTree")
        if self._is_item_in_crafting_tree(primary_item, shared_tree):
            logger.info(f"物品 '{primary_item}' 在现有合成树中，复用现有上下文")
            return existing_context

        logger.info(f"物品 '{primary_item}' 不在现有合成树中，返回None")
        return None

    def _is_item_in_crafting_tree(self, item_name: str, crafting_tree: Optional[Dict[str, Any]]) -> bool:
        """检查指定物品是否在合成树中"""
        if not crafting_tree or not item_name:
            return False

        def search_tree(node: Dict[str, Any]) -> bool:
            node_item_name = node.get("itemName", "")
            if self._normalize_item_name(item_name) == self._normalize_item_name(node_item_name):
                return True

            for child in node.get("children", []):
                if search_tree(child):
                    return True
            return False

        return search_tree(crafting_tree)

    def _normalize_item_name(self, item_name: str) -> str:
        """标准化物品名称用于比较"""
        return item_name.lower().strip().replace(" ", "_") if item_name else ""

    async def _generate_response_stream(
        self,
        message: str,
        chat_history_str: str,
        response_type: ResponseType,
        primary_item: Optional[str],
        crafting_context: Optional[Dict[str, Any]]
    ) -> AsyncIterable[Dict[str, Any]]:
        """生成响应事件流"""
        if response_type == ResponseType.CRAFTING and primary_item and crafting_context:
            async for event in self._generate_crafting_response_stream(
                message, chat_history_str, primary_item, crafting_context
            ):
                yield event
        else:
            async for event in self.base_rag_pipeline.process_query(message, chat_history_str):
                yield event

    async def _generate_crafting_response_stream(
        self,
        message: str,
        chat_history_str: str,
        primary_item: str,
        crafting_context: Dict[str, Any]
    ) -> AsyncIterable[Dict[str, Any]]:
        """生成合成导航响应流"""
        try:
            logger.info(f"开始生成合成导航响应流")
            logger.info(f"参数: message='{message}', primary_item='{primary_item}'")
            logger.info(f"crafting_context类型: {type(crafting_context)}")
            logger.info(f"crafting_context内容: {crafting_context}")

            # 验证 crafting_context 的结构
            if not isinstance(crafting_context, dict):
                raise ValueError(f"crafting_context 应该是字典类型，但收到: {type(crafting_context)}")

            if "targetItem" not in crafting_context:
                raise KeyError(f"crafting_context 缺少 'targetItem' 键，当前键: {list(crafting_context.keys())}")

            # 增强查询以包含物品信息
            enhanced_query = f"{message} {primary_item}"
            logger.info(f"增强查询: '{enhanced_query}'")

            # 获取检索上下文
            context_str, _, _ = await self.base_rag_pipeline.retrieve_and_format_context(
                query=enhanced_query,
                original_query=message
            )

            # 构建合成导航专用的提示
            # 将合成树转换为字符串格式
            shared_tree = crafting_context.get("sharedTree", {})
            crafting_tree_str = json.dumps(shared_tree, ensure_ascii=False, indent=2)

            crafting_prompt = CRAFTING_RESPONSE_PROMPT_TEMPLATE.format(
                target_item=crafting_context["targetItem"],
                identified_item=primary_item,
                crafting_context=crafting_tree_str,
                context=context_str,
                chat_history=chat_history_str,
                query=message
            )
            logger.info(f"合成导航提示已构建，长度: {len(crafting_prompt)}")

            # 复用基础RAG Pipeline的流式响应方法
            async for chunk in self.base_rag_pipeline.stream_llm_response(
                chat_history_str=chat_history_str,
                final_prompt_string=crafting_prompt
            ):
                if chunk:
                    yield {"event": "message", "data": chunk}

            yield {"event": "end", "data": json.dumps({})}

        except Exception as e:
            logger.error(f"合成导航响应生成失败: {e}", exc_info=True)
            yield {"event": "error", "data": json.dumps({"message": f"响应生成失败: {str(e)}"})}
