# import json
# import os
# import re
# import time
# # import random # Not strictly used in this version, can be kept or removed
# import logging
# import traceback
# from bs4 import BeautifulSoup
# from urllib.parse import urljoin, unquote
# from pathlib import Path

# # --- Constants from the original script ---
# WIKI_OUTPUT_FOLDER_GENERAL = './wiki_total'
# RECIPE_OUTPUT_FOLDER = './recipe'
# LOG_FILE = 'crawl_wiki_from_local_html_log.txt'
# MAX_RETRIES = 3
# LOCAL_HTML_FILE = 'item.html'
# WIKI_BASE_URL = "https://zh.minecraft.wiki"
# # --- End Constants ---

# logging.basicConfig(filename=LOG_FILE, level=logging.INFO,
#                     format='%(asctime)s - %(levelname)s - %(message)s',
#                     datefmt='%Y-%m-%d %H:%M:%S')

# def my_log(msg):
#     print(msg)
#     logging.info(msg)

# def sanitize_filename_part(text, max_len=50):
#     """Sanitizes a string to be used as part of a filename."""
#     if not text:
#         return "unknown"
#     text = text.replace(' ', '_')
#     text = re.sub(r'[<>:"/\\|?*]', '_', text)
#     text = re.sub(r'_+', '_', text)
#     if len(text) > max_len:
#         text = text[:max_len]
#     return text.strip('_')

# def extract_wiki_id(wiki_url_or_data_page_attr):
#     """
#     Extracts and sanitizes a wiki ID from a URL or a data-page attribute.
#     """
#     segment = ""
#     if wiki_url_or_data_page_attr.startswith("http"):
#         match = re.search(r'/w/(.+)$', wiki_url_or_data_page_attr)
#         if match:
#             segment = match.group(1)
#             segment = unquote(segment)
#     else:
#         segment = wiki_url_or_data_page_attr

#     segment = segment.replace('/', '_').replace(' ', '_')
#     segment = re.sub(r'[<>:"/\\|?*]', '_', segment)
#     segment = re.sub(r'_+', '_', segment)
#     segment = segment.strip('_')

#     max_filename_len = 150
#     if len(segment) > max_filename_len:
#         my_log(f"Warning: Extracted segment '{segment}' is too long. Truncating.")
#         segment = segment[:max_filename_len]
#     return segment if segment else None

# def clean_text(text):
#     if text:
#         text = text.replace('\xa0', ' ')
#         text = re.sub(r'\s+', ' ', text)
#         return text.strip()
#     return ""

# def extract_item_details_from_slot(slot_span, base_url):
#     """
#     Extracts item name, image URL, and source URL from an inventory slot span.
#     An inventory slot span is typically <span class="invslot">...</span>
#     """
#     item_details = None
#     item_name = None
#     image_url = None
#     source_url = None

#     # Prioritize animated-active if present within the slot
#     active_item_span = slot_span.find('span', class_='invslot-item-image animated-active')
#     # Otherwise, find the first (or only) item image span
#     target_item_span = active_item_span if active_item_span else slot_span.find('span', class_='invslot-item-image')

#     if target_item_span:
#         # Extract Name from title attribute
#         if target_item_span.has_attr('title'):
#             item_name = target_item_span['title']
        
#         # Find link (a) and image (img) tags within the target_item_span
#         a_tag = target_item_span.find('a')
#         if a_tag:
#             if not item_name and a_tag.has_attr('title'): # Fallback for name from <a> tag's title
#                 item_name = a_tag['title']
#             if a_tag.has_attr('href'):
#                 source_url = urljoin(base_url, a_tag['href'])
        
#         img_tag = target_item_span.find('img')
#         if img_tag and img_tag.has_attr('src'):
#             image_url = urljoin(base_url, img_tag['src'])
        
#         # If an item name was successfully extracted, create the details dictionary
#         if item_name:
#             item_details = {"name": clean_text(item_name)}
#             if image_url:
#                 item_details["image_url"] = image_url
#             if source_url:
#                 item_details["source_url"] = source_url
                
#     return item_details

# # --- New Recipe Parsing Function ---
# def extract_and_save_recipes(page_source, base_url, output_folder):
#     """
#     Extracts recipe data from all relevant tables in the HTML source and saves each recipe to a JSON file.
#     """
#     my_log(f"Starting recipe extraction, saving to folder: {output_folder}")
#     Path(output_folder).mkdir(parents=True, exist_ok=True)

#     soup = BeautifulSoup(page_source, 'html.parser')
#     recipe_tables = soup.find_all('table', class_='wikitable', attrs={'data-description': True})

#     if not recipe_tables:
#         my_log("No recipe tables with 'data-description' found. Trying fallback selector...")
#         recipe_tables = soup.select('div.load-page-content table.wikitable.sortable.collapsible')
#         if recipe_tables:
#             my_log(f"Found {len(recipe_tables)} tables using fallback selector.")
#         else:
#             my_log("No recipe tables found using fallback selector either.")
#             return 0

#     total_recipes_saved = 0
#     for table_idx, table in enumerate(recipe_tables):
#         my_log(f"Processing table {table_idx + 1}/{len(recipe_tables)}...")
#         if not table.tbody:
#             my_log(f"  Table {table_idx + 1} has no tbody, skipping.")
#             continue

#         rows = table.tbody.find_all('tr')
#         recipes_in_table = 0
#         for row_idx, row in enumerate(rows):
#             if row_idx == 0 and all(child.name == 'th' for child in row.find_all(recursive=False)):
#                 continue # Skip header row

#             cells = row.find_all(['th', 'td'], recursive=False)

#             if len(cells) == 4: # Standard recipe row
#                 try:
#                     recipe_entry = {}

#                     # 1. 名称 (Name)
#                     name_cell = cells[0]
#                     name_link_tag = name_cell.find('a')
#                     item_name_text = name_link_tag.get_text(strip=True) if name_link_tag else name_cell.get_text(strip=True)
#                     recipe_entry['名称'] = clean_text(item_name_text)

#                     # 2. 材料 (Materials)
#                     materials_cell = cells[1]
#                     material_links = materials_cell.find_all('a')
#                     if material_links:
#                         materials_text_val = ' + '.join([clean_text(link.get_text(strip=True)) for link in material_links])
#                     else:
#                         materials_text_val = clean_text(materials_cell.get_text(strip=True))
#                     recipe_entry['材料'] = materials_text_val
                    
#                     # 3. 合成配方 (Crafting Recipe) - 10 items (9 input + 1 output)
#                     recipe_display_cell = cells[2]
#                     recipe_grid_items = [None] * 10 # Initialize with None for 9 input + 1 output

#                     # Process input grid
#                     input_grid_div = recipe_display_cell.find('span', class_='mcui-input')
#                     if input_grid_div:
#                         input_rows_spans = input_grid_div.find_all('span', class_='mcui-row', recursive=False)
#                         current_slot_idx = 0
#                         for r_span in input_rows_spans:
#                             invslot_spans = r_span.find_all('span', class_='invslot', recursive=False)
#                             for invslot_span in invslot_spans:
#                                 if current_slot_idx < 9:
#                                     recipe_grid_items[current_slot_idx] = extract_item_details_from_slot(invslot_span, base_url)
#                                     current_slot_idx += 1
                    
#                     # Process output slot
#                     output_div = recipe_display_cell.find('span', class_='mcui-output')
#                     if output_div:
#                         # The output item is usually within a <span class="invslot"> or <span class="invslot invslot-large">
#                         output_slot_span = output_div.find(['span', 'div'], class_=lambda x: x and 'invslot' in x.split())
#                         if output_slot_span:
#                              recipe_grid_items[9] = extract_item_details_from_slot(output_slot_span, base_url)
#                         else: # Fallback if the structure is simpler (e.g. item image directly under output div)
#                              recipe_grid_items[9] = extract_item_details_from_slot(output_div, base_url)
                    
#                     recipe_entry['合成配方'] = recipe_grid_items

#                     # 4. 描述 (Description)
#                     description_cell = cells[3]
#                     description_text = clean_text(description_cell.get_text(separator=' ', strip=True))
#                     desc_links_data = []
#                     for a_tag in description_cell.find_all('a'):
#                         link_text = clean_text(a_tag.get_text(strip=True))
#                         link_href = a_tag.get('href')
#                         if link_href:
#                             if link_href.startswith('/'):
#                                 link_href = urljoin(base_url, link_href)
#                             desc_links_data.append({'text': link_text, 'href': link_href})
                    
#                     recipe_entry['描述'] = {
#                         'text': description_text,
#                         'links': desc_links_data
#                     }

#                     # Filename generation
#                     sane_name = sanitize_filename_part(recipe_entry['名称'])
#                     sane_materials = sanitize_filename_part(materials_text_val.replace(' + ', '_')) # Use the actual materials text for filename
#                     recipe_filename = f"recipe_{sane_name}_{sane_materials}.json"
#                     recipe_filepath = Path(output_folder) / recipe_filename

#                     with open(recipe_filepath, 'w', encoding='utf-8') as f_json:
#                         json.dump(recipe_entry, f_json, ensure_ascii=False, indent=4)
#                     total_recipes_saved += 1
#                     recipes_in_table +=1

#                 except Exception as e:
#                     my_log(f"  Error processing row {row_idx} in table {table_idx + 1}: {e}")
#                     my_log(f"  Row HTML: {str(row)[:300]}...")
#                     traceback.print_exc(file=open(LOG_FILE, 'a'))
#         my_log(f"  Finished processing table {table_idx + 1}, saved {recipes_in_table} recipes.")
    
#     my_log(f"Total recipes saved from this page: {total_recipes_saved}")
#     return total_recipes_saved

# if __name__ == "__main__":
#     start_time_main = time.perf_counter()
#     my_log(f"脚本开始执行 - Minecraft Wiki 配方解析器 (从 '{LOCAL_HTML_FILE}' 读取)...")

#     os.makedirs(RECIPE_OUTPUT_FOLDER, exist_ok=True)
#     my_log(f"配方输出目录: {RECIPE_OUTPUT_FOLDER}")

#     if not os.path.exists(LOCAL_HTML_FILE):
#         my_log(f"[致命错误] HTML 文件未找到: {LOCAL_HTML_FILE}")
#         my_log(f"请确保 '{LOCAL_HTML_FILE}' 存在于脚本同目录下并包含配方页面内容。")
#         exit(1)

#     try:
#         my_log(f"正在从本地 HTML 文件读取内容: {LOCAL_HTML_FILE}")
#         with open(LOCAL_HTML_FILE, 'r', encoding='utf-8') as f:
#             page_source_content = f.read()
        
#         if not page_source_content.strip():
#             my_log(f"[错误] HTML 文件 '{LOCAL_HTML_FILE}' 为空.")
#             exit(1)

#         my_log(f"HTML 内容已加载，开始解析配方...")
#         num_saved = extract_and_save_recipes(page_source_content, WIKI_BASE_URL, RECIPE_OUTPUT_FOLDER)
#         my_log(f"从 '{LOCAL_HTML_FILE}' 解析并保存了 {num_saved} 个配方。")

#     except FileNotFoundError:
#         my_log(f"[致命错误] HTML 文件读取时未找到: {LOCAL_HTML_FILE}")
#     except Exception as e_main:
#         my_log(f"主执行流程中发生严重错误: {e_main}")
#         traceback.print_exc(file=open(LOG_FILE, 'a'))
    
#     elapsed_main = time.perf_counter() - start_time_main
#     my_log("-" * 30)
#     my_log(f"脚本执行完毕.")
#     my_log(f"总耗时: {elapsed_main:.2f} 秒 ({elapsed_main/60:.2f} 分钟)")
#     my_log(f"日志文件位于: {LOG_FILE}")
#     my_log(f"配方文件位于: {RECIPE_OUTPUT_FOLDER}")

import json
import os
import re
import time
# import random # Not strictly used in this version, can be kept or removed
import logging
import traceback
from bs4 import BeautifulSoup
from urllib.parse import urljoin, unquote
from pathlib import Path

# --- Constants from the original script ---
WIKI_OUTPUT_FOLDER_GENERAL = './wiki_total'
RECIPE_OUTPUT_FOLDER = './recipe'
LOG_FILE = 'crawl_wiki_from_local_html_log.txt'
MAX_RETRIES = 3
LOCAL_HTML_FILE = 'item.html'
WIKI_BASE_URL = "https://zh.minecraft.wiki"
# --- End Constants ---

logging.basicConfig(filename=LOG_FILE, level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S')

def my_log(msg):
    print(msg)
    logging.info(msg)

def sanitize_filename_part(text, max_len=50):
    """Sanitizes a string to be used as part of a filename."""
    if not text:
        return "unknown"
    text = text.replace(' ', '_')
    text = re.sub(r'[<>:"/\\|?*]', '_', text)
    text = re.sub(r'_+', '_', text)
    if len(text) > max_len:
        text = text[:max_len]
    return text.strip('_')

def extract_wiki_id(wiki_url_or_data_page_attr):
    """
    Extracts and sanitizes a wiki ID from a URL or a data-page attribute.
    """
    segment = ""
    if wiki_url_or_data_page_attr.startswith("http"):
        match = re.search(r'/w/(.+)$', wiki_url_or_data_page_attr)
        if match:
            segment = match.group(1)
            segment = unquote(segment)
    else:
        segment = wiki_url_or_data_page_attr

    segment = segment.replace('/', '_').replace(' ', '_')
    segment = re.sub(r'[<>:"/\\|?*]', '_', segment)
    segment = re.sub(r'_+', '_', segment)
    segment = segment.strip('_')

    max_filename_len = 150
    if len(segment) > max_filename_len:
        my_log(f"Warning: Extracted segment '{segment}' is too long. Truncating.")
        segment = segment[:max_filename_len]
    return segment if segment else None

def clean_text(text):
    if text:
        text = text.replace('\xa0', ' ')
        text = re.sub(r'\s+', ' ', text)
        return text.strip()
    return ""

def extract_item_details_from_slot(slot_span, base_url):
    """
    Extracts item name, image URL, source URL, and stack size from an inventory slot span.
    An inventory slot span is typically <span class="invslot">...</span>
    """
    item_details = None
    item_name = None
    image_url = None
    source_url = None
    stack_size = None # Initialize stack_size

    # Prioritize animated-active if present within the slot
    active_item_span = slot_span.find('span', class_='invslot-item-image animated-active')
    # Otherwise, find the first (or only) item image span
    target_item_span = active_item_span if active_item_span else slot_span.find('span', class_='invslot-item-image')

    if target_item_span:
        # 1. Item Name: Primary source is the title attribute of the target_item_span itself.
        if target_item_span.has_attr('title'):
            item_name = target_item_span['title']

        # 2. Image URL and Source URL:
        #    Often nested within <span typeof="mw:File"><a><img></a></span> or similar.
        main_link_container = target_item_span.find('span', attrs={'typeof': 'mw:File'})
        link_tag_for_item = None
        img_tag_found = None

        if main_link_container:
            link_tag_for_item = main_link_container.find('a')
        
        # Fallback if no mw:File structure or if link_tag_for_item not found within it
        if not link_tag_for_item:
            # Try to find an 'a' tag that is a direct child of target_item_span and does NOT contain a stacksize span
            # (to avoid picking a generic link that only holds the stacksize).
            possible_links = target_item_span.find_all('a', recursive=False) # Direct children first
            if not possible_links: # If no direct children, search recursively but be careful
                possible_links = target_item_span.find_all('a')

            for pl in possible_links:
                if not pl.find('span', class_='invslot-stacksize'): # This 'a' is not just for stacksize
                    link_tag_for_item = pl
                    break
            if not link_tag_for_item and possible_links: # If all 'a' tags had stacksize, pick first one as last resort
                link_tag_for_item = possible_links[0]


        if link_tag_for_item:
            if link_tag_for_item.has_attr('href'):
                # Only set source_url if this link is not solely for a stack size display of a different item
                # A bit heuristic: if item_name is already set from target_item_span,
                # and this link's title is different and more generic, be wary.
                # However, the href is usually the defining part.
                source_url = urljoin(base_url, link_tag_for_item['href'])

            link_title = link_tag_for_item.get('title')
            if link_title:
                cleaned_link_title = clean_text(link_title)
                # Refine item_name if the link's title is more specific or if item_name is missing.
                # Avoid generic titles like "File" or if it's clearly a category link for stack size.
                if (not item_name or len(cleaned_link_title) > len(clean_text(item_name))) and \
                   cleaned_link_title.lower() not in ["file", "文件"] and \
                   not (link_tag_for_item.find('span', class_='invslot-stacksize') and clean_text(item_name) != cleaned_link_title) :
                    item_name = link_title # Use original case from title

            img_tag_found = link_tag_for_item.find('img')
        
        if not img_tag_found: # If img wasn't in a link_tag_for_item, search directly in target_item_span
            img_tag_found = target_item_span.find('img')

        if img_tag_found and img_tag_found.has_attr('src'):
            image_url = urljoin(base_url, img_tag_found['src'])
        
        # 3. Stack Size
        stack_size_span = target_item_span.find('span', class_='invslot-stacksize')
        if stack_size_span:
            size_text = clean_text(stack_size_span.get_text(strip=True))
            try:
                stack_size_val = int(size_text)
                if stack_size_val > 0: # Ensure it's a positive number
                    stack_size = stack_size_val
            except ValueError:
                my_log(f"Warning: Could not parse stack size '{size_text}' as integer for item '{item_name}'.")

        # Final assembly of item_details
        if item_name: # An item_name is essential
            item_details = {"name": clean_text(item_name)} # Ensure name is cleaned
            if image_url:
                item_details["image_url"] = image_url
            if source_url:
                item_details["source_url"] = source_url
            if stack_size is not None:
                item_details["stack_size"] = stack_size
                
    return item_details

# --- Recipe Parsing Function (extract_and_save_recipes) ---
def extract_and_save_recipes(page_source, base_url, output_folder):
    """
    Extracts recipe data from all relevant tables in the HTML source and saves each recipe to a JSON file.
    """
    my_log(f"Starting recipe extraction, saving to folder: {output_folder}")
    Path(output_folder).mkdir(parents=True, exist_ok=True)

    soup = BeautifulSoup(page_source, 'html.parser')
    recipe_tables = soup.find_all('table', class_='wikitable', attrs={'data-description': True})

    if not recipe_tables:
        my_log("No recipe tables with 'data-description' found. Trying fallback selector...")
        recipe_tables = soup.select('div.load-page-content table.wikitable.sortable.collapsible')
        if recipe_tables:
            my_log(f"Found {len(recipe_tables)} tables using fallback selector.")
        else:
            my_log("No recipe tables found using fallback selector either.")
            return 0

    total_recipes_saved = 0
    for table_idx, table in enumerate(recipe_tables):
        my_log(f"Processing table {table_idx + 1}/{len(recipe_tables)}...")
        if not table.tbody:
            my_log(f"  Table {table_idx + 1} has no tbody, skipping.")
            continue

        rows = table.tbody.find_all('tr')
        recipes_in_table = 0
        for row_idx, row in enumerate(rows):
            if row_idx == 0 and all(child.name == 'th' for child in row.find_all(recursive=False)):
                continue # Skip header row

            cells = row.find_all(['th', 'td'], recursive=False)

            if len(cells) == 4: # Standard recipe row
                try:
                    recipe_entry = {}

                    # 1. 名称 (Name)
                    name_cell = cells[0]
                    name_link_tag = name_cell.find('a')
                    item_name_text = name_link_tag.get_text(strip=True) if name_link_tag else name_cell.get_text(strip=True)
                    recipe_entry['名称'] = clean_text(item_name_text)

                    # 2. 材料 (Materials)
                    materials_cell = cells[1]
                    material_links = materials_cell.find_all('a')
                    if material_links:
                        materials_text_val = ' + '.join([clean_text(link.get_text(strip=True)) for link in material_links])
                    else:
                        materials_text_val = clean_text(materials_cell.get_text(strip=True))
                    recipe_entry['材料'] = materials_text_val
                    
                    # 3. 合成配方 (Crafting Recipe) - 10 items (9 input + 1 output)
                    recipe_display_cell = cells[2]
                    recipe_grid_items = [None] * 10 # Initialize with None for 9 input + 1 output

                    # Process input grid
                    input_grid_div = recipe_display_cell.find('span', class_='mcui-input')
                    if input_grid_div:
                        input_rows_spans = input_grid_div.find_all('span', class_='mcui-row', recursive=False)
                        current_slot_idx = 0
                        for r_span in input_rows_spans:
                            invslot_spans = r_span.find_all('span', class_='invslot', recursive=False)
                            for invslot_span_tag in invslot_spans: # Renamed to avoid conflict
                                if current_slot_idx < 9:
                                    recipe_grid_items[current_slot_idx] = extract_item_details_from_slot(invslot_span_tag, base_url)
                                    current_slot_idx += 1
                    
                    # Process output slot
                    output_div = recipe_display_cell.find('span', class_='mcui-output')
                    if output_div:
                        # The output item is usually within a <span class="invslot"> or <span class="invslot invslot-large">
                        output_slot_span_tag = output_div.find(['span', 'div'], class_=lambda x: x and 'invslot' in x.split()) # Renamed
                        if output_slot_span_tag:
                             recipe_grid_items[9] = extract_item_details_from_slot(output_slot_span_tag, base_url)
                        else: # Fallback if the structure is simpler (e.g. item image directly under output div)
                             recipe_grid_items[9] = extract_item_details_from_slot(output_div, base_url) # Pass output_div if no invslot found
                    
                    recipe_entry['合成配方'] = recipe_grid_items

                    # 4. 描述 (Description)
                    description_cell = cells[3]
                    description_text = clean_text(description_cell.get_text(separator=' ', strip=True))
                    desc_links_data = []
                    for a_tag_desc in description_cell.find_all('a'): # Renamed
                        link_text = clean_text(a_tag_desc.get_text(strip=True))
                        link_href = a_tag_desc.get('href')
                        if link_href:
                            if link_href.startswith('/'):
                                link_href = urljoin(base_url, link_href)
                            desc_links_data.append({'text': link_text, 'href': link_href})
                    
                    recipe_entry['描述'] = {
                        'text': description_text,
                        'links': desc_links_data
                    }

                    # Filename generation
                    sane_name = sanitize_filename_part(recipe_entry['名称'])
                    sane_materials = sanitize_filename_part(materials_text_val.replace(' + ', '_')) 
                    recipe_filename = f"recipe_{sane_name}_{sane_materials}.json"
                    recipe_filepath = Path(output_folder) / recipe_filename

                    with open(recipe_filepath, 'w', encoding='utf-8') as f_json:
                        json.dump(recipe_entry, f_json, ensure_ascii=False, indent=4)
                    total_recipes_saved += 1
                    recipes_in_table +=1

                except Exception as e:
                    my_log(f"  Error processing row {row_idx} in table {table_idx + 1}: {e}")
                    my_log(f"  Row HTML: {str(row)[:300]}...")
                    traceback.print_exc(file=open(LOG_FILE, 'a'))
        my_log(f"  Finished processing table {table_idx + 1}, saved {recipes_in_table} recipes.")
    
    my_log(f"Total recipes saved from this page: {total_recipes_saved}")
    return total_recipes_saved

if __name__ == "__main__":
    start_time_main = time.perf_counter()
    my_log(f"脚本开始执行 - Minecraft Wiki 配方解析器 (从 '{LOCAL_HTML_FILE}' 读取)...")

    os.makedirs(RECIPE_OUTPUT_FOLDER, exist_ok=True)
    my_log(f"配方输出目录: {RECIPE_OUTPUT_FOLDER}")

    if not os.path.exists(LOCAL_HTML_FILE):
        my_log(f"[致命错误] HTML 文件未找到: {LOCAL_HTML_FILE}")
        my_log(f"请确保 '{LOCAL_HTML_FILE}' 存在于脚本同目录下并包含配方页面内容。")
        exit(1)

    try:
        my_log(f"正在从本地 HTML 文件读取内容: {LOCAL_HTML_FILE}")
        with open(LOCAL_HTML_FILE, 'r', encoding='utf-8') as f:
            page_source_content = f.read()
        
        if not page_source_content.strip():
            my_log(f"[错误] HTML 文件 '{LOCAL_HTML_FILE}' 为空.")
            exit(1)

        my_log(f"HTML 内容已加载，开始解析配方...")
        num_saved = extract_and_save_recipes(page_source_content, WIKI_BASE_URL, RECIPE_OUTPUT_FOLDER)
        my_log(f"从 '{LOCAL_HTML_FILE}' 解析并保存了 {num_saved} 个配方。")

    except FileNotFoundError:
        my_log(f"[致命错误] HTML 文件读取时未找到: {LOCAL_HTML_FILE}")
    except Exception as e_main:
        my_log(f"主执行流程中发生严重错误: {e_main}")
        traceback.print_exc(file=open(LOG_FILE, 'a'))
    
    elapsed_main = time.perf_counter() - start_time_main
    my_log("-" * 30)
    my_log(f"脚本执行完毕.")
    my_log(f"总耗时: {elapsed_main:.2f} 秒 ({elapsed_main/60:.2f} 分钟)")
    my_log(f"日志文件位于: {LOG_FILE}")
    my_log(f"配方文件位于: {RECIPE_OUTPUT_FOLDER}")