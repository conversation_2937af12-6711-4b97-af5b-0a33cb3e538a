import argparse
import json
import logging
import os
import random
import sys
from pathlib import Path

from langchain_core.documents.base import Document
from tqdm import tqdm
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(level=logging.INFO)

load_dotenv()

# Add scripts directory to PATH for importing modules
root_dir = Path(__file__).resolve().parent.parent
sys.path.append(str(root_dir))

from build_chroma_db import ModDataLoader
from helpers.qra_pipeline_single_doc import generate_qra


def load_documents(input_dir: Path, mod_start: int, mod_end: int) -> list[tuple[int, list[Document]]]:
  doc_batches = []
  cwd = os.getcwd()
  os.chdir(input_dir)
  mod_dir = Path('mode')
  loader = ModDataLoader()
  for i in range(mod_start, mod_end + 1):
    mod_file = mod_dir / f'mode{i}.json'
    if mod_file.exists():
      doc_batches.append((i, loader.load_single_mod(mod_file)))
  os.chdir(cwd)
  return doc_batches


if __name__ == '__main__':
  parser = argparse.ArgumentParser(description='QRA Pipeline for RAG Documents')
  parser.add_argument('--model_name', type=str, required=True, help='Name of the model to use')
  parser.add_argument('--data_dir', type=str, required=True, help='Root directory of benchmark data, should including the following subdirectories: bbs, itemnew, mode, post')
  parser.add_argument('--json_idx', type=int, default=0, help='Index of the JSON files to process')
  parser.add_argument('--limit', type=int, default=10, help='Limit on the number of items in an article to process')
  parser.add_argument('--seed', type=int, default=42, help='Random seed for reproducibility')
  parser.add_argument('--start', type=int, default=1, help='Start index of the mod files to process, inclusively')
  parser.add_argument('--end', type=int, default=1, help='End index of the mod files to process, inclusively')

  args = parser.parse_args()

  random.seed(args.seed)

  if not os.path.exists(args.data_dir):
    raise FileNotFoundError(f"Data directory {args.data_dir} does not exist.")

  data_dir = Path(args.data_dir)
  doc_batches = load_documents(data_dir, args.start, args.end)

  qra_dir = data_dir / f'qra/version{args.json_idx}'
  os.makedirs(qra_dir, exist_ok=True)
  for i, batch in tqdm(doc_batches, desc='Generating QRAs', total=len(doc_batches)):
    generate_qra(
      model_name=args.model_name,
      outline=json.dumps([doc.page_content for doc in random.choices(batch, k=args.limit)], ensure_ascii=False),
      output_path=qra_dir / f'qra{i}.json',
    )
