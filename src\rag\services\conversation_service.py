"""
对话服务模块

此模块提供与对话管理相关的核心业务逻辑服务。
它封装了对话管理、消息处理和数据库交互等功能。
"""

from sqlalchemy.orm import Session
from datetime import datetime
import json
from typing import AsyncIterable, Dict, Any, List, Tuple, Optional
from fastapi import HTTPException, status

from ..database import Conversation, Message, ConversationType, MessageType, CraftingContext
from ..rag_pipeline.pipeline import RAGPipeline
from ..crafting import CraftingRAGPipeline

class ConversationService:
    """
    对话服务类，提供对话管理功能
    """

    def __init__(
        self,
        db: Session,
        rag_pipeline: Optional[RAGPipeline] = None,
        crafting_rag_pipeline: Optional[CraftingRAGPipeline] = None,
    ):
        """
        初始化对话服务

        Args:
            db: 数据库会话
            rag_pipeline: RAG Pipeline实例，可选，用于处理RAG查询
            crafting_rag_pipeline: 合成导航RAG Pipeline实例，可选，用于处理合成导航查询
        """
        self.db = db
        self.rag_pipeline = rag_pipeline
        self.crafting_rag_pipeline = crafting_rag_pipeline

    async def handle_new_message(
        self, conversation_id: int, user_id: int, user_content: str
    ) -> AsyncIterable[Dict[str, Any]]:
        """
        处理现有对话中的新消息，返回流式响应

        Args:
            conversation_id: 对话ID
            user_id: 用户ID
            user_content: 用户消息内容

        Yields:
            Dict[str, Any]: 包含事件类型和数据的字典，用于SSE响应
        """
        # 检查对话是否存在且属于当前用户
        conversation = self.db.query(Conversation).filter(
            Conversation.id == conversation_id, Conversation.user_id == user_id
        ).first()

        if not conversation:
            yield {"event": "error", "data": "对话不存在"}
            return

        # 保存用户消息
        user_message = Message(
            conversation_id=conversation_id,
            role="user",
            content=user_content
        )
        self.db.add(user_message)

        # 更新对话的最后更新时间
        conversation.updated_at = datetime.now()
        self.db.commit()
        self.db.refresh(user_message)

        try:
            # 获取对话历史记录
            chat_history = await self._get_chat_history(conversation_id)

            # 根据对话类型选择处理方式
            if conversation.type == ConversationType.SYNTHETIC_NAVIGATION:
                # 合成导航对话
                async for event in self._handle_crafting_conversation(
                    conversation_id, user_content, chat_history
                ):
                    yield event
            else:
                # 普通对话
                async for event in self._handle_normal_conversation(
                    user_content, chat_history
                ):
                    yield event

            # 注意：不在这里保存助手消息，而是由路由层的后台任务处理
            # 这样可以避免重复保存，并且不阻塞响应流

        except Exception as e:
            print(f"处理流时出错: {e}")
            # 发送错误事件
            yield {"event": "error", "data": json.dumps({"message": f"处理回复时发生错误: {str(e)}"})}

        # 注意：不要在异步生成器中使用return语句返回值

    async def handle_new_conversation_message(
        self, user_id: int, user_content: str, conversation_type: ConversationType = ConversationType.NORMAL
    ) -> Tuple[int, str, AsyncIterable[Dict[str, Any]]]:
        """
        创建新对话并处理第一条消息，返回对话ID、初始标题和流式响应

        Args:
            user_id: 用户ID
            user_content: 用户消息内容
            conversation_type: 对话类型，默认为普通对话

        Returns:
            Tuple[int, str, AsyncIterable[Dict[str, Any]]]: 包含对话ID、初始标题和事件生成器的元组
        """
        # 根据对话类型选择处理方式
        if conversation_type == ConversationType.SYNTHETIC_NAVIGATION:
            # 合成导航对话
            if self.crafting_rag_pipeline is None:
                initial_title = user_content[:15] + "..." if len(user_content) > 15 else user_content
                async def error_event_generator():
                    yield {"event": "error", "data": json.dumps({"message": "合成导航Pipeline未初始化"})}
                    yield {"event": "end", "data": ""}
                event_generator_stream = error_event_generator()
            else:
                initial_title = user_content[:15] + "..." if len(user_content) > 15 else user_content
                # 创建合成导航事件流（稍后实现）
                event_generator_stream = self._create_crafting_conversation_stream(user_content)
        else:
            # 普通对话
            if self.rag_pipeline is None:
                initial_title = user_content[:15] + "..." if len(user_content) > 15 else user_content
                async def simple_event_generator():
                    yield {"event": "error", "data": json.dumps({"message": "RAG Pipeline未初始化，无法处理查询"})}
                    yield {"event": "end", "data": ""}
                event_generator_stream = simple_event_generator()
            else:
                initial_title, event_generator_stream = await self.rag_pipeline.process_new_conversation(user_content)

        # 创建新对话，使用临时标题
        new_conversation = Conversation(
            user_id=user_id,
            title=initial_title,
            type=conversation_type
        )
        self.db.add(new_conversation)
        self.db.commit()
        self.db.refresh(new_conversation)
        conversation_id = new_conversation.id

        # 保存用户消息
        user_message = Message(
            conversation_id=conversation_id,
            role="user",
            content=user_content
        )
        self.db.add(user_message)
        self.db.commit()

        # 包装事件生成器，添加conversation_start事件和处理后台任务
        async def wrapped_event_generator() -> AsyncIterable[Dict[str, Any]]:
            full_response = ""

            # 先发送包含新对话ID、初始标题和对话类型的事件
            # 注意：使用驼峰命名 conversationId 以匹配前端期望的格式
            yield {"event": "conversation_start", "data": json.dumps({
                "conversationId": conversation_id,
                "title": initial_title,
                "type": conversation_type.value
            })}

            # 转发Pipeline生成的事件
            async for event in event_generator_stream:
                yield event

                # 根据事件类型执行不同操作
                if event["event"] == "message":
                    # 处理消息内容
                    if isinstance(event["data"], str):
                        full_response += event["data"]
                    elif isinstance(event["data"], dict):
                        # 处理JSON格式的消息数据
                        chunk_data = event["data"]
                        if "chunk" in chunk_data:
                            full_response += chunk_data["chunk"]
                elif event["event"] == "end" and full_response:
                    # 流结束，但不在这里保存助手消息，而是由路由层的后台任务处理
                    pass
                elif event["event"] == "title_update":
                    # 标题更新事件，不在这里处理，而是由路由层的后台任务处理
                    # 这样可以避免重复更新，并且不阻塞响应流
                    pass

        return conversation_id, initial_title, wrapped_event_generator()

    async def handle_dump_message(self, user_content: str) -> dict:
        async for event in self.rag_pipeline.process_query(user_content, dump=True):
            if event["event"] == "dump":
                return json.loads(event["data"])
        return {"event": "error", "data": "No dump event received"}

    async def save_assistant_message(self, conversation_id: int, content: str) -> None:
        """
        保存助手消息到数据库

        Args:
            conversation_id: 对话ID
            content: 助手消息内容
        """
        try:
            assistant_message = Message(
                conversation_id=conversation_id,
                role="assistant",
                content=content
            )
            self.db.add(assistant_message)
            self.db.commit()
            print(f"助手消息 (对话ID: {conversation_id}) 已保存。")
        except Exception as e:
            print(f"保存助手消息失败 (对话ID: {conversation_id}): {e}")
            self.db.rollback()
            raise

    async def update_conversation_title(self, conversation_id: int, title: str) -> None:
        """
        更新对话标题

        Args:
            conversation_id: 对话ID
            title: 新标题
        """
        try:
            conversation = self.db.query(Conversation).filter(Conversation.id == conversation_id).first()
            if conversation:
                conversation.title = title
                self.db.commit()
                print(f"对话 {conversation_id} 标题已更新为 '{title}'")
            else:
                print(f"更新标题失败: 对话 {conversation_id} 不存在")
        except Exception as e:
            print(f"更新标题失败 (对话ID: {conversation_id}): {e}")
            self.db.rollback()
            raise

    async def get_conversation(self, conversation_id: int, user_id: int):
        """
        获取单个对话详情

        Args:
            conversation_id: 对话ID
            user_id: 用户ID

        Returns:
            Conversation: 对话对象，如果是合成导航对话则包含合成上下文
        """
        conversation = self.db.query(Conversation).filter(
            Conversation.id == conversation_id, Conversation.user_id == user_id
        ).first()

        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="对话不存在"
            )

        # 如果是合成导航对话，获取合成上下文
        if conversation.type == ConversationType.SYNTHETIC_NAVIGATION:
            crafting_context = await self.get_crafting_context(conversation_id)
            # 将合成上下文附加到对话对象上（用于响应模型）
            conversation.craftingContext = crafting_context
        else:
            conversation.craftingContext = None

        return conversation

    async def get_user_conversations(self, user_id: int) -> List[Conversation]:
        """
        获取用户的所有对话

        Args:
            user_id: 用户ID

        Returns:
            List[Conversation]: 对话列表
        """
        conversations = self.db.query(Conversation).filter(
            Conversation.user_id == user_id
        ).order_by(Conversation.updated_at.desc()).all()

        return conversations

    async def create_conversation(self, user_id: int, title: str, conversation_type: ConversationType = ConversationType.NORMAL) -> Conversation:
        """
        创建新对话

        Args:
            user_id: 用户ID
            title: 对话标题
            conversation_type: 对话类型，默认为普通对话

        Returns:
            Conversation: 创建的对话对象
        """
        new_conversation = Conversation(
            user_id=user_id,
            title=title,
            type=conversation_type
        )
        self.db.add(new_conversation)
        self.db.commit()
        self.db.refresh(new_conversation)
        return new_conversation

    async def update_conversation(self, conversation_id: int, user_id: int, title: str, conversation_type: Optional[ConversationType] = None) -> Conversation:
        """
        更新对话标题和类型

        Args:
            conversation_id: 对话ID
            user_id: 用户ID
            title: 新标题
            conversation_type: 新的对话类型，可选

        Returns:
            Conversation: 更新后的对话对象
        """
        conversation = self.db.query(Conversation).filter(
            Conversation.id == conversation_id, Conversation.user_id == user_id
        ).first()

        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="对话不存在"
            )

        conversation.title = title
        if conversation_type is not None:
            conversation.type = conversation_type
        self.db.commit()
        self.db.refresh(conversation)
        return conversation

    async def delete_conversation(self, conversation_id: int, user_id: int) -> None:
        """
        删除对话

        Args:
            conversation_id: 对话ID
            user_id: 用户ID
        """
        conversation = self.db.query(Conversation).filter(
            Conversation.id == conversation_id, Conversation.user_id == user_id
        ).first()

        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="对话不存在"
            )

        self.db.delete(conversation)
        self.db.commit()

    # 私有辅助方法
    async def _get_chat_history(self, conversation_id: int) -> str:
        """
        获取对话历史记录

        Args:
            conversation_id: 对话ID

        Returns:
            str: 格式化的对话历史字符串
        """
        messages = self.db.query(Message).filter(
            Message.conversation_id == conversation_id
        ).order_by(Message.created_at).limit(10)  # 限制历史记录条数，避免上下文过长

        chat_history = []
        for msg in messages:
            if msg.role == "user":
                chat_history.append(f"用户: {msg.content}")
            else:
                chat_history.append(f"助手: {msg.content}")

        return "\n".join(chat_history)

    # 合成导航相关方法
    async def _handle_normal_conversation(
        self, user_content: str, chat_history: str
    ) -> AsyncIterable[Dict[str, Any]]:
        """
        处理普通对话

        Args:
            user_content: 用户消息内容
            chat_history: 对话历史

        Yields:
            Dict[str, Any]: 事件数据
        """
        if self.rag_pipeline is None:
            yield {"event": "error", "data": json.dumps({"message": "RAG Pipeline未初始化，无法处理查询"})}
            return

        async for event in self.rag_pipeline.process_query(user_content, chat_history):
            yield event

    async def _handle_crafting_conversation(
        self, conversation_id: int, user_content: str, chat_history: str
    ) -> AsyncIterable[Dict[str, Any]]:
        """
        处理合成导航对话

        Args:
            conversation_id: 对话ID
            user_content: 用户消息内容
            chat_history: 对话历史

        Yields:
            Dict[str, Any]: 事件数据
        """
        if self.crafting_rag_pipeline is None:
            yield {"event": "error", "data": json.dumps({"message": "合成导航Pipeline未初始化"})}
            return

        try:
            # 获取现有的合成上下文
            existing_context = await self.get_crafting_context(conversation_id)

            # 使用合成导航Pipeline处理消息
            response_type, primary_item, crafting_context, event_stream = await self.crafting_rag_pipeline.process_crafting_message(
                message=user_content,
                chat_history_str=chat_history,
                existing_crafting_context=existing_context
            )

            # 如果生成了新的合成上下文（即没有现有上下文时），保存到数据库
            if crafting_context and not existing_context:
                await self.save_crafting_context(conversation_id, crafting_context)

            # 转发事件流，并添加message_complete事件
            full_response = ""
            async for event in event_stream:
                # 收集完整响应内容
                if event["event"] == "message":
                    if isinstance(event["data"], str):
                        full_response += event["data"]
                    elif isinstance(event["data"], dict):
                        chunk_data = event["data"]
                        if "chunk" in chunk_data:
                            full_response += chunk_data["chunk"]
                    # 转发消息事件
                    yield event
                elif event["event"] == "end":
                    # 在end事件之前发送message_complete事件
                    message_complete_data = {
                        "type": response_type.value,
                    }
                    if primary_item:
                        message_complete_data["item_name"] = primary_item

                    yield {"event": "message_complete", "data": json.dumps(message_complete_data)}

                    # 然后发送原始的end事件
                    yield event
                else:
                    # 转发其他事件
                    yield event

        except Exception as e:
            print(f"处理合成导航对话失败: {e}")
            yield {"event": "error", "data": json.dumps({"message": f"处理合成导航对话失败: {str(e)}"})}

    async def _create_crafting_conversation_stream(self, user_content: str) -> AsyncIterable[Dict[str, Any]]:
        """
        创建合成导航对话的事件流（用于新对话）

        Args:
            user_content: 用户消息内容

        Yields:
            Dict[str, Any]: 事件数据
        """
        if self.crafting_rag_pipeline is None:
            yield {"event": "error", "data": json.dumps({"message": "合成导航Pipeline未初始化"})}
            yield {"event": "end", "data": ""}
            return

        try:
            # 处理首条消息
            response_type, primary_item, crafting_context, event_stream = await self.crafting_rag_pipeline.process_crafting_message(
                message=user_content,
                chat_history_str="",
                existing_crafting_context=None
            )

            # 如果生成了合成上下文，发送crafting_context事件
            if crafting_context:
                yield {"event": "crafting_context", "data": json.dumps(crafting_context)}

            # 转发事件流
            full_response = ""
            async for event in event_stream:
                # 收集完整响应内容
                if event["event"] == "message":
                    if isinstance(event["data"], str):
                        full_response += event["data"]
                    elif isinstance(event["data"], dict):
                        chunk_data = event["data"]
                        if "chunk" in chunk_data:
                            full_response += chunk_data["chunk"]
                    # 转发消息事件
                    yield event
                elif event["event"] == "end":
                    # 在end事件之前发送message_complete事件
                    message_complete_data = {
                        "type": response_type.value,
                    }
                    if primary_item:
                        message_complete_data["item_name"] = primary_item

                    yield {"event": "message_complete", "data": json.dumps(message_complete_data)}

                    # 然后发送原始的end事件
                    yield event
                else:
                    # 转发其他事件
                    yield event

        except Exception as e:
            print(f"创建合成导航对话流失败: {e}")
            yield {"event": "error", "data": json.dumps({"message": f"创建合成导航对话流失败: {str(e)}"})}
            yield {"event": "end", "data": ""}

    # 合成上下文管理方法
    async def get_crafting_context(self, conversation_id: int) -> Optional[Dict[str, Any]]:
        """
        获取对话的合成上下文

        Args:
            conversation_id: 对话ID

        Returns:
            Optional[Dict[str, Any]]: 合成上下文数据，如果不存在则返回None
        """
        try:
            crafting_context = self.db.query(CraftingContext).filter(
                CraftingContext.conversation_id == conversation_id
            ).first()

            if crafting_context:
                return {
                    "targetItem": crafting_context.target_item,
                    "sharedTree": crafting_context.shared_tree,
                    "created_at": crafting_context.created_at.timestamp()
                }
            return None
        except Exception as e:
            print(f"获取合成上下文失败 (对话ID: {conversation_id}): {e}")
            return None

    async def save_crafting_context(self, conversation_id: int, crafting_context: Dict[str, Any]) -> None:
        """
        保存或更新对话的合成上下文

        Args:
            conversation_id: 对话ID
            crafting_context: 合成上下文数据
        """
        try:
            # 检查是否已存在合成上下文
            existing_context = self.db.query(CraftingContext).filter(
                CraftingContext.conversation_id == conversation_id
            ).first()

            if existing_context:
                # 更新现有上下文
                existing_context.target_item = crafting_context["targetItem"]
                existing_context.shared_tree = crafting_context["sharedTree"]
                existing_context.updated_at = datetime.now()
            else:
                # 创建新的合成上下文
                new_context = CraftingContext(
                    conversation_id=conversation_id,
                    target_item=crafting_context["targetItem"],
                    shared_tree=crafting_context["sharedTree"]
                )
                self.db.add(new_context)

            self.db.commit()
            print(f"合成上下文已保存 (对话ID: {conversation_id})")

        except Exception as e:
            print(f"保存合成上下文失败 (对话ID: {conversation_id}): {e}")
            self.db.rollback()
            raise

    async def save_assistant_message_with_type(
        self, conversation_id: int, content: str, message_type: MessageType = MessageType.TEXT, item_name: Optional[str] = None
    ) -> None:
        """
        保存助手消息到数据库（支持消息类型和物品名称）

        Args:
            conversation_id: 对话ID
            content: 助手消息内容
            message_type: 消息类型
            item_name: 物品名称（仅crafting类型消息使用）
        """
        try:
            assistant_message = Message(
                conversation_id=conversation_id,
                role="assistant",
                type=message_type,
                content=content,
                item_name=item_name
            )
            self.db.add(assistant_message)
            self.db.commit()
            print(f"助手消息已保存 (对话ID: {conversation_id}, 类型: {message_type.value})")
        except Exception as e:
            print(f"保存助手消息失败 (对话ID: {conversation_id}): {e}")
            self.db.rollback()
            raise