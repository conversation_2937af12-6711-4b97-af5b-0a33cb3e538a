import json
from concurrent.futures import ThreadPoolExecutor
import requests
from datetime import datetime
from bs4 import BeautifulSoup
import csv
from typing import List, Dict
import re
import time
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

class ReplyData:
    def __init__(
        self,
        user_id: str,
        username: str,
        user_level: int,
        comment_id: str,
        post_time: str,
        time_range: str,
        upvotes: int,
        content_text: str,
        report: int
    ):
        self.user_id = user_id
        self.username = username
        self.user_level = user_level
        self.comment_id = comment_id
        self.post_time = post_time
        self.time_range = time_range
        self.upvotes = upvotes
        self.content_text = content_text
        self.report = report

class CommentData:
    def __init__(
        self,
        user_id: str,
        username: str,
        user_level: int,
        floor: str,
        comment_id: str,
        post_time: str,
        time_range: str,
        upvotes: int,
        downvotes: int,
        replies_count: int,
        content_text: str,
        content_links: List[str],
        image_urls: List[str],
        is_abnormal_time: bool = False,
        replies: List['ReplyData'] = None
    ):
        self.user_id = user_id
        self.username = username
        self.user_level = user_level
        self.floor = floor
        self.comment_id = comment_id
        self.post_time = post_time
        self.time_range = time_range
        self.upvotes = upvotes
        self.downvotes = downvotes
        self.replies_count = replies_count
        self.content_text = content_text
        self.content_links = content_links
        self.image_urls = image_urls
        self.is_abnormal_time = is_abnormal_time
        self.replies = replies or []

def filter_links(links: List[str]) -> List[str]:
    valid_links = []
    for link in links:
        if link.startswith("javascript:"):
            continue
        if re.match(r"^(https?:)?\/\/|^\/", link):
            valid_links.append(link)
    return valid_links

def normalize_protocol(link: str) -> str:
    if link.startswith("//"):
        return f"https:{link}" if "mcmod.cn" in link else f"http:{link}"
    return link

def clean_text(text: str) -> str:
    text = re.sub(r'\s+', ' ', text)  # Replace multiple spaces with a single space
    text = re.sub(r'\n+', ' ', text)  # Replace newlines with a space
    text = re.sub(r'[^\w\s]', '', text)  # Remove special characters
    return text.strip()

def deduplicate_links(links: List[str]) -> List[str]:
    seen = set()
    unique_links = []
    for link in links:
        normalized = link.rstrip("/")
        if normalized not in seen:
            seen.add(normalized)
            unique_links.append(link)
    return unique_links

def clean_comment(raw_comment: Dict) -> CommentData:
    user = raw_comment.get("user", {})
    time_data = raw_comment.get("time", {})
    post_time = time_data.get("source", "")
    is_abnormal = False
    try:
        dt = datetime.strptime(post_time, "%Y-%m-%d %H:%M:%S")
        if dt > datetime.now():
            is_abnormal = True
    except ValueError:
        post_time = "1970-01-01 00:00:00"
        is_abnormal = True
    content = raw_comment.get("content", "")
    soup = BeautifulSoup(content, "html.parser")
    content_text = "\n".join([p.get_text() for p in soup.find_all("p")])
    links = [a["href"] for a in soup.find_all("a", href=True)]
    valid_links = filter_links(links)
    normalized_links = [normalize_protocol(link) for link in valid_links]
    dedup_links = deduplicate_links(normalized_links)
    image_urls = [img["src"] for img in soup.find_all("img", src=True)]
    attitude = raw_comment.get("attitude", {})
    reply_count = int(raw_comment.get('reply_count', 0))
    return CommentData(
        user_id=user.get("id", ""),
        username=user.get("name", "匿名用户"),
        user_level=int(user.get("lv", 0)),
        floor=raw_comment.get("floor", "").replace("<span class=\"L1\">", "").replace("</span>", ""),
        comment_id=raw_comment.get("id", ""),
        post_time=post_time,
        time_range=time_data.get("range", ""),
        upvotes=int(attitude.get("up", 0)),
        downvotes=int(attitude.get("down", 0)),
        replies_count=reply_count,
        content_text=clean_text(content_text.strip()),
        content_links=dedup_links,
        image_urls=image_urls,
        is_abnormal_time=is_abnormal,
    )

def save_to_csv(comments: List[CommentData], filename: str = "comments.csv"):
    with open(filename, "w", newline="", encoding="utf-8") as f:
        writer = csv.writer(f)
        writer.writerow([
            "UserID", "Username", "Level", "Floor", "CommentID",
            "PostTime", "TimeRange", "Upvotes", "Downvotes", "RepliesCount",
            "Content", "Links", "Images", "IsAbnormalTime", "Replies"
        ])

        for comment in comments:
            writer.writerow([
                comment.user_id,
                comment.username,
                comment.user_level,
                comment.floor,
                comment.comment_id,
                comment.post_time,
                comment.time_range,
                comment.upvotes,
                comment.downvotes,
                comment.replies_count,
                comment.content_text,
                "|".join(comment.content_links),
                "|".join(comment.image_urls),
                int(comment.is_abnormal_time),
                json.dumps([{
                    "UserID": r.user_id,
                    "Username": r.username,
                    "Level": r.user_level,
                    "CommentID": r.comment_id,
                    "PostTime": r.post_time,
                    "TimeRange": r.time_range,
                    "Upvotes": r.upvotes,
                    "Content": r.content_text,
                    "Report": r.report
                } for r in comment.replies], ensure_ascii=False)
            ])

def build_payload(page: int, id: int) -> dict:
    return {
        'type': 'class',
        'channel': '1',
        'doid': str(id),
        'page': page,
        'selfonly': 0
    }

def build_headers(id: int) -> dict:
    return {
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Connection': 'keep-alive',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Origin': f'https://www.mcmod.cn',
        'Referer': f'https://www.mcmod.cn/class/{id}.html',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'sec-ch-ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
    }

def clean_reply(raw_reply: Dict) -> ReplyData:
    user = raw_reply.get("user", {})
    time_data = raw_reply.get("time", {})
    post_time = time_data.get("source", "")
    content = raw_reply.get("content", "")
    return ReplyData(
        user_id=user.get("id", ""),
        username=user.get("name", "匿名用户"),
        user_level=int(user.get("lv", 0)),
        comment_id=raw_reply.get("id", ""),
        post_time=post_time,
        time_range=time_data.get("range", ""),
        upvotes=int(raw_reply.get("attitude", {}).get("up", 0)),
        content_text=content.strip(),
        report=int(raw_reply.get("report", 0))
    )

def fetch_replies(comment_id: str, parent_id: int, session: requests.Session, delay: float = 1.5) -> List[CommentData]:
    try:
        time.sleep(delay)
        url = 'https://www.mcmod.cn/frame/comment/CommentReply/'
        payload = {'replyID': comment_id, 'page': 1}
        response = session.post(
            url,
            headers=build_headers(parent_id),
            data={'data': json.dumps(payload)},
            timeout=8
        )
        response.raise_for_status()
        raw_data = response.json()
        if raw_data['state'] != 0:
            return []
        return [clean_reply(r) for r in raw_data.get('data', {}).get('row', [])]
    except Exception as e:
        # print(f"回复抓取失败 | ID:{comment_id} | 错误:{str(e)}")
        return []

def fetch_all_comments(id: int, max_retries=3, delay=1.5):
    session = requests.Session()
    retries = Retry(total=max_retries,
                    backoff_factor=0.3,
                    status_forcelist=[500, 502, 503, 504])
    session.mount('https://', HTTPAdapter(max_retries=retries))

    all_comments = []
    current_page = 1
    total_page = 1
    has_pagination = True
    while True:
        try:
            payload = build_payload(current_page, id)
            response = session.post(
                'https://www.mcmod.cn/frame/comment/CommentRow/',
                headers=build_headers(id),
                data={'data': json.dumps(payload)},
                timeout=10
            )
            response.raise_for_status()
            raw_data = response.json()
            if raw_data['state'] != 0:
                # print(f"状态码异常终止 @ 第{current_page}页")
                break
            if 'page' not in raw_data['data']:
                has_pagination = False
                # print("检测到无分页信息，进入单页模式")
                if 'row' in raw_data['data']:
                    page_comments = [clean_comment(c) for c in raw_data['data']['row']]
                    all_comments.extend(page_comments)
                break
            else:
                if current_page == 1:
                    total_page = raw_data['data']['page'].get('total_page', 1)
                    # print(f"总页数识别成功：{total_page}页（含分页模式）")
                page_comments = [clean_comment(c) for c in raw_data['data']['row']]
                all_comments.extend(page_comments)
                now_page = raw_data['data']['page'].get('now_page', current_page)
                if now_page >= total_page:
                    # print(f"正常终止：已到达末页 {total_page}")
                    break
                next_page = raw_data['data']['page'].get('next', current_page + 1)
                current_page = next_page if next_page > current_page else current_page + 1
            time.sleep(delay)
        except (requests.exceptions.RequestException, json.JSONDecodeError, KeyError) as e:
            # print(f"抓取失败 @ 第{current_page}页: {str(e)}")
            if not has_pagination:
                break
            if current_page >= total_page:
                break
            current_page += 1
    count = 0
    for idx, comment in enumerate(all_comments):
        if comment.replies_count > 0:
            comment.replies = fetch_replies(comment.comment_id, id, session, delay)
            count += 1
    return count, all_comments

def process_single_id(id):
    count, all_comments = fetch_all_comments(id)
    return id, count, all_comments

def fetch_comments_in_range(l, r):
    with ThreadPoolExecutor(max_workers=1) as executor:  # 调整max_workers为合理值（如CPU核心数）
        futures = [executor.submit(process_single_id, id) for id in range(l, r)]
        for future in futures:
            id, count, all_comments = future.result()
            save_to_csv(all_comments, f"./comment/comment{id}.csv")
            print(f"ID: {id} - 总计: {len(all_comments)}, 回复: {count}, 异常: {sum(c.is_abnormal_time for c in all_comments)}")

l = 3
r = 4
fetch_comments_in_range(l, r)

#submitTest