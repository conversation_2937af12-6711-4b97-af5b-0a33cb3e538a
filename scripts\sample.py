from dotenv import load_dotenv
from langchain_community.document_loaders import DirectoryLoader
from langchain_community.chat_models import ChatZhipuAI
from langchain_huggingface import HuggingFaceEmbeddings
from ragas.llms import LangchainLLMWrapper
from ragas.embeddings import LangchainEmbeddingsWrapper

load_dotenv()

loader = DirectoryLoader("Sample_Docs_Markdown/", glob="**/*.md")
documents = loader.load()

llm = ChatZhipuAI(model="GLM-4-Plus")

model_name = "BAAI/bge-large-zh-v1.5"
model_kwargs = {'device': 'cuda'}
encode_kwargs = {'normalize_embeddings': True}

embeddings = HuggingFaceEmbeddings(
    model_name=model_name,
    model_kwargs=model_kwargs,
    encode_kwargs=encode_kwargs
)

generator_llm = LangchainLLMWrapper(llm)
generator_embeddings = LangchainEmbeddingsWrapper(embeddings)

from ragas.testset.graph import KnowledgeGraph

kg = KnowledgeGraph()

from ragas.testset.graph import Node, NodeType

for doc in documents:
    kg.nodes.append(
        Node(
            type=NodeType.DOCUMENT,
            properties={
                "page_content": doc.page_content,
                "document_metadata": doc.metadata
            }
        )
    )

from ragas.testset.transforms import default_transforms, apply_transforms

transformer_llm = generator_llm
embedding_model = generator_embeddings

trans = default_transforms(documents=documents, llm=transformer_llm, embedding_model=embedding_model)
apply_transforms(kg, trans)

kg.save("knowledge_graph.json")
loaded_kg = KnowledgeGraph.load("knowledge_graph.json")

from ragas.testset import TestsetGenerator

generator = TestsetGenerator(llm=generator_llm, embedding_model=embedding_model, knowledge_graph=loaded_kg)

from ragas.testset.synthesizers import default_query_distribution

query_distribution = default_query_distribution(generator_llm)

testset = generator.generate(testset_size=10, query_distribution=query_distribution)

print(testset.to_pandas())



