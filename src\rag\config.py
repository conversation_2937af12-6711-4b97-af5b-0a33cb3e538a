# src/rag/config.py
import os
from dotenv import load_dotenv
from pathlib import Path
import logging

# --- 初始化日志记录器 (用于配置加载过程的日志) ---
# 注意: 这个基础日志配置应与项目中统一的 logging_config.py 协调，
# 避免冲突或重复配置。这里仅为展示配置加载时的日志输出。
logging.basicConfig(
    level=os.getenv("LOG_LEVEL", "INFO").upper(), # 尝试从环境变量预读日志级别
    format='%(asctime)s - %(name)s.%(funcName)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("rag.config") # 使用带层级的 logger 名称

# --- 确定 .env 文件路径 ---
# 假设 config.py 位于 src/rag/config.py
# 项目根目录通常是 src/ 目录的父目录
try:
    current_file_path = Path(__file__).resolve()
    project_root_from_config = current_file_path.parent.parent.parent
    default_env_path = project_root_from_config / ".env"
except NameError: # __file__ 未定义 (例如在某些交互式环境中)
    default_env_path = Path(os.getcwd()) / ".env"


# 允许通过环境变量指定 .env 文件位置，增加灵活性
ENV_FILE_PATH_OVERRIDE = os.getenv("RAG_ENV_FILE_PATH")

env_path_to_load = None
if ENV_FILE_PATH_OVERRIDE and Path(ENV_FILE_PATH_OVERRIDE).exists():
    env_path_to_load = Path(ENV_FILE_PATH_OVERRIDE)
    logger.info(f"尝试从 RAG_ENV_FILE_PATH 加载 .env 文件: {env_path_to_load}")
elif default_env_path.exists():
    env_path_to_load = default_env_path
    logger.info(f"尝试从默认位置加载 .env 文件: {env_path_to_load}")
else:
    # 尝试当前工作目录作为最后的备选
    cwd_env_path = Path(os.getcwd()) / ".env"
    if cwd_env_path.exists():
        env_path_to_load = cwd_env_path
        logger.info(f"尝试从当前工作目录加载 .env 文件: {env_path_to_load}")
    else:
        logger.warning(
            f"在指定的 RAG_ENV_FILE_PATH、"
            f"默认位置 ({default_env_path}) "
            f"或当前工作目录 ({cwd_env_path}) "
            f"均未找到 .env 文件。"
            "将依赖现有的环境变量和代码默认值。"
        )

# --- 加载 .env 文件 (如果找到) ---
if env_path_to_load:
    load_dotenv(dotenv_path=env_path_to_load, override=True)
    logger.info(f"成功从以下路径加载配置: {env_path_to_load}")
else:
    # 即使没找到 .env 文件，也调用一次 load_dotenv()
    # 这样如果环境变量已经由外部设置（如 Docker, K8s），它们仍然会被 os.getenv 读取
    load_dotenv(override=True)
    logger.info("未通过路径加载 .env 文件，将依赖预先存在的环境变量或默认值。")

# --- 辅助函数：获取环境变量，进行类型转换，并提供默认值 ---
# 特殊类型标记，用于 get_env_var 的 var_type 参数
class ListStrMarker: pass # 用于逗号分隔的字符串列表
class BoolMarker: pass   # 用于更明确的布尔转换

def get_env_var(var_name: str, default_value=None, var_type=str):
    """
    检索环境变量，如果未找到则提供默认值，
    并执行类型转换。
    """
    raw_value = os.getenv(var_name)

    if raw_value is None: # 环境变量未设置
        if default_value is None and not (var_type is str or var_type is ListStrMarker or isinstance(var_type, type(None))):
             # 仅对非字符串、非列表且非可选类型，且没有默认值的情况发出警告或错误
             # logger.warning(f"Configuration variable '{var_name}' is not set and has no default, but type is {var_type}.")
             pass # 允许为None，由使用者处理
        value_to_convert = default_value
    else:
        value_to_convert = raw_value

    if value_to_convert is None: # 无论来源，如果最终值是None，直接返回
        return None

    try:
        if var_type is BoolMarker:
            if isinstance(value_to_convert, bool): return value_to_convert
            return str(value_to_convert).lower() in ('true', '1', 't', 'yes', 'y')
        elif var_type is int:
            return int(value_to_convert)
        elif var_type is float:
            return float(value_to_convert)
        elif var_type is ListStrMarker: # 逗号分隔的字符串列表
            if isinstance(value_to_convert, list): return value_to_convert # 如果默认值已经是列表
            return [item.strip() for item in str(value_to_convert).split(',') if item.strip()]
        elif var_type is str:
            return str(value_to_convert)
        else: # 如果 var_type 是其他 callable (如 Path)
            return var_type(value_to_convert)
    except (ValueError, TypeError) as e:
        logger.error(
            f"转换环境变量 '{var_name}' "
            f"的值 '{value_to_convert}' (类型: {type(value_to_convert).__name__}) "
            f"到类型 {var_type.__name__ if hasattr(var_type, '__name__') else str(var_type)} 时出错。"
            f"使用默认值: '{default_value}'。错误: {e}"
        )
        return default_value


# --- 定义所有配置变量 ---

# LLM Configurations
# 通用 (默认，如果未配置特定任务的 LLM，则作为后备)
LLM_MODEL = get_env_var("LLM_MODEL", "THUDM/GLM-Z1-32B-0414")
BASE_URL = get_env_var("BASE_URL", "https://api.siliconflow.cn/v1")
API_KEY = get_env_var("API_KEY", None) # API 密钥理想情况下不应有硬编码的默认值

# RAG System LLM
RAG_LLM_MODEL = get_env_var("RAG_LLM_MODEL", LLM_MODEL)
RAG_BASE_URL = get_env_var("RAG_BASE_URL", BASE_URL)
RAG_API_KEY = get_env_var("RAG_API_KEY", API_KEY)
RAG_TEMPERATURE = get_env_var("RAG_TEMPERATURE", 0.1, float)

# Title Generation LLM
TITLE_LLM_MODEL = get_env_var("TITLE_LLM_MODEL", "THUDM/GLM-4-9B-0414")
TITLE_BASE_URL = get_env_var("TITLE_BASE_URL", BASE_URL)
TITLE_API_KEY = get_env_var("TITLE_API_KEY", API_KEY)
TITLE_TEMPERATURE = get_env_var("TITLE_TEMPERATURE", 0.3, float)

# Metadata Matching LLM
METADATA_LLM_MODEL = get_env_var("METADATA_LLM_MODEL", "THUDM/GLM-4-9B-0414")
METADATA_BASE_URL = get_env_var("METADATA_BASE_URL", BASE_URL)
METADATA_API_KEY = get_env_var("METADATA_API_KEY", API_KEY)
METADATA_TEMPERATURE = get_env_var("METADATA_TEMPERATURE", 0.0, float)

# Intent Classification LLM
INTENT_LLM_MODEL = get_env_var("INTENT_LLM_MODEL", "THUDM/GLM-4-9B-0414")
INTENT_BASE_URL = get_env_var("INTENT_BASE_URL", BASE_URL)
INTENT_API_KEY = get_env_var("INTENT_API_KEY", API_KEY)
INTENT_TEMPERATURE = get_env_var("INTENT_TEMPERATURE", 0.0, float)

# Embedding and Vector Database
BAAI_PATH = get_env_var("BAAI_PATH", "BAAI/bge-large-zh-v1.5") # 可以是 HuggingFace Hub 名称或本地路径
CHROMA_PATH = get_env_var("CHROMA_PATH", "./default_chroma_db_location") # 代码级别的默认值
DEVICE = get_env_var("DEVICE", "cpu") # 应为 "cpu" 或 "cuda"

# Running Environment
ENVIRONMENT = get_env_var("ENVIRONMENT", "development") # 例如 "development", "production"

# Database (MySQL)
DATABASE_URL = get_env_var("DATABASE_URL", "mysql+pymysql://root:123456@localhost/rag_db_default")

# JWT Authentication
SECRET_KEY = get_env_var("SECRET_KEY", "change-this-default-secret-key-in-production") # 关键: 在生产环境的 .env 文件中覆盖此值
ACCESS_TOKEN_EXPIRE_MINUTES = get_env_var("ACCESS_TOKEN_EXPIRE_MINUTES", 60 * 24 * 7, int) # 默认: 1 周

# 检索超参数 (代码级别默认值，可通过 .env 文件覆盖)
RETRIEVER_INITIAL_K = get_env_var("RETRIEVER_INITIAL_K", 15, int)
RERANK_MODEL_NAME = get_env_var("RERANK_MODEL_NAME", "BAAI/bge-reranker-v2-m3") # HuggingFace Hub 名称或本地路径
RERANK_TOP_K = get_env_var("RERANK_TOP_K", 5, int)
RERANK_BATCH_SIZE = get_env_var("RERANK_BATCH_SIZE", 8, int) # 重排序批处理大小，CPU环境下建议较小值

# 查询扩展配置
QUERY_EXPANSION_ENABLED = get_env_var("QUERY_EXPANSION_ENABLED", True, BoolMarker)
QUERY_EXPANSION_TEMPERATURE = get_env_var("QUERY_EXPANSION_TEMPERATURE", 0.2, float)
QUERY_EXPANSION_LLM_MODEL = get_env_var("QUERY_EXPANSION_LLM_MODEL", "THUDM/GLM-4-9B-0414")
QUERY_EXPANSION_BASE_URL = get_env_var("QUERY_EXPANSION_BASE_URL", BASE_URL)
QUERY_EXPANSION_API_KEY = get_env_var("QUERY_EXPANSION_API_KEY", API_KEY)

# Application Specific Settings
LOG_LEVEL = get_env_var("LOG_LEVEL", "INFO").upper()
# 本地开发的默认CORS配置。对于其他环境，请在 .env 文件中覆盖。
CORS_ALLOWED_ORIGINS = get_env_var("CORS_ALLOWED_ORIGINS", "http://localhost:8080,http://127.0.0.1:8080", ListStrMarker)

RECORDING_ENABLED = get_env_var("RECORDING_ENABLED", False, BoolMarker)

# 合成导航功能配置
# 物品实体识别配置
ITEM_EXTRACTION_MODEL = get_env_var("ITEM_EXTRACTION_MODEL", "THUDM/GLM-4-9B-0414")
ITEM_EXTRACTION_BASE_URL = get_env_var("ITEM_EXTRACTION_BASE_URL", BASE_URL)
ITEM_EXTRACTION_API_KEY = get_env_var("ITEM_EXTRACTION_API_KEY", API_KEY)
ITEM_EXTRACTION_TEMPERATURE = get_env_var("ITEM_EXTRACTION_TEMPERATURE", 0.1, float)

# Neo4j配置（预留，当前阶段不启用）
NEO4J_ENABLED = get_env_var("NEO4J_ENABLED", False, BoolMarker)
NEO4J_URI = get_env_var("NEO4J_URI", "bolt://localhost:7687")
NEO4J_USERNAME = get_env_var("NEO4J_USERNAME", "neo4j")
NEO4J_PASSWORD = get_env_var("NEO4J_PASSWORD", "password")


# --- (可选) 记录生效的配置以供调试 ---
# 这最好在主日志系统完全配置后完成
def log_effective_configuration():
    # 如果 LOG_LEVEL 已通过 .env 更新，则重新获取 logger 实例
    effective_logger = logging.getLogger("rag.config.effective")
    if effective_logger.isEnabledFor(logging.INFO): # 检查是否启用了 INFO 级别
        effective_logger.info("--- 生效配置摘要 ---")
        # 选择性地记录非敏感或摘要性配置
        config_summary = {
            "ENVIRONMENT": ENVIRONMENT,
            "LOG_LEVEL": LOG_LEVEL,
            "LLM_MODEL (Default)": LLM_MODEL,
            "RAG_LLM_MODEL": RAG_LLM_MODEL,
            "DATABASE_URL (Host/DB)": DATABASE_URL.split('@')[-1] if '@' in DATABASE_URL else DATABASE_URL,
            "BAAI_PATH": BAAI_PATH,
            "CHROMA_PATH": CHROMA_PATH,
            "DEVICE": DEVICE,
            "RETRIEVER_INITIAL_K": RETRIEVER_INITIAL_K,
            "RERANK_MODEL_NAME": RERANK_MODEL_NAME,
            "RERANK_TOP_K": RERANK_TOP_K,
            "QUERY_EXPANSION_ENABLED": QUERY_EXPANSION_ENABLED,
            "QUERY_EXPANSION_TEMPERATURE": QUERY_EXPANSION_TEMPERATURE,
            "QUERY_EXPANSION_LLM_MODEL": QUERY_EXPANSION_LLM_MODEL,
            "ITEM_EXTRACTION_MODEL": ITEM_EXTRACTION_MODEL,
            "ITEM_EXTRACTION_TEMPERATURE": ITEM_EXTRACTION_TEMPERATURE,
            "NEO4J_ENABLED": NEO4J_ENABLED,
            "API_KEY_IS_SET": "Yes" if API_KEY else "No",
            "SECRET_KEY_IS_DEFAULT": "Yes" if SECRET_KEY == "change-this-default-secret-key-in-production" else "No (Custom)",
            "CORS_ALLOWED_ORIGINS": CORS_ALLOWED_ORIGINS,
        }
        for key, value in config_summary.items():
            effective_logger.info(f"{key}: {value}")
        effective_logger.info("--- 生效配置摘要结束 ---")

