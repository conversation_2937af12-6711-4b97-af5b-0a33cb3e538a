"""
物品实体识别器

此模块提供智能物品实体识别功能，能够从用户消息中识别Minecraft物品。
使用LLM + 规则匹配的混合策略确保识别准确性。
"""

import json
import re
import time
from typing import List, Dict, Any, Optional
from langchain_core.messages import HumanMessage
from langchain_core.language_models.chat_models import BaseChatModel

from .prompts import ITEM_ENTITY_EXTRACTION_PROMPT_TEMPLATE
from ..logging_config import get_logger

logger = get_logger("item_entity_extractor")

class ItemEntityExtractor:
    """
    物品实体识别器

    使用LLM进行智能物品识别，结合规则验证确保准确性。
    """

    def __init__(self, llm: BaseChatModel, temperature: float = 0.1):
        """
        初始化物品实体识别器

        Args:
            llm: 用于物品识别的语言模型
            temperature: LLM温度参数，较低值确保更稳定的识别结果
        """
        self.llm = llm
        self.temperature = temperature

        # 常见Minecraft物品词典（用于验证和规则匹配）
        self.common_items = {
            # 工具类
            "钻石剑", "铁剑", "石剑", "木剑", "金剑", "下界合金剑",
            "钻石镐", "铁镐", "石镐", "木镐", "金镐", "下界合金镐",
            "钻石斧", "铁斧", "石斧", "木斧", "金斧", "下界合金斧",
            "钻石锹", "铁锹", "石锹", "木锹", "金锹", "下界合金锹",
            "钻石锄", "铁锄", "石锄", "木锄", "金锄", "下界合金锄",
            "弓", "弩", "钓鱼竿", "打火石", "剪刀", "指南针", "时钟",

            # 装备类
            "钻石头盔", "铁头盔", "金头盔", "皮革头盔", "链甲头盔", "下界合金头盔",
            "钻石胸甲", "铁胸甲", "金胸甲", "皮革胸甲", "链甲胸甲", "下界合金胸甲",
            "钻石护腿", "铁护腿", "金护腿", "皮革护腿", "链甲护腿", "下界合金护腿",
            "钻石靴子", "铁靴子", "金靴子", "皮革靴子", "链甲靴子", "下界合金靴子",
            "盾牌", "鞘翅",

            # 材料类
            "钻石", "铁锭", "金锭", "铜锭", "下界合金锭",
            "煤炭", "木炭", "红石", "青金石", "绿宝石",
            "木棍", "线", "皮革", "羽毛", "火药", "骨头", "蜘蛛眼",
            "木板", "圆石", "石头", "玻璃", "黑曜石",

            # 食物类
            "面包", "苹果", "金苹果", "附魔金苹果",
            "生牛肉", "熟牛肉", "生猪肉", "熟猪肉", "生鸡肉", "熟鸡肉",
            "生鱼", "熟鱼", "生鲑鱼", "熟鲑鱼", "胡萝卜", "土豆", "烤土豆",
            "蛋糕", "曲奇", "南瓜派", "蘑菇煲",

            # 药水类
            "治疗药水", "伤害药水", "再生药水", "力量药水", "速度药水",
            "跳跃药水", "抗火药水", "水下呼吸药水", "夜视药水", "隐身药水",

            # 其他常用物品
            "床", "箱子", "工作台", "熔炉", "附魔台", "铁砧", "酿造台",
            "书", "纸", "地图", "船", "矿车", "马鞍", "命名牌",
        }

        logger.info(f"物品实体识别器初始化完成，词典包含 {len(self.common_items)} 个常见物品")

    async def extract_items(self, message: str) -> List[str]:
        """
        从用户消息中提取物品实体

        Args:
            message: 用户消息

        Returns:
            List[str]: 识别到的物品名称列表
        """
        if not message or not message.strip():
            logger.warning("消息为空，无法进行物品识别")
            return []

        start_time = time.time()
        logger.info(f"开始识别物品实体: '{message[:50]}{'...' if len(message) > 50 else ''}'")

        try:
            # 直接使用LLM进行物品识别
            llm_items = await self._llm_based_extraction(message)

            extraction_time = time.time() - start_time
            logger.info(f"物品识别完成，耗时: {extraction_time:.2f}秒")
            logger.info(f"LLM识别到物品: {llm_items}")

            return llm_items[:3] if llm_items else []

        except Exception as e:
            logger.error(f"物品识别失败: {e}")
            return []



    async def _llm_based_extraction(self, message: str) -> List[str]:
        """
        基于LLM的物品识别

        Args:
            message: 用户消息

        Returns:
            List[str]: LLM识别的物品列表
        """
        try:
            # 格式化提示
            prompt = ITEM_ENTITY_EXTRACTION_PROMPT_TEMPLATE.format(message=message)

            # 调用LLM
            messages = [HumanMessage(content=prompt)]
            response = await self.llm.ainvoke(messages, temperature=self.temperature)
            response_text = response.content if hasattr(response, 'content') else str(response)

            logger.info(f"LLM原始响应: {response_text[:200]}...")

            # 解析JSON响应
            items = self._parse_llm_response(response_text)

            if items:
                logger.info(f"LLM识别到物品: {items}")
                return items
            else:
                # 如果LLM识别失败，使用简单的规则匹配作为降级
                logger.warning("LLM识别失败，使用规则匹配降级")
                return self._simple_rule_extraction(message)

        except Exception as e:
            logger.error(f"LLM物品识别异常: {e}")
            # 降级到简单规则匹配
            return self._simple_rule_extraction(message)

    def _parse_llm_response(self, response_text: str) -> List[str]:
        """
        解析LLM响应，提取物品列表

        Args:
            response_text: LLM响应文本

        Returns:
            List[str]: 识别到的物品列表
        """
        try:
            # 方法1: 尝试提取完整JSON
            json_match = re.search(r'\{[^{}]*"items"[^{}]*\}', response_text, re.DOTALL)
            if json_match:
                try:
                    result = json.loads(json_match.group())
                    items = result.get("items", [])
                    if isinstance(items, list):
                        return items[:3]  # 最多返回3个物品，包括空列表
                except json.JSONDecodeError:
                    pass

            # 方法2: 尝试提取items数组
            items_match = re.search(r'"items"\s*:\s*\[(.*?)\]', response_text, re.DOTALL)
            if items_match:
                items_str = items_match.group(1)
                # 提取引号中的内容
                item_matches = re.findall(r'"([^"]+)"', items_str)
                if item_matches:
                    return item_matches[:3]

            # 方法3: 查找常见的物品名称模式
            item_patterns = [
                r'钻石剑', r'铁剑', r'金剑', r'石剑', r'木剑',
                r'钻石镐', r'铁镐', r'金镐', r'石镐', r'木镐',
                r'钻石斧', r'铁斧', r'金斧', r'石斧', r'木斧',
                r'钻石头盔', r'铁头盔', r'金头盔', r'皮革头盔',
                r'钻石胸甲', r'铁胸甲', r'金胸甲', r'皮革胸甲',
                r'钻石护腿', r'铁护腿', r'金护腿', r'皮革护腿',
                r'钻石靴子', r'铁靴子', r'金靴子', r'皮革靴子',
                r'钻石', r'铁锭', r'金锭', r'煤炭', r'红石',
                r'弓', r'弩', r'盾牌', r'钓鱼竿'
            ]

            found_items = []
            for pattern in item_patterns:
                if re.search(pattern, response_text):
                    item_name = pattern.replace(r'\\', '')  # 移除正则转义
                    if item_name not in found_items:
                        found_items.append(item_name)

            if found_items:
                return found_items[:3]

            logger.warning(f"无法解析LLM响应: {response_text[:100]}...")
            return []

        except Exception as e:
            logger.error(f"解析LLM响应时出错: {e}")
            return []

    def _simple_rule_extraction(self, message: str) -> List[str]:
        """
        简单的规则匹配，作为LLM失败时的降级方案

        Args:
            message: 用户消息

        Returns:
            List[str]: 识别到的物品列表
        """
        # 常见物品的简单匹配
        common_items = [
            "钻石剑", "铁剑", "金剑", "石剑", "木剑",
            "钻石镐", "铁镐", "金镐", "石镐", "木镐",
            "钻石斧", "铁斧", "金斧", "石斧", "木斧",
            "钻石头盔", "铁头盔", "金头盔", "皮革头盔",
            "钻石胸甲", "铁胸甲", "金胸甲", "皮革胸甲",
            "钻石护腿", "铁护腿", "金护腿", "皮革护腿",
            "钻石靴子", "铁靴子", "金靴子", "皮革靴子",
            "钻石", "铁锭", "金锭", "煤炭", "红石",
            "弓", "弩", "盾牌", "钓鱼竿", "面包", "苹果"
        ]

        found_items = []
        for item in common_items:
            if item in message:
                found_items.append(item)

        # 按长度排序，优先选择更具体的物品
        found_items.sort(key=len, reverse=True)

        logger.info(f"规则匹配识别到物品: {found_items[:3]}")
        return found_items[:3]


