"""
文档过滤器模块

此模块提供了用于过滤和处理检索到的文档的工具。
"""

from langchain_huggingface import HuggingFaceEmbeddings
from langchain_community.document_transformers import EmbeddingsRedundantFilter
from typing import List, Optional
from langchain_core.documents import Document

class DocumentFilterManager:
    """
    文档过滤器管理器，用于创建和应用文档过滤器
    """
    
    @staticmethod
    def create_redundant_filter(
        embeddings: HuggingFaceEmbeddings,
        similarity_threshold: float = 0.9
    ) -> EmbeddingsRedundantFilter:
        """
        创建冗余过滤器
        
        Args:
            embeddings: 嵌入模型
            similarity_threshold: 相似度阈值，默认为0.95
            
        Returns:
            EmbeddingsRedundantFilter: 冗余过滤器
        """
        return EmbeddingsRedundantFilter(
            embeddings=embeddings,
            similarity_threshold=similarity_threshold
        )
    
    @staticmethod
    def filter_by_score(
        documents: List[Document],
        score_key: str = "metadata_match_score",
        min_score: float = 0.0,
        max_docs: Optional[int] = None
    ) -> List[Document]:
        """
        根据分数过滤文档
        
        Args:
            documents: 文档列表
            score_key: 分数键名，默认为"metadata_match_score"
            min_score: 最小分数，默认为0.0
            max_docs: 最大文档数量，默认为None（不限制）
            
        Returns:
            List[Document]: 过滤后的文档列表
        """
        # 过滤分数大于等于min_score的文档
        filtered_docs = [
            doc for doc in documents 
            if score_key in doc.metadata and doc.metadata[score_key] >= min_score
        ]
        
        # 按分数降序排序
        sorted_docs = sorted(
            filtered_docs,
            key=lambda doc: doc.metadata.get(score_key, 0),
            reverse=True
        )
        
        # 限制文档数量
        if max_docs is not None:
            return sorted_docs[:max_docs]
        
        return sorted_docs
    
    @staticmethod
    def filter_by_source(
        documents: List[Document],
        source_name: str,
        source_key: str = "retriever_source"
    ) -> List[Document]:
        """
        根据来源过滤文档
        
        Args:
            documents: 文档列表
            source_name: 来源名称
            source_key: 来源键名，默认为"retriever_source"
            
        Returns:
            List[Document]: 过滤后的文档列表
        """
        filtered_docs = []
        for doc in documents:
            if source_key in doc.metadata:
                sources = doc.metadata[source_key]
                if isinstance(sources, list) and source_name in sources:
                    filtered_docs.append(doc)
                elif sources == source_name:
                    filtered_docs.append(doc)
        
        return filtered_docs
