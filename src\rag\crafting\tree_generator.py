"""
合成树生成器

此模块提供Minecraft物品合成树的生成功能。
当前阶段使用Mock数据，为未来接入Neo4j图数据库预留接口。
"""

import json
import time
from typing import Dict, Any, Optional, List
from ..logging_config import get_logger

logger = get_logger("crafting_tree_generator")

class CraftingTreeGenerator:
    """
    合成树生成器
    
    当前使用Mock数据生成合成树，未来将接入Neo4j图数据库。
    """
    
    def __init__(self, neo4j_enabled: bool = False):
        """
        初始化合成树生成器
        
        Args:
            neo4j_enabled: 是否启用Neo4j（当前阶段为False）
        """
        self.neo4j_enabled = neo4j_enabled
        
        # Mock数据：常见物品的合成树
        self.mock_crafting_trees = self._initialize_mock_data()
        
        logger.info(f"合成树生成器初始化完成，Neo4j启用状态: {neo4j_enabled}")
        logger.info(f"Mock数据包含 {len(self.mock_crafting_trees)} 个物品的合成树")
    
    async def generate_crafting_tree(self, target_item: str) -> Optional[Dict[str, Any]]:
        """
        生成指定物品的合成树
        
        Args:
            target_item: 目标物品名称
            
        Returns:
            Dict[str, Any]: 合成树数据，符合前端接口规范
        """
        start_time = time.time()
        logger.info(f"开始生成合成树: {target_item}")
        
        try:
            if self.neo4j_enabled:
                # 未来Neo4j实现
                tree = await self._generate_from_neo4j(target_item)
            else:
                # 当前Mock数据实现
                tree = self._generate_from_mock_data(target_item)
            
            generation_time = time.time() - start_time
            logger.info(f"合成树生成完成，耗时: {generation_time:.2f}秒")
            
            return tree
            
        except Exception as e:
            logger.error(f"合成树生成失败: {e}")
            return None
    
    def _generate_from_mock_data(self, target_item: str) -> Optional[Dict[str, Any]]:
        """
        从Mock数据生成合成树
        
        Args:
            target_item: 目标物品名称
            
        Returns:
            Dict[str, Any]: 合成树数据
        """
        # 直接查找匹配的物品
        if target_item in self.mock_crafting_trees:
            return self.mock_crafting_trees[target_item]
        
        # 模糊匹配
        for item_name, tree_data in self.mock_crafting_trees.items():
            if target_item in item_name or item_name in target_item:
                logger.info(f"模糊匹配: '{target_item}' -> '{item_name}'")
                return tree_data
        
        # 如果没有找到，生成一个简单的默认树
        logger.warning(f"未找到物品 '{target_item}' 的合成树，生成默认树")
        return self._generate_default_tree(target_item)
    
    async def _generate_from_neo4j(self, target_item: str) -> Optional[Dict[str, Any]]:
        """
        从Neo4j数据库生成合成树（未来实现）
        
        Args:
            target_item: 目标物品名称
            
        Returns:
            Dict[str, Any]: 合成树数据
        """
        # TODO: 实现Neo4j查询逻辑
        logger.info("Neo4j合成树生成功能尚未实现")
        return None
    
    def _generate_default_tree(self, target_item: str) -> Dict[str, Any]:
        """
        生成默认的合成树结构
        
        Args:
            target_item: 目标物品名称
            
        Returns:
            Dict[str, Any]: 默认合成树
        """
        return {
            "itemId": target_item.lower().replace(" ", "_"),
            "itemName": target_item,
            "itemIcon": f"/icons/{target_item.lower().replace(' ', '_')}.png",
            "itemDesc": f"{target_item}是一个Minecraft物品。",
            "recipe": [
                [None, None, None],
                [None, target_item, None],
                [None, None, None]
            ],
            "children": []
        }
    
    def _initialize_mock_data(self) -> Dict[str, Dict[str, Any]]:
        """
        初始化Mock数据
        
        Returns:
            Dict[str, Dict[str, Any]]: Mock合成树数据
        """
        return {
            "钻石剑": {
                "itemId": "diamond_sword",
                "itemName": "钻石剑",
                "itemIcon": "/icons/diamond_sword.png",
                "itemDesc": "钻石剑是Minecraft中最强的近战武器之一，攻击力为7点。",
                "recipe": [
                    [None, "钻石", None],
                    [None, "钻石", None],
                    [None, "木棍", None]
                ],
                "children": [
                    {
                        "itemId": "diamond",
                        "itemName": "钻石",
                        "itemIcon": "/icons/diamond.png",
                        "itemDesc": "钻石是一种珍贵的宝石，用于制作高级工具和装备。",
                        "recipe": [
                            [None, None, None],
                            [None, "钻石矿石", None],
                            [None, None, None]
                        ],
                        "children": []
                    },
                    {
                        "itemId": "stick",
                        "itemName": "木棍",
                        "itemIcon": "/icons/stick.png",
                        "itemDesc": "木棍是制作工具的基础材料。",
                        "recipe": [
                            [None, "木板", None],
                            [None, "木板", None],
                            [None, None, None]
                        ],
                        "children": [
                            {
                                "itemId": "planks",
                                "itemName": "木板",
                                "itemIcon": "/icons/planks.png",
                                "itemDesc": "木板是由原木制作的基础建筑材料。",
                                "recipe": [
                                    [None, "原木", None],
                                    [None, None, None],
                                    [None, None, None]
                                ],
                                "children": []
                            }
                        ]
                    }
                ]
            },
            "铁剑": {
                "itemId": "iron_sword",
                "itemName": "铁剑",
                "itemIcon": "/icons/iron_sword.png",
                "itemDesc": "铁剑是一种常用的近战武器，攻击力为6点。",
                "recipe": [
                    [None, "铁锭", None],
                    [None, "铁锭", None],
                    [None, "木棍", None]
                ],
                "children": [
                    {
                        "itemId": "iron_ingot",
                        "itemName": "铁锭",
                        "itemIcon": "/icons/iron_ingot.png",
                        "itemDesc": "铁锭是由铁矿石冶炼而成的金属材料。",
                        "recipe": [
                            [None, None, None],
                            [None, "铁矿石", None],
                            [None, None, None]
                        ],
                        "children": []
                    },
                    {
                        "itemId": "stick",
                        "itemName": "木棍",
                        "itemIcon": "/icons/stick.png",
                        "itemDesc": "木棍是制作工具的基础材料。",
                        "recipe": [
                            [None, "木板", None],
                            [None, "木板", None],
                            [None, None, None]
                        ],
                        "children": []
                    }
                ]
            },
            "工作台": {
                "itemId": "crafting_table",
                "itemName": "工作台",
                "itemIcon": "/icons/crafting_table.png",
                "itemDesc": "工作台是进行复杂合成的必需工具。",
                "recipe": [
                    ["木板", "木板", None],
                    ["木板", "木板", None],
                    [None, None, None]
                ],
                "children": [
                    {
                        "itemId": "planks",
                        "itemName": "木板",
                        "itemIcon": "/icons/planks.png",
                        "itemDesc": "木板是由原木制作的基础建筑材料。",
                        "recipe": [
                            [None, "原木", None],
                            [None, None, None],
                            [None, None, None]
                        ],
                        "children": []
                    }
                ]
            },
            "面包": {
                "itemId": "bread",
                "itemName": "面包",
                "itemIcon": "/icons/bread.png",
                "itemDesc": "面包是一种基础食物，可以恢复5点饥饿值。",
                "recipe": [
                    ["小麦", "小麦", "小麦"],
                    [None, None, None],
                    [None, None, None]
                ],
                "children": [
                    {
                        "itemId": "wheat",
                        "itemName": "小麦",
                        "itemIcon": "/icons/wheat.png",
                        "itemDesc": "小麦是通过种植小麦种子获得的农作物。",
                        "recipe": [
                            [None, None, None],
                            [None, "小麦种子", None],
                            [None, None, None]
                        ],
                        "children": []
                    }
                ]
            }
        }
