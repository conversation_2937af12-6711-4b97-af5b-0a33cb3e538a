@startuml RAG应用架构

' 核心接口
interface Runnable {
  +invoke(query, config, **kwargs)
}

' 数据库模型
package "数据库模型" {
  class Base <<ORM>> {
  }
  
  class User {
    +id: Integer
    +username: String
    +email: String
    +hashed_password: String
    +created_at: DateTime
    +is_active: Boolean
    +queries: List<Query>
    +conversations: List<Conversation>
  }
  
  class Query {
    +id: Integer
    +user_id: Integer
    +text: Text
    +answer: Text
    +created_at: DateTime
    +user: User
  }
  
  class Conversation {
    +id: Integer
    +user_id: Integer
    +title: String
    +created_at: DateTime
    +updated_at: DateTime
    +user: User
    +messages: List<Message>
  }
  
  class Message {
    +id: Integer
    +conversation_id: Integer
    +role: String
    +content: Text
    +created_at: DateTime
    +conversation: Conversation
  }
  
  Base <|-- User
  Base <|-- Query
  Base <|-- Conversation
  Base <|-- Message
  
  User "1" -- "many" Query: owns 
  User "1" -- "many" Conversation: owns 
  Conversation "1" -- "many" Message: contains 
}

' 检索器组件
package "检索器组件" {
  class SourcedRetriever {
    +retriever: Runnable
    +source_name: String
    +invoke(query, config, **kwargs)
    +get_relevant_documents(query, **kwargs)
    +with_config(**kwargs)
    +with_retry(**kwargs)
    +with_timeout(timeout, **kwargs)
  }
  
  class MetadataMatchingRetriever {
    +documents: List<Document>
    +llm: ChatOpenAI
    +k: Integer
    +mod_names: Set
    +item_names: Set
    +invoke(query, config, **kwargs)
    +_extract_entities(query)
    +_normalize_name(name)
    +with_config(**kwargs)
    +with_retry(**kwargs)
    +with_timeout(timeout, **kwargs)
  }
  
  class EnsembleRetriever {
    +retrievers: List<Retriever>
    +weights: List<float>
    +invoke(query)
  }
  
  Runnable <|.. SourcedRetriever
  Runnable <|.. MetadataMatchingRetriever
}

' FastAPI应用组件
package "API组件" {
  class FastAPI {
    +state
    +include_router()
  }
  
  class BaseModel <<Pydantic>> {
  }
  
  class QueryRequest {
    +query: String
  }
  
  class AnswerResponse {
    +answer: String
  }
  
  BaseModel <|-- QueryRequest
  BaseModel <|-- AnswerResponse
}

' 工具类
package "工具类" {
  class Utils {
    +{static} generate_conversation_title(llm, user_query)
    +{static} process_query_message(app, conversation_id, query, db)
  }
  
  class Auth {
    +{static} get_current_user(token, db)
  }
  
  class Database {
    +{static} init_db()
    +{static} get_db()
    +{static} verify_password(plain_password, hashed_password)
    +{static} get_password_hash(password)
    +{static} create_access_token(data)
    +{static} verify_token(token)
  }
}

' 主应用
class Application {
  +ensemble_retriever: EnsembleRetriever
  +filter: EmbeddingsRedundantFilter
  +llm: ChatOpenAI
  +prompt: ChatPromptTemplate
  +memory: ConversationBufferMemory
  +lifespan(app)
}

' 关系
Application --> FastAPI: 构建 
SourcedRetriever --> "1" Runnable: 封装 
EnsembleRetriever --> "many" SourcedRetriever: 组合 
Application --> EnsembleRetriever: 使用 
Utils ..> Application: 使用 
Auth ..> User: 验证 
Application ..> Database: 使用 

@enduml