{"prompt_type": "事实性问题", "system_prompt": "你是一个事实性问题生成专家。", "user_prompt": "事实性问题是指答案唯一准确的问题。请根据大纲，生成事实性问题。生成步骤和要求如下：\n    1、先从大纲中筛选可以构成事实性问题的key，value对；\n    2、直接生成或重新总结事实性问题的key，value对：如果value类型属于物品、生物、方块、模组、其他术语，名词或名词短语，则保持不变，否则根据value重新总结生成key，value对，如果总结的value仍然是一个句子，则丢弃这个key，value对；\n    3、根据key，value对生成事实性问题以及对应的答案。要求问题指向明确，有唯一标准答案，问题类型尽量丰富多样，不要多个问题全是一种答案类型。答案包含的类型如下：物品、生物、方块、模组、其他术语，名词或名词短语。\n    \n    以下是一组示例问题和答案，旨在指导您如何格式化生成事实性问题以及对应的答案：\n    示例问题：\n    [\n        {{\n            \"问题类型\": \"事实性问题\",\n            \"问题\": \"命令方块属于哪个模组？\",\n            \"答案\": \"我的世界原版(Minecraft)。\"\n        }}\n        {{\n            \"问题类型\": \"事实性问题\",\n            \"问题\": \"通过什么命令可以获取命令方块？\",\n            \"答案\": \"/give @p minecraft:command_block 64。\"\n        }},\n        {{\n            \"问题类型\": \"事实性问题\",\n            \"问题\": \"命令方块的爆炸抗性是多少？\",\n            \"答案\": \"3600000。\"\n        }},\n        {{\n            \"问题类型\": \"事实性问题\",\n            \"问题\": \"命令方块的GUI界面通过哪种操作可以打开？\",\n            \"答案\": \"对放下的命令方块按下使用键（鼠标右键）。\"\n        }},\n        {{\n            \"问题类型\": \"事实性问题\",\n            \"问题\": \"命令方块能否被生存模式玩家破坏？\",\n            \"答案\": \"不能。\"\n        }}\n    ]\n\n    拒绝生成的问题和答案示例：\n    [\n        {{\n            \"问题类型\": \"事实性问题\",\n            \"问题\": \"命令方块无法执行什么命令？\",\n            \"答案\": \"/kick\"\n            \"原因\": \"命令方块不能执行的很可能不止/kick，所以不要制造歧义。\"\n        }},\n        {{\n            \"问题类型\": \"事实性问题\",\n            \"问题\": \"哪位开发者是命令方块的作者？\",\n            \"答案\": \"命令方块的作者\"\n            \"原因\": \"答案重复了一遍问题，本质上没有回答问题，这种是无效问题，不要生成。\"\n        }}\n    ]\n    \n    请参照上述示例的结构，根据提供的大纲，构建问题类型为事实性问题，问题内容以及答案。以json数组格式输出。\n\n    大纲如下：```json\n{outline}```\n"}
{"prompt_type": "多跳推理问题", "system_prompt": "你是一个生成多跳推理问题的专家。", "user_prompt": "多跳推理类问题要求我们从大纲中的多个相关事件和细节出发，通过逻辑推理，层层递进，最终得到问题的答案。多跳推理问题需要涵盖多个不同的步骤或逻辑连接点，以保证推理的连贯性和复杂性。请遵循以下步骤来精确构建多跳推理问题：\n\n1、确定起点和终点：从大纲中选择一个明确的起点和终点，分析两者之间的逻辑关系。\n2、设计中间步骤：在起点和终点之间设计多个逻辑推理步骤，每个步骤都应当与前后相关联，形成一个推理链。\n3、构建问题：将这些步骤串联成一个完整的问题，问题应当要求回答者依次通过所有推理步骤，才能得出最终答案。确保问题表达清晰，逻辑连贯。\n4、生成答案：根据问题和大纲生成答案，确保答案的推理过程和步骤完整、合理。\n\n示例问题类型，起点、终点，中间步骤，问题内容，答案：\n[\n    {{\n        \"问题类型\": \"多跳推理问题\",\n        \"起点\": \"熔岩的物理特性\",\n        \"终点\": \"利用熔岩在Java版1.17版本后实现无限再生\",\n        \"中间步骤\": [\"熔岩在主世界和下界的流动差异\", \"滴水石块与滴水石锥的交互\", \"炼药锅的填充机制\"],\n        \"问题\": \"Java版1.17之后，玩家如何利用滴水石块、滴水石锥以及炼药锅的特定交互机制，实现熔岩的无限再生？\",\n        \"答案\": \"自1.17起，如果将**滴水石块放置在熔岩源下方**，并在其下方悬挂**滴水石锥**，再于滴水石锥下方放置**炼药锅**，那么熔岩粒子会逐渐滴落并缓慢填充炼药锅，最终在炼药锅中生成新的熔岩。这种方式使得熔岩成为了可再生资源，并且新生成的熔岩不会对玩家造成伤害。\"\n    }},\n    {{\n        \"问题类型\": \"多跳推理问题\",\n        \"起点\": \"电脑（Computer Craft）模组中小乌龟的优势\",\n        \"终点\": \"在Gregtech5初期进行自动化生产\",\n        \"中间步骤\": [\"成本\", \"空间占用\", \"效率\"],\n        \"问题\": \"在电脑（Computer Craft）模组中，小乌龟如何凭借其在成本、空间占用、效率等方面的优势，帮助玩家在Gregtech5初期实现自动化生产的？\",\n        \"答案\": \"在电脑（Computer Craft）模组中，小乌龟在Gregtech5初期实现自动化生产的优势体现在多方面：首先，其制造成本低廉，材料主要为金和少量钻石，能源消耗极少甚至不需要用电；其次，作为一个3x1x3的整体设备，它能有效节约空间；效率方面，平均1秒处理一组材料，且能完美兼容加速火把，大幅提升物品吞吐速度；这些综合优势使得玩家可以在游戏前期以较低的门槛搭建起自动化生产线，从而在Gregtech5初期高效地进行生产。\"\n    }}\n]\n\n请参照上述示例的结构，根据提供的大纲，构建问题类型为多跳推理问题，起点、终点，中间步骤，问题内容以及答案。以json数组格式输出。\n\n大纲如下：```json\n{outline}```\n"}
{"prompt_type": "总结性问题", "system_prompt": "你是一个生成总结性问题的专家。", "user_prompt": "总结性问题要求我们综合大纲中的关键信息，对物品功能、生物特性、模组教程等方面进行全面总结。总结性问题应当涵盖多个方面的信息，要求回答者对大纲中的信息进行综合分析和提炼。请遵循以下步骤来精确构建总结性问题：\n\n1、确定总结内容：分析大纲，确定需要总结的内容和信息范围，如物品功能、生物特性、模组教程等。\n2、提炼关键信息：从大纲中提炼出与总结内容相关的关键事件、数据或策略。\n3、构建问题：根据提炼出的关键信息，构建一个全面且明确的总结性问题。问题应当要求回答者对多个方面的信息进行综合分析和总结。\n4、生成答案：根据问题和大纲生成答案，确保答案涵盖所有关键点，逻辑清晰，表述准确。\n\n示例问题类型，总结内容，问题内容，答案：\n[\n    {{\n        \"问题类型\": \"总结性问题\",\n        \"总结内容\": \"僵尸的头的获取、特性与用途\",\n        \"问题\": \"请总结僵尸的头在原版Minecraft中的主要获取途径、其作为物品和方块的特性，以及它在游戏中的多种用途。\",\n        \"答案\": \"僵尸的头在Minecraft中主要通过**闪电苦力怕炸死僵尸**时掉落，也可通过**合成**（僵尸精华和空白头骨）或**肢解僵尸**获得。作为物品，它是一种**可穿戴物品**，佩戴后可**减小僵尸发现玩家的范围50%**。作为方块，它是一种**固体透明方块**，具有5的爆炸抗性和1的硬度。在用途方面，僵尸的头可以用于**合成烟火之星**。此外，自1.19.3快照22w46a起，将其放置在**音符盒顶面**时，音符盒被激活会**播放僵尸的叫声**。\"\n    }},\n    {{\n        \"问题类型\": \"总结性问题\",\n        \"总结内容\": \"Minecraft物品耐久度的机制与修复方法\",\n        \"问题\": \"请总结Minecraft中物品耐久度的显示机制、不同类型物品的耐久度消耗规则以及玩家修复受损物品的多种方法。\",\n        \"答案\": \"Minecraft中物品的耐久度通过**物品栏下方的进度条**显示，从绿色逐渐变为红色直至消耗殆尽并消失。具体数值可通过**F3+H键**查看。耐久度消耗规则因物品类型而异：**武器和工具**攻击生物每次消耗1点或2点耐久，破坏方块消耗1点或2点；**护甲**在玩家受到其可减免的伤害时会降低耐久度，灵魂疾行和荆棘附魔也会额外消耗耐久。修复受损物品有多种方法：可在**合成栏或砂轮中合并同种受损物品**，加和耐久并额外获得5%耐久但会损失非诅咒附魔；也可在**铁砧中合并修复**，加和耐久并额外获得12%耐久，保留附魔，但消耗经验；或者在**铁砧中将工具与原材料合成修复**，每个原材料恢复1/4耐久。带有**耐久、经验修补或无法损坏标签**的物品能有效延长寿命或不消耗耐久。\"\n    }},\n    {{\n        \"问题类型\": \"总结性问题\",\n        \"总结内容\": \"林业模组中染色玻璃的合成与历史地位\",\n        \"问题\": \"请总结林业模组中染色玻璃的合成方式，并说明其在Minecraft原版加入染色玻璃后的历史地位变化。\",\n        \"答案\": \"在林业模组中，染色玻璃可以通过**热电子合成器**合成，配方为**1个骨粉、1个蜂胶和1个打蜡工具**，产出**白色染色玻璃**。理论上，通过蜂胶与对应的染料可以制作出16种染色玻璃和最后的无色玻璃。然而，在Minecraft原版**1.7.2版本加入染色玻璃后**，林业模组中的染色玻璃已**逐渐被玩家遗忘**，因为原版提供了更直接的获取方式。\"\n    }}\n]\n\n请参照上述示例的结构，根据提供的大纲，构建问题类型为总结性问题，总结内容，问题内容以及答案。以json数组格式输出。\n\n大纲如下：```json\n{outline}```\n"}
{"prompt_type": "多文档信息整合问题", "system_prompt": "你是一个问题答案生成专家。", "user_prompt": "多文档信息整合问题要求从多个文章大纲汇集多个信息片段才能得出答案。本任务的目标是需要识别并利用两篇或多篇文章大纲之间的共同点，以此为基础生成多文档信息整合问题。请遵循以下步骤：\n    1、寻找共同点：分析提供的文章大纲，找出它们之间的共同点，以关键词形式给出，每个共同点只给出一个关键词。\n    2、构建问题：基于每个共同点设计一个问题，不要包含多个子问题，确保回答这个问题需要综合两篇大纲中的信息。要求表达清晰、准确，指向明确避免歧义，每个问题中不要包含两个或多个子问题。\n    3、生成答案：根据共同点和问题生成答案。确保问题的答案需要从大纲中的多个信息片段汇集并整合生成。答案要求逻辑清晰，语句通顺连贯。\n\n    示例问题类型，共同点，问题，答案：\n    [\n        {{\n        \"问题类型\": \"多文档信息整合问题\",\n        \"共同点\": \"树苗\",\n        \"问题\": \"林业模组中的重蚁树树苗和落叶松树苗在游戏中分别是如何获取的？\",\n        \"答案\": \"在林业模组中，**重蚁树树苗**是通过**柚树苗和黑橡树苗杂交**获得的，并且需要2x2的空间才能种植；**落叶松树苗**则是通过**落叶松树苗与红果云杉树苗杂交**获得的。\"\n        }},\n        {{\n        \"问题类型\": \"多文档信息整合问题\",\n        \"共同点\": \"复合工具强化\",\n        \"问题\": \"在化学模组中，将溴、钙和铍装载到复合工具中，分别能为工具带来哪些不同的强化能力？\",\n        \"答案\": \"在化学模组中，将**溴**装载到复合工具中，能使其获得**提炼金矿**的能力。装载**钙**则能使其获得**“获得额外的骨头”**的能力。而装载**铍**能使复合工具获得**斩首能力**。\"\n    }},\n\n    ]\n    拒绝生成的问题示例：\n    [\n        {{\n            \"问题类型\": \"多文档信息整合问题\",\n            \"共同点\": \"法器\",\n            \"问题\": \"在辉夜姬的五难题模组中，博丽的驱魔棒和幽幽子的扇子各自的合成材料和主要用途是什么？\",\n            \"解释\": \"问题中包含两个子问题：博丽的驱魔棒和幽幽子的扇子各自的合成材料是什么；博丽的驱魔棒和幽幽子的扇子各自的主要用途是什么？这是不被允许的，每个问题中不能包含两个或多个子问题。\"\n        }}\n    ]\n\n    请参照上述示例的结构，根据提供的文章大纲，构建问题类型为多文档信息整合问题，共同点，问题内容以及答案。以json数组格式输出。\n    大纲如下：```json\n{outline}```\n"}
{"prompt_type": "多文档数值比较问题", "system_prompt": "你是一个问题答案生成专家。", "user_prompt": "多文档比较问题要求根据两篇文章大纲中提供的信息，将不同物品、生物、方块的特定属性或数值进行对比，从而深入理解不同事物在同一方面的区别。请遵循以下步骤来构建多文档比较问题：\n    1、寻找共同点：分析文章大纲，找出可以进行对比的多个词条，以关键词形式给出，每个共同点只给出一个关键词。\n    2、构建问题：设计问题时，确保问题的答案需要综合文章大纲中的多个部分。问题要求表达清晰、准确，指向明确避免歧义，注意问题中的属性或数值一定要有可比性，不是两个简单问题的拼接。\n    3、生成答案：根据共同点和问题生成答案。确保问题的答案需要从大纲中的多个信息片段汇集并整合生成。答案要求逻辑清晰，语句通顺连贯。\n\n    示例问题类型，共同点，问题，答案：\n    [\n        {{\n            \"问题类型\": \"多文档对比问题\",\n            \"共同点\": \"镐\",\n            \"问题\": \"比较《我的世界》中金镐和木镐的挖掘速度特性，哪种镐在挖掘速度方面具有优势？\",\n            \"答案\": \"**金镐具有原版最高的x12倍挖掘速度加成**，远高于木镐的x1倍挖掘速度加成。\"\n        }},\n        {{\n            \"问题类型\": \"多文档对比问题\",\n            \"共同点\": \"食物\",\n            \"问题\": \"比较Minecraft中马铃薯和胡萝卜的作用，哪种食物提供的饥饿值更多？\"\n            \"答案\": \"胡萝卜提供3点饥饿值，多于马铃薯提供的1点饥饿值。\"\n        }}\n    ]\n    \n    请参照上述示例的结构，根据提供的文章大纲，构建问题类型为多文档对比分析问题，共同点，问题内容以及答案。以json数组格式输出。\n    大纲如下：```json\n{outline}```"}
{"prompt_type": "多文档时间序列问题", "system_prompt": "你是一个问题答案生成专家。", "user_prompt": "时间序列比较问题要求根据两篇文章大纲中提供的信息，将不同公司的相关事件发生的时间进行对比，从而深入理解公司重大事件的时间线。请遵循以下步骤来构建时间序列比较问题：\n1、寻找共同点：分析文章大纲，找出可以进行时间比较的多个事件，以关键词形式给出，每个共同点只给出一个关键词。\n2、构建问题：设计问题时，确保问题的答案需要综合文章大纲中的多个部分。问题要求表达清晰、准确，指向明确避免歧义，注意问题中的事件时间一定要有可比性，不是两个简单问题的拼接。\n3、生成答案：根据共同点和问题生成答案。确保问题的答案需要从大纲中的多个信息片段汇集并整合生成。答案要求逻辑清晰，语句通顺连贯。\n\n示例问题类型，共同点，问题，答案：\n[\n    {{\n        \"问题类型\": \"多文档时间序列问题\",\n        \"共同点\": \"资产收购\",\n        \"问题\": \"比较Apple公司和Google公司分别进行的资产收购时间，哪家公司收购时间更早？\",\n        \"答案\": \"Google公司的资产收购时间更早。\"\n    }},\n    {{\n        \"问题类型\": \"多文档时间序列问题\",\n        \"共同点\": \"产品发布\",\n        \"问题\": \"比较Apple公司和Samsung公司分别进行的最新旗舰手机发布时间，哪家公司发布时间更早？\",\n        \"答案\": \"Samsung公司发布时间更早。\"\n    }},\n    {{\n        \"问题类型\": \"多文档时间序列问题\",\n        \"共同点\": \"业务扩展\",\n        \"问题\": \"比较2021年Amazon公司和2022年Microsoft公司分别进行的业务扩展时间，哪家公司扩展时间更早？\",\n        \"答案\": \"2021年Amazon公司扩展时间更早。\"\n    }}\n]\n\n请参照上述示例的结构，根据提供的文章大纲，构建问题类型为时间序列比较问题，共同点，问题内容以及答案。以json数组格式输出。\n大纲1如下：```json\n{outline_1}```\n大纲2如下：```json\n{outline_2}```\n"}
{"prompt_type": "单文档reference抽取", "system_prompt": "你是一个针对问题匹配对应参考答案的专家。", "user_prompt": "请根据提供的问题和答案，在文章中找到问题对应的reference并根据reference对答案进行优化，请按以下步骤进行：\n    1、根据提供的问题和答案，在文章中找到问题的来源片段，也就是reference。reference如果出现多个句子均为reference，请以数组的格式将它们组织在一起。你需要找到文章中所有的reference，不要只找其中的一两句话，需要把其中所有相关的片段进行召回，reference可以参考答案要点，但不仅仅局限于答案，在文章中尽可能多的找出reference。reference尽可能不要是总结句，如果reference是总结句，请务必将答案中所有提到的信息点对应的单独句子描述整合到reference数组中。reference必须是原文中的原句，不要进行删减或者改写。如果没有找到对应的reference，请输出空字符串，不要找无关的句子。\n    2、根据reference对现有的答案进行优化，如果reference中的内容答案中没有的则对答案进行补充，如果答案中有内容在reference中没有对应，先检查文章中是否有该内容对应的reference，如果有遗漏则把它添加到reference中，并保持答案内容不变，如果文章中没有对应的reference则在答案中把无关内容删除。答案不要根据reference堆叠，要求逻辑清晰，语句通顺，简洁明了。如果发现根据现有的reference无法得到该问题的答案，则答案字段输出无法回答。\n    \n    以下是一组示例问题及答案和reference，指导您如何格式化生成：\n\n    示例问题、reference和答案：\n    [\n        {{\n            \"问题类型\": \"事实性问题\",\n            \"问题\": \"命令方块的GUI界面通过哪种操作可以打开？\",\n            \"ref\": [\n                \"对放下的命令方块按下使用键（鼠标右键）即可出现命令方块的GUI界面。\"\n            ],\n            \"答案\": \"对放下的命令方块按下使用键（鼠标右键）。\"\n        }},\n        {{\n            \"问题类型\": \"事实性问题\",\n            \"问题\": \"附魔台的高度是多少？\",\n            \"ref\": [\n                \"\"\n            ],\n            \"答案\": \"无法回答。\"\n        }},\n        {{\n            \"问题类型\": \"多跳推理问题\",\n            \"问题\": \"Java版1.17之后，玩家如何利用滴水石块、滴水石锥以及炼药锅的特定交互机制，实现熔岩的无限再生？\",\n            \"ref\": [\n                \"在Java版1.17之后，熔岩终于可再生了。\",\n                \"如果熔岩源下方的滴水石块带有滴水石锥，且下方有炼药锅，那么熔岩粒子逐渐滴落并缓慢填充炼药锅，成为新的熔岩，但是这没有伤害。\"\n            ],\n            \"答案\": \"自1.17起，如果将**滴水石块放置在熔岩源下方**，并在其下方悬挂**滴水石锥**，再于滴水石锥下方放置**炼药锅**，那么熔岩粒子会逐渐滴落并缓慢填充炼药锅，最终在炼药锅中生成新的熔岩。这种方式使得熔岩成为了可再生资源，并且新生成的熔岩不会对玩家造成伤害。\"\n        }},\n        {{\n            \"问题类型\": \"总结性问题\",\n            \"问题\": \"请总结林业模组中染色玻璃的合成方式，并说明其在Minecraft原版加入染色玻璃后的历史地位变化。\",\n            \"ref\": [\n                \"[{{'workbench': '热电子合成器', 'inputs': '骨粉 * 1 蜂胶 * 1 打蜡工具 * 1', 'outputs': '白色染色玻璃 * 1'}}]\",\n                \"被染过色的玻璃. 通过蜂胶与对应的染料便可将做出染色玻璃。一共16种玻璃+最后的无色玻璃。\",\n                \"此物品在MC原版1.7.2中加入染色玻璃后已逐渐被玩家遗忘。\"\n            ],\n            \"答案\": \"在林业模组中，染色玻璃可以通过**热电子合成器**合成，配方为**1个骨粉、1个蜂胶和1个打蜡工具**，产出**白色染色玻璃**。理论上，通过蜂胶与对应的染料可以制作出16种染色玻璃和最后的无色玻璃。然而，在Minecraft原版**1.7.2版本加入染色玻璃后**，林业模组中的染色玻璃已**逐渐被玩家遗忘**，因为原版提供了更直接的获取方式。\"\n        }}\n    ]\n\n    错误的reference召回如下：\n    [\n        {{\n            \"问题类型\": \"事实性问题\",\n            \"问题\": \"附魔台的高度是多少？\",\n            \"ref\": [\n                \"当有玩家靠近附魔台时，附魔台上的书会自动面向玩家并自己翻开。\"\n            ],\n            \"答案\": \"无法回答。\"\n            \"原因\": \"reference中的句子不能直接回答问题，仅仅是提到玩家靠近附魔台时的动画。这样的reference是错误的召回。正确的应该是不召回任何片段并输出无法回答。\"\n        }}\n    ]\n    \n    请参照上述示例的结构，根据提供的问题，构建对应的reference以及答案。以json数组格式输出。\n\n    文章如下：```json\n{outline}```\n    问题如下：```json\n{qa_pairs}```"}
{"prompt_type": "多文档reference抽取", "system_prompt": "你是一个针对问题匹配对应参考答案的专家。", "user_prompt": "请根据提供的问题和答案，在文章中找到问题对应的reference并根据reference对答案进行优化，请按以下步骤进行：\n    1、根据提供的问题和答案，在文章中找到问题的来源片段，也就是reference并注明文章来源。reference如果出现多个句子均为reference，请以数组的格式将它们组织在一起。你需要找到文章中所有的reference，不要只找其中的一两句话，需要把其中所有相关的片段进行召回，reference可以参考答案要点，但不仅仅局限于答案，在文章中尽可能多的找出reference。reference尽可能不要是总结句，如果reference是总结句，请务必将答案中所有提到的信息点对应的单独句子描述整合到reference数组中。reference必须是原文中的原句，不要进行删减或者改写。如果没有找到对应的reference，请输出空字符串，不要找无关的句子。\n    2、根据reference对现有的答案进行优化，如果reference中的内容答案中没有的则对答案进行补充，如果答案中有内容在reference中没有对应，先检查文章中是否有该内容对应的reference，如果有遗漏则把它添加到reference中，并保持答案内容不变，如果文章中没有对应的reference则在答案中把无关内容删除。答案不要根据reference堆叠，要求逻辑清晰，语句通顺，简洁明了。如果发现根据现有的reference无法得到该问题的答案，则答案字段输出无法回答。\n\n    以下是一组示例问题及答案和reference，指导您如何格式化生成：\n\n    示例问题类型、问题及答案：\n    [\n        {{\n            \"问题类型\": \"多文档信息整合问题\",\n            \"共同点\": \"技术创新\",\n            \"问题\": \"考虑到技术创新的影响，XYZ科技公司和ABC科技公司分别如何利用其最新技术提升了产品竞争力？\",\n            \"ref\": [\n                {{\n                    \"公司名\": \"XYZ科技公司\",\n                    \"content\": \"XYZ科技公司2023年推出了新一代智能手机，采用了先进的芯片技术和摄像头技术，提升了产品性能和用户体验。\"\n                }},\n                {{\n                    \"公司名\": \"ABC科技公司\",\n                    \"content\": \"ABC科技公司2023年发布了新款平板电脑，采用了全新的操作系统和显示屏技术，增强了产品的功能和设计感。\"\n                }}\n            ],\n            \"答案\": \"XYZ科技公司2023年推出了新一代智能手机，采用了先进的芯片技术和摄像头技术，提升了产品性能和用户体验。ABC科技公司2023年发布了新款平板电脑，采用了全新的操作系统和显示屏技术，增强了产品的功能和设计感。\"\n        }},\n        {{\n            \"问题类型\": \"多文档对比问题\",\n            \"共同点\": \"营业收入\",\n            \"问题\": \"比较2021年星空航空公司和2021年银河航空公司的营业收入，哪家公司的营业收入更高？\"\n            \"ref\": [\n                {{\n                    \"公司名\": \"星空航空公司\",\n                    \"content\": \"2021年星空航空公司营业收入为10亿美元。\"\n                }},\n                {{\n                    \"公司名\": \"银河航空公司\",\n                    \"content\": \"2021年银河航空公司营业收入为8亿美元。\"\n                }}\n            ],\n            \"答案\": \"2021年星空航空公司营业收入更高。\"\n        }},\n        {{\n        \"问题类型\": \"多文档时间序列问题\",\n        \"共同点\": \"资产收购\",\n        \"问题\": \"比较Apple公司和Google公司分别进行的资产收购时间，哪家公司收购时间更早？\",\n        \"ref\": [\n            {{\n                \"公司名\": \"Apple公司\",\n                \"content\": \"Apple公司在2023年进行了资产收购。\"\n            }},\n            {{\n                \"公司名\": \"Google公司\",\n                \"content\": \"Google公司在2022年进行了资产收购。\"\n            }}\n        ],\n        \"答案\": \"Google公司的资产收购时间更早。\"\n    }},\n    ]\n    \n    文章1如下：```json\n{outline_1}```, \n    文章2如下: ```json\n{outline_2}```,\n    问题如下：```json\n{qa_pairs}```\n    \n    请严格按照上述示例的结构，根据提供问题类型和问题，构建对应的reference及答案。以json格式输出。你需要回答问题列表中的所有问题而不是第一个问题。每个问题都需要生成对应的reference，不要只生成问题答案。\n    "}
