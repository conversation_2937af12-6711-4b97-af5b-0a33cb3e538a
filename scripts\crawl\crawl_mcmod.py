# import requests
# from bs4 import BeautifulSoup
# import re
# import os
# import time
# from pathlib import Path

# def crawl_mcmod_classes(url, output_file):
#     """
#     Crawl mcmod.cn to extract class IDs from mod listings.
    
#     Args:
#         url (str): The URL to crawl (e.g., https://www.mcmod.cn/modlist.html?mcver=1.20.1&page=1)
#         output_file (str): Path to the output file where class IDs will be saved
#     """
#     print(f"Crawling {url}...")
    
#     headers = {
#         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
#     }
    
#     try:
#         response = requests.get(url, headers=headers)
#         response.raise_for_status()
        
#         soup = BeautifulSoup(response.text, 'html.parser')
        
#         # Find the modlist-list-frame div
#         modlist_frame = soup.select_one('div.modlist-list-frame')
        
#         if not modlist_frame:
#             print("Couldn't find the modlist-list-frame element.")
#             return
        
#         # Find all modlist-block divs
#         mod_blocks = modlist_frame.select('div.modlist-block')
        
#         class_ids = []
#         for block in mod_blocks:
#             # Look for href attributes that match the pattern /class/NUMBER.html
#             links = block.select('a[href^="/class/"]')
#             for link in links:
#                 href = link.get('href')
#                 match = re.search(r'/class/(\d+)\.html', href)
#                 if match:
#                     class_id = match.group(1)
#                     class_ids.append(class_id)
        
#         # Remove duplicates while preserving order
#         unique_class_ids = []
#         for class_id in class_ids:
#             if class_id not in unique_class_ids:
#                 unique_class_ids.append(class_id)
        
#         # Write to file
#         with open(output_file, 'a', encoding='utf-8') as f:
#             for class_id in unique_class_ids:
#                 f.write(f"{class_id}\n")
        
#         print(f"Extracted {len(unique_class_ids)} unique class IDs from {url}")
        
#     except requests.exceptions.RequestException as e:
#         print(f"Error fetching the URL: {e}")
#     except Exception as e:
#         print(f"An error occurred: {e}")

# def crawl_multiple_pages(base_url, start_page, end_page, output_file):
#     """
#     Crawl multiple pages of mcmod.cn.
    
#     Args:
#         base_url (str): The base URL without page parameter
#         start_page (int): Starting page number
#         end_page (int): Ending page number
#         output_file (str): Path to the output file
#     """
#     # Create a new file or clear existing one
#     with open(output_file, 'w', encoding='utf-8'):
#         pass
        
#     for page in range(start_page, end_page + 1):
#         url = f"{base_url}&page={page}"
#         crawl_mcmod_classes(url, output_file)
        
#         # Add delay to avoid overwhelming the server
#         if page < end_page:
#             print(f"Waiting before crawling next page...")
#             time.sleep(1)

# if __name__ == "__main__":
#     # Base URL for Minecraft 1.20.1 mods
#     BASE_URL = "https://www.mcmod.cn/modlist.html?mcver=1.20.1"
    
#     # Create output directory if it doesn't exist
#     output_dir = Path("./")
#     output_dir.mkdir(parents=True, exist_ok=True)
    
#     # Output file path
#     output_file = output_dir / "class_ids.txt"
    
#     # Crawl first 5 pages (adjust as needed)
#     crawl_multiple_pages(BASE_URL, 1, 289, output_file)
    
#     print(f"Crawling complete. Class IDs saved to {output_file}")

import requests
from bs4 import BeautifulSoup
import re
import os
import time
import random
import logging
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# --- Configuration ---
NUM_WORKERS = 10
BASE_URL = "https://www.mcmod.cn/modlist.html?mcver=1.20.1"
START_PAGE = 1
END_PAGE = 289 # Adjust as needed
OUTPUT_DIR = Path("./")
OUTPUT_FILE = OUTPUT_DIR / "class_ids.txt"
LOG_FILENAME = 'crawl_mcmod_log.txt'
REQUEST_TIMEOUT = 15 # seconds
MAX_RETRIES = 2 # Retries per page before giving up
MIN_DELAY = 0.5 # Minimum delay between requests for a worker (seconds)
MAX_DELAY = 1.5 # Maximum delay between requests for a worker (seconds)

USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) RAG-Data-Collector/1.0' # Custom agent example
]

# --- Logging Setup ---
logging.basicConfig(filename=LOG_FILENAME, level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(threadName)s - %(message)s')

def my_log(msg):
    print(msg)
    logging.info(msg)

# --- Proxy Management ---
# Global proxy list - populated by proxy_init
proxies = []
# Use a lock for thread-safe proxy refresh if needed, though each worker refreshes its own slot
proxy_lock = threading.Lock()

def proxy_init(num_proxies=NUM_WORKERS):
    """Initialize the specified number of proxies."""
    # !! IMPORTANT: Replace with your actual proxy provider API endpoint and credentials !!
    # This is an example structure based on crawl_item.py
    # Ensure your API call fetches 'num_proxies' IPs.
    api_url = f"https://dps.kdlapi.com/api/getdps/?secret_id=o908aykc7vhhaftk9iz6&signature=5818amln1aur09moioq1sie91299nzlv&num={num_proxies}&pt=1&sep=1" # EXAMPLE URL
    my_log(f"Initializing {num_proxies} proxies...")
    try:
        response = requests.get(api_url, timeout=20)
        response.raise_for_status()
        proxy_ip_list = [ip.strip() for ip in response.text.split('\r\n') if ip.strip()]

        if len(proxy_ip_list) < num_proxies:
             # Decide how to handle this: raise error, use fewer workers, proceed with caution?
            my_log(f"Warning: Requested {num_proxies} proxies, but only got {len(proxy_ip_list)}. Adjusting worker count or expect potential issues.")
            # For simplicity, we'll proceed but log the warning. A robust solution might adjust NUM_WORKERS.

        # !! IMPORTANT: Replace with your actual proxy authentication format !!
        initialized_proxies = [
            {
                "http":  f"http://d3758262232:e6upmnib@{ip}/", # EXAMPLE FORMAT
                "https": f"http://d3758262232:e6upmnib@{ip}/"  # EXAMPLE FORMAT
            } for ip in proxy_ip_list
        ]
        my_log(f"Successfully initialized {len(initialized_proxies)} proxies.")
        return initialized_proxies
    except requests.exceptions.RequestException as e:
        my_log(f"Fatal Error: Proxy initialization failed: {str(e)}")
        raise # Stop execution if proxies can't be initialized
    except Exception as e:
        my_log(f"Fatal Error: An unexpected error occurred during proxy initialization: {str(e)}")
        raise

def refresh_proxy(worker_id):
    """Refresh the proxy for a specific worker ID."""
    # !! IMPORTANT: Replace with your actual proxy provider API endpoint and credentials !!
    # This is an example structure based on crawl_item.py for fetching ONE new IP
    api_url = "https://dps.kdlapi.com/api/getdps/?secret_id=o908aykc7vhhaftk9iz6&signature=5818amln1aur09moioq1sie91299nzlv&num=1&pt=1&sep=1" # EXAMPLE URL
    my_log(f"Worker {worker_id}: Refreshing proxy...")
    global proxies
    try:
        response = requests.get(api_url, timeout=20)
        response.raise_for_status()
        proxy_ip = response.text.strip()

        if proxy_ip and worker_id < len(proxies):
             # !! IMPORTANT: Replace with your actual proxy authentication format !!
            new_proxy = {
                "http":  f"http://d3758262232:e6upmnib@{proxy_ip}/", # EXAMPLE FORMAT
                "https": f"http://d3758262232:e6upmnib@{proxy_ip}/"  # EXAMPLE FORMAT
            }
            # Although direct assignment might seem non-threadsafe, each worker manages its own slot.
            # If multiple workers called refresh *simultaneously*, they'd get different IPs anyway.
            # A lock is safer if global state modification becomes more complex.
            # with proxy_lock:
            proxies[worker_id] = new_proxy
            my_log(f"Worker {worker_id}: Proxy updated to {proxy_ip}")
            return True
        elif not proxy_ip:
            my_log(f"Worker {worker_id}: Failed to get a new proxy IP (empty response).")
        else:
             my_log(f"Worker {worker_id}: Invalid worker_id for proxy list.")
        return False
    except requests.exceptions.RequestException as e:
        my_log(f"Worker {worker_id}: Error refreshing proxy: {str(e)}")
        return False
    except Exception as e:
         my_log(f"Worker {worker_id}: An unexpected error occurred during proxy refresh: {str(e)}")
         return False

# --- Crawling Logic ---
def crawl_page(page_num, worker_id):
    """
    Crawl a single page to extract class IDs using the assigned worker's proxy.

    Args:
        page_num (int): The page number to crawl.
        worker_id (int): The ID of the worker (used to select proxy).

    Returns:
        list: A list of unique class IDs found on the page, or None if failed.
    """
    url = f"{BASE_URL}&page={page_num}"
    my_log(f"Worker {worker_id}: Crawling page {page_num} ({url})")
    headers = {'User-Agent': random.choice(USER_AGENTS)}
    session = requests.Session() # Use session for potential connection pooling benefits

    for attempt in range(MAX_RETRIES):
        time.sleep(random.uniform(MIN_DELAY, MAX_DELAY)) # Delay before each attempt
        proxy = proxies[worker_id] # Get the current proxy for this worker

        try:
            response = session.get(
                url,
                headers=headers,
                proxies=proxy,
                timeout=REQUEST_TIMEOUT
            )

            # --- Handle Specific Status Codes ---
            if response.status_code == 403:
                my_log(f"Worker {worker_id}: Page {page_num} - Access Forbidden (403). Refreshing proxy.")
                if not refresh_proxy(worker_id):
                    my_log(f"Worker {worker_id}: Page {page_num} - Failed to refresh proxy after 403. Aborting retries for this page.")
                    return None # Give up on this page if proxy refresh fails
                continue # Retry with the new proxy

            if response.status_code == 404:
                 my_log(f"Worker {worker_id}: Page {page_num} - Not Found (404). Skipping.")
                 return [] # Treat as success, but no data

            # Raise exceptions for other bad status codes (e.g., 5xx)
            response.raise_for_status()

            # --- Process Successful Response ---
            response.encoding = 'utf-8' # Ensure correct encoding
            soup = BeautifulSoup(response.text, 'html.parser')
            modlist_frame = soup.select_one('div.modlist-list-frame')

            if not modlist_frame:
                my_log(f"Worker {worker_id}: Page {page_num} - Couldn't find 'modlist-list-frame'. Skipping.")
                return [] # Page structure might be different, treat as empty

            mod_blocks = modlist_frame.select('div.modlist-block')
            class_ids_on_page = set() # Use a set for efficient uniqueness check on this page
            for block in mod_blocks:
                links = block.select('a[href^="/class/"]')
                for link in links:
                    href = link.get('href')
                    match = re.search(r'/class/(\d+)\.html', href)
                    if match:
                        class_id = match.group(1)
                        class_ids_on_page.add(class_id)

            my_log(f"Worker {worker_id}: Page {page_num} - Extracted {len(class_ids_on_page)} unique class IDs.")
            return list(class_ids_on_page) # Return list from set

        except requests.exceptions.Timeout:
             my_log(f"Worker {worker_id}: Page {page_num} - Request timed out (Attempt {attempt + 1}/{MAX_RETRIES}). Refreshing proxy.")
             refresh_proxy(worker_id) # Refresh proxy on timeout
             # Continue to next retry attempt

        except requests.exceptions.RequestException as e:
            my_log(f"Worker {worker_id}: Page {page_num} - Request failed (Attempt {attempt + 1}/{MAX_RETRIES}): {e}. Refreshing proxy.")
            refresh_proxy(worker_id) # Refresh proxy on general request errors
            # Continue to next retry attempt

        except Exception as e:
            my_log(f"Worker {worker_id}: Page {page_num} - An unexpected error occurred (Attempt {attempt + 1}/{MAX_RETRIES}): {e}")
            # Optionally refresh proxy here too, depending on error type
            # Continue to next retry attempt

    # If loop finishes without returning (i.e., all retries failed)
    my_log(f"Worker {worker_id}: Page {page_num} - Failed to crawl after {MAX_RETRIES} attempts.")
    return None


def worker_task(worker_id, pages_to_crawl):
    """
    Task executed by each worker thread. Crawls assigned pages.

    Args:
        worker_id (int): The ID of the worker.
        pages_to_crawl (list): List of page numbers assigned to this worker.

    Returns:
        list: A list of all unique class IDs found by this worker across its assigned pages.
    """
    worker_results = set()
    my_log(f"Worker {worker_id}: Starting task, assigned {len(pages_to_crawl)} pages.")

    for page_num in pages_to_crawl:
        page_result = crawl_page(page_num, worker_id)
        if page_result is not None: # Successfully crawled (even if empty list)
            worker_results.update(page_result) # Add found IDs to the worker's set
        else:
            # Log failure for this page, already logged in crawl_page
            pass

    my_log(f"Worker {worker_id}: Finished task, found {len(worker_results)} unique IDs.")
    return list(worker_results)


# --- Main Execution ---
if __name__ == "__main__":
    start_time = time.perf_counter()

    # Initialize Proxies
    try:
        proxies = proxy_init(NUM_WORKERS)
        if len(proxies) < NUM_WORKERS:
             my_log(f"Warning: Could not obtain {NUM_WORKERS} proxies. Proceeding with {len(proxies)}.")
             actual_workers = len(proxies)
        else:
             actual_workers = NUM_WORKERS

        if actual_workers == 0:
             my_log("Fatal Error: No proxies available. Exiting.")
             exit(1)

    except Exception as e:
        my_log(f"Fatal Error: Could not initialize proxies. Exiting. Error: {e}")
        exit(1)


    # Create output directory
    OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

    # Distribute pages to workers
    all_pages = list(range(START_PAGE, END_PAGE + 1))
    pages_per_worker = [[] for _ in range(actual_workers)]
    for i, page in enumerate(all_pages):
        worker_index = i % actual_workers
        pages_per_worker[worker_index].append(page)

    all_found_class_ids = set()
    total_pages_processed = 0
    total_pages_failed = 0 # Keep track if needed

    my_log(f"Starting crawl with {actual_workers} workers for pages {START_PAGE} to {END_PAGE}.")

    with ThreadPoolExecutor(max_workers=actual_workers, thread_name_prefix='Crawler') as executor:
        # Submit tasks
        futures = {executor.submit(worker_task, wid, pages_per_worker[wid]): wid for wid in range(actual_workers)}

        # Process completed tasks
        for future in as_completed(futures):
            worker_id = futures[future]
            try:
                worker_result = future.result() # This is the list of unique IDs from that worker
                if worker_result is not None:
                    count_before = len(all_found_class_ids)
                    all_found_class_ids.update(worker_result)
                    count_after = len(all_found_class_ids)
                    my_log(f"Worker {worker_id} completed. Added {len(worker_result)} IDs ({count_after - count_before} new).")
                else:
                    # Should not happen if worker_task returns list, but handle defensively
                    my_log(f"Worker {worker_id} completed but returned None (unexpected).")

            except Exception as e:
                my_log(f"Worker {worker_id} generated an exception: {e}")
                # Decide how to handle worker exceptions (e.g., log, retry worker, ignore)


    # --- Final Output ---
    my_log(f"Crawling complete. Aggregated results from all workers.")
    final_unique_ids = sorted(list(all_found_class_ids), key=int) # Sort numerically

    try:
        with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
            for class_id in final_unique_ids:
                f.write(f"{class_id}\n")
        my_log(f"Successfully wrote {len(final_unique_ids)} unique class IDs to {OUTPUT_FILE}")
    except IOError as e:
         my_log(f"Error writing output file {OUTPUT_FILE}: {e}")


    elapsed = time.perf_counter() - start_time
    my_log(f"Total execution time: {elapsed:.2f} seconds")