"""
文档管理 API 路由模块

此模块定义了与文档管理相关的 API 端点，
包括添加文档到向量数据库。
"""
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List, Optional, Dict, Any

from ..dependencies import get_document_service, get_current_user
from ..services.document_service import DocumentService
from ..database import User

# 创建路由
router = APIRouter(prefix="/documents", tags=["documents"])

# 请求和响应模型
class DocumentCreate(BaseModel):
    title: str
    content: str
    modename: Optional[str] = None
    url: Optional[str] = None

class DocumentCreateResponse(BaseModel):
    ids: List[str]
    message: str

class DocumentDeleteRequest(BaseModel):
    document_id: str

class DocumentDeleteResponse(BaseModel):
    success: bool
    message: str

# 添加新文档
@router.post("", response_model=DocumentCreateResponse)
async def add_document(
    document_data: DocumentCreate,
    current_user: User = Depends(get_current_user),
    service: DocumentService = Depends(get_document_service)
):
    """
    添加新文档到向量数据库
    
    - **title**: 文档标题
    - **content**: 文档内容
    - **modename**: 模组名称，可选
    - **url**: 源网址，可选
    """
    try:
        ids = await service.add_document(
            title=document_data.title,
            content=document_data.content,
            modename=document_data.modename,
            url=document_data.url
        )
        
        return DocumentCreateResponse(
            ids=ids,
            message=f"成功添加了 {len(ids)} 个文档片段"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"添加文档失败: {str(e)}")

# 删除文档
@router.delete("", response_model=DocumentDeleteResponse)
async def delete_document(
    document_data: DocumentDeleteRequest,
    current_user: User = Depends(get_current_user),
    service: DocumentService = Depends(get_document_service)
):
    """
    从向量数据库中删除文档
    
    - **document_id**: 要删除的文档ID
    """
    success = await service.delete_document(document_data.document_id)
    
    if success:
        return DocumentDeleteResponse(
            success=True,
            message="文档删除成功"
        )
    else:
        raise HTTPException(status_code=500, detail="文档删除失败")
