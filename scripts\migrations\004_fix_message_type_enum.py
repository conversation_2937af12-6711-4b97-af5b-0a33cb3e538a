"""
数据库迁移脚本：修复MessageType枚举值不匹配问题
Migration: Fix MessageType enum value mismatch

此脚本修复MessageType枚举值不匹配的问题，将数据库中的枚举值从小写改为大写，
以匹配SQLAlchemy枚举的期望值。
"""

import sys
import os
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.rag.config import DATABASE_URL

def run_migration():
    """执行数据库迁移：修复MessageType枚举值不匹配问题"""
    print("开始执行数据库迁移：修复MessageType枚举值不匹配问题...")

    # 创建数据库连接
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    with SessionLocal() as session:
        try:
            # 检查当前枚举定义
            result = session.execute(text("""
                SELECT COLUMN_TYPE 
                FROM information_schema.COLUMNS 
                WHERE TABLE_NAME = 'messages' 
                AND COLUMN_NAME = 'type'
            """))
            
            current_enum = result.fetchone()
            if not current_enum:
                print("✗ 未找到messages表的type字段")
                return False
                
            current_enum_def = current_enum[0]
            print(f"当前枚举定义: {current_enum_def}")
            
            # 如果已经是大写枚举，跳过迁移
            if "TEXT" in current_enum_def and "CRAFTING" in current_enum_def:
                print("枚举定义已经是大写格式，跳过迁移")
                return True
            
            # 检查当前数据分布
            print("\n检查当前数据分布...")
            result = session.execute(text("SELECT type, COUNT(*) as count FROM messages GROUP BY type"))
            current_data = result.fetchall()
            print("当前数据分布:")
            for row in current_data:
                print(f"  type: '{row[0]}', count: {row[1]}")
            
            # 步骤1：更新现有数据
            print("\n步骤1：更新现有数据...")
            
            # 更新 'text' -> 'TEXT'
            result = session.execute(text("UPDATE messages SET type = 'TEXT' WHERE type = 'text'"))
            text_updated = result.rowcount
            print(f"更新了 {text_updated} 条 'text' -> 'TEXT' 记录")
            
            # 更新 'crafting' -> 'CRAFTING'
            result = session.execute(text("UPDATE messages SET type = 'CRAFTING' WHERE type = 'crafting'"))
            crafting_updated = result.rowcount
            print(f"更新了 {crafting_updated} 条 'crafting' -> 'CRAFTING' 记录")
            
            # 步骤2：修改枚举定义
            print("\n步骤2：修改枚举定义...")
            
            # 临时删除外键约束
            foreign_key_dropped = False
            try:
                session.execute(text("ALTER TABLE messages DROP FOREIGN KEY messages_ibfk_1"))
                foreign_key_dropped = True
                print("临时删除了外键约束")
            except Exception as e:
                print(f"删除外键约束失败（可能不存在）: {e}")
            
            # 修改type字段的枚举定义
            session.execute(text("""
                ALTER TABLE messages 
                MODIFY COLUMN type ENUM('TEXT', 'CRAFTING') NOT NULL DEFAULT 'TEXT'
            """))
            print("修改了type字段的枚举定义为 ENUM('TEXT', 'CRAFTING')")
            
            # 重新添加外键约束
            if foreign_key_dropped:
                try:
                    session.execute(text("""
                        ALTER TABLE messages 
                        ADD CONSTRAINT messages_ibfk_1 
                        FOREIGN KEY (conversation_id) REFERENCES conversations(id)
                    """))
                    print("重新添加了外键约束")
                except Exception as e:
                    print(f"重新添加外键约束失败: {e}")
            
            # 步骤3：验证修复结果
            print("\n步骤3：验证修复结果...")
            
            # 检查更新后的数据
            result = session.execute(text("SELECT type, COUNT(*) as count FROM messages GROUP BY type"))
            updated_data = result.fetchall()
            print("修复后数据分布:")
            for row in updated_data:
                print(f"  type: '{row[0]}', count: {row[1]}")
            
            # 检查新的枚举定义
            result = session.execute(text("""
                SELECT COLUMN_TYPE 
                FROM information_schema.COLUMNS 
                WHERE TABLE_NAME = 'messages' 
                AND COLUMN_NAME = 'type'
            """))
            new_enum = result.fetchone()
            if new_enum:
                print(f"新的type字段定义: {new_enum[0]}")
            
            # 提交更改
            session.commit()
            print("\n✓ 数据库迁移完成！")
            return True
            
        except Exception as e:
            print(f"\n✗ 迁移过程中发生错误: {e}")
            session.rollback()
            import traceback
            traceback.print_exc()
            return False

def rollback_migration():
    """回滚数据库迁移：将枚举值改回小写"""
    print("开始回滚数据库迁移：将MessageType枚举值改回小写...")

    # 创建数据库连接
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    with SessionLocal() as session:
        try:
            # 检查当前枚举定义
            result = session.execute(text("""
                SELECT COLUMN_TYPE 
                FROM information_schema.COLUMNS 
                WHERE TABLE_NAME = 'messages' 
                AND COLUMN_NAME = 'type'
            """))
            
            current_enum = result.fetchone()
            if not current_enum:
                print("✗ 未找到messages表的type字段")
                return False
                
            current_enum_def = current_enum[0]
            print(f"当前枚举定义: {current_enum_def}")
            
            # 如果已经是小写枚举，跳过回滚
            if "text" in current_enum_def and "crafting" in current_enum_def:
                print("枚举定义已经是小写格式，跳过回滚")
                return True
            
            # 步骤1：更新现有数据
            print("\n步骤1：更新现有数据...")
            
            # 更新 'TEXT' -> 'text'
            result = session.execute(text("UPDATE messages SET type = 'text' WHERE type = 'TEXT'"))
            text_updated = result.rowcount
            print(f"更新了 {text_updated} 条 'TEXT' -> 'text' 记录")
            
            # 更新 'CRAFTING' -> 'crafting'
            result = session.execute(text("UPDATE messages SET type = 'crafting' WHERE type = 'CRAFTING'"))
            crafting_updated = result.rowcount
            print(f"更新了 {crafting_updated} 条 'CRAFTING' -> 'crafting' 记录")
            
            # 步骤2：修改枚举定义
            print("\n步骤2：修改枚举定义...")
            
            # 临时删除外键约束
            foreign_key_dropped = False
            try:
                session.execute(text("ALTER TABLE messages DROP FOREIGN KEY messages_ibfk_1"))
                foreign_key_dropped = True
                print("临时删除了外键约束")
            except Exception as e:
                print(f"删除外键约束失败（可能不存在）: {e}")
            
            # 修改type字段的枚举定义
            session.execute(text("""
                ALTER TABLE messages 
                MODIFY COLUMN type ENUM('text', 'crafting') NOT NULL DEFAULT 'text'
            """))
            print("修改了type字段的枚举定义为 ENUM('text', 'crafting')")
            
            # 重新添加外键约束
            if foreign_key_dropped:
                try:
                    session.execute(text("""
                        ALTER TABLE messages 
                        ADD CONSTRAINT messages_ibfk_1 
                        FOREIGN KEY (conversation_id) REFERENCES conversations(id)
                    """))
                    print("重新添加了外键约束")
                except Exception as e:
                    print(f"重新添加外键约束失败: {e}")
            
            # 提交更改
            session.commit()
            print("\n✓ 数据库回滚完成！")
            return True
            
        except Exception as e:
            print(f"\n✗ 回滚过程中发生错误: {e}")
            session.rollback()
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="修复MessageType枚举值不匹配问题")
    parser.add_argument("--rollback", action="store_true", help="回滚迁移")
    
    args = parser.parse_args()
    
    if args.rollback:
        success = rollback_migration()
        if success:
            print("\n数据库回滚成功！")
        else:
            print("\n数据库回滚失败！")
    else:
        success = run_migration()
        if success:
            print("\n数据库迁移成功！")
        else:
            print("\n数据库迁移失败！")
