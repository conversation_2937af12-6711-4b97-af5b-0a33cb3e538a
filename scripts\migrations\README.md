# 数据库迁移说明

本目录包含数据库迁移脚本，用于管理数据库结构的变更。

## 迁移文件

### 001_add_conversation_type.py
为对话表添加类型字段，支持以下两种对话类型：
- `NORMAL`: 普通对话
- `SYNTHETIC_NAVIGATION`: 合成导航

### 002_add_message_type_and_item_name.py
为消息表添加类型和物品名称字段，支持合成导航功能：
- `type`: 消息类型枚举 ('text', 'crafting') - 注意：后续通过004迁移修改为大写
- `item_name`: 物品名称字段（仅crafting类型消息使用）

### 003_add_crafting_contexts_table.py
创建合成上下文表，用于存储对话级别的合成树信息：
- 存储目标物品和完整合成树JSON
- 与对话表建立一对一关系
- 支持级联删除

### 004_fix_message_type_enum.py
修复MessageType枚举值不匹配问题：
- 将数据库中的枚举值从小写 ('text', 'crafting') 改为大写 ('TEXT', 'CRAFTING')
- 更新现有数据以匹配新的枚举定义
- 解决SQLAlchemy枚举处理时的LookupError问题

**问题背景**：
SQLAlchemy的枚举处理机制要求数据库中的枚举值与Python枚举类中定义的值完全匹配。
当数据库中存储的是小写值 ('text') 而Python代码期望大写值 ('TEXT') 时，
会出现 `LookupError: 'text' is not among the defined enum values` 错误。

## 使用方法

### 1. 执行所有迁移
```bash
cd scripts
python migrate.py --all
```

### 2. 执行特定迁移
```bash
cd scripts
python migrate.py --migration conversation_type
```

### 3. 回滚特定迁移
```bash
cd scripts
python migrate.py --rollback conversation_type
```

### 4. 回滚所有迁移
```bash
cd scripts
python migrate.py --rollback-all
```

### 5. 执行MessageType枚举修复（单独执行）
```bash
cd scripts/migrations
python 004_fix_message_type_enum.py
```

### 6. 回滚MessageType枚举修复
```bash
cd scripts/migrations
python 004_fix_message_type_enum.py --rollback
```

### 7. 列出所有迁移文件
```bash
cd scripts
python migrate.py --list
```

## 直接执行单个迁移脚本

你也可以直接执行单个迁移脚本：

```bash
cd scripts
python migrations/001_add_conversation_type.py
```

回滚单个迁移：
```bash
cd scripts
python migrations/001_add_conversation_type.py --rollback
```

## 注意事项

1. **备份数据库**: 在执行迁移前，请确保备份数据库
2. **测试环境**: 建议先在测试环境中执行迁移
3. **权限检查**: 确保数据库用户有足够的权限执行DDL操作
4. **依赖关系**: 迁移脚本按文件名顺序执行，请确保依赖关系正确

## 迁移脚本结构

每个迁移脚本应包含以下函数：
- `run_migration()`: 执行迁移的主函数
- `rollback_migration()`: 回滚迁移的函数

## 数据库连接

迁移脚本使用项目配置文件中的数据库连接信息。请确保：
1. 数据库服务正在运行
2. 配置文件中的数据库连接信息正确
3. 数据库用户有足够的权限

## 故障排除

### 常见错误

1. **连接错误**: 检查数据库服务是否运行，连接信息是否正确
2. **权限错误**: 确保数据库用户有CREATE、ALTER、DROP等权限
3. **字段已存在**: 如果字段已存在，迁移会自动跳过
4. **MessageType枚举错误**: 如果遇到 `LookupError: 'text' is not among the defined enum values`，请执行004迁移修复

### 手动检查

你可以手动检查迁移结果：

```sql
-- 检查conversations表结构
DESCRIBE conversations;

-- 检查type字段的值
SELECT type, COUNT(*) FROM conversations GROUP BY type;

-- 检查索引
SHOW INDEX FROM conversations;

-- 检查messages表的type字段枚举定义
SELECT COLUMN_TYPE
FROM information_schema.COLUMNS
WHERE TABLE_NAME = 'messages'
AND COLUMN_NAME = 'type';

-- 检查messages表中type字段的值分布
SELECT type, COUNT(*) as count FROM messages GROUP BY type;
```
