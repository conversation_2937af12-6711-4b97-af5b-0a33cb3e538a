# 通用LLM配置（默认配置，如果没有为特定任务配置则使用这些）
LLM_MODEL=gpt-3.5-turbo
BASE_URL=https://api.openai.com/v1
API_KEY=your-openai-api-key

# RAG系统专用LLM配置
RAG_LLM_MODEL=gpt-3.5-turbo
RAG_BASE_URL=https://api.openai.com/v1
RAG_API_KEY=your-openai-api-key
RAG_TEMPERATURE=0.1

# 标题生成专用LLM配置（可以使用更轻量级的模型）
TITLE_LLM_MODEL=gpt-3.5-turbo
TITLE_BASE_URL=https://api.openai.com/v1
TITLE_API_KEY=your-openai-api-key
TITLE_TEMPERATURE=0.3

# 元数据匹配专用LLM配置（可以使用更轻量级的模型）
METADATA_LLM_MODEL=gpt-3.5-turbo
METADATA_BASE_URL=https://api.openai.com/v1
METADATA_API_KEY=your-openai-api-key
METADATA_TEMPERATURE=0.0

# 意图分类专用LLM配置（可以使用更轻量级的模型）
INTENT_LLM_MODEL=gpt-3.5-turbo
INTENT_BASE_URL=https://api.openai.com/v1
INTENT_API_KEY=your-openai-api-key
INTENT_TEMPERATURE=0.0

# 向量数据库和Embedding模型配置
BAAI_PATH=BAAI/bge-large-zh-v1.5
CHROMA_PATH=./chroma_db
DEVICE=cpu

# 重排序模型配置
RERANK_MODEL_NAME=BAAI/bge-reranker-base
RERANK_TOP_K=8
RERANK_BATCH_SIZE=8

# 查询扩展配置
QUERY_EXPANSION_ENABLED=true
QUERY_EXPANSION_TEMPERATURE=0.2
QUERY_EXPANSION_LLM_MODEL=THUDM/GLM-4-9B-0414
QUERY_EXPANSION_BASE_URL=https://api.siliconflow.cn/v1
QUERY_EXPANSION_API_KEY=your-api-key

# 运行环境
ENVIRONMENT=development

# MySQL数据库配置
DATABASE_URL=mysql+pymysql://root:password@localhost/rag_db

# JWT配置
SECRET_KEY=your-secret-key-change-this-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=60

# 调试请求密钥
DUMP_KEY=set-to-a-safe-key

# ========================================
# 合成导航功能配置 (Crafting Navigation)
# ========================================

# 物品实体识别LLM配置
# 用于从用户消息中智能识别Minecraft物品实体
ITEM_EXTRACTION_MODEL=THUDM/GLM-4-9B-0414
ITEM_EXTRACTION_BASE_URL=https://api.siliconflow.cn/v1
ITEM_EXTRACTION_API_KEY=your-siliconflow-api-key
# 物品识别的温度参数，建议使用较低值确保识别准确性
ITEM_EXTRACTION_TEMPERATURE=0.1

# Neo4j图数据库配置（用于真实的合成树数据）
# 当前阶段使用Mock数据，Neo4j集成为未来功能
NEO4J_ENABLED=false
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=your-neo4j-password

# ========================================
# 应用程序配置
# ========================================

# 日志级别配置
LOG_LEVEL=INFO

# CORS配置（允许的前端域名）
CORS_ALLOWED_ORIGINS=http://localhost:8080,http://127.0.0.1:8080

# 录制功能开关（用于调试和分析）
RECORDING_ENABLED=false

# 检索系统超参数
RETRIEVER_INITIAL_K=15