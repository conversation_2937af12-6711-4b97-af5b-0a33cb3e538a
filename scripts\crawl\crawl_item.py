import logging
import os
import random
import re
import time
import requests
from bs4 import BeautifulSoup
import json
from concurrent.futures import ThreadPoolExecutor, as_completed

USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) RAG-Data-Collector/1.0'
]

proxies = []

def proxy_init():
    """初始化10个代理"""
    api_url = "https://dps.kdlapi.com/api/getdps/?secret_id=ocj9drbbrtvcx5dz5noo&signature=3l06tgp34sbarv971vh4scbltjw49vdm&num=10&pt=1&sep=1"
    try:
        proxy_ip = requests.get(api_url).text
        proxy_list = [ip.strip() for ip in proxy_ip.split('\r\n') if ip.strip()]

        if len(proxy_list) != 10:
            raise ValueError(f"获取代理数量不足10个，实际获取{len(proxy_list)}个")

        return [
            {
                "http":  f"http://d3886795008:301hierq@{ip}/",
                "https": f"http://d3886795008:301hierq@{ip}/"
            } for ip in proxy_list
        ]
    except Exception as e:
        logging.error(f"代理初始化失败: {str(e)}")
        raise

def refresh_proxy(worker_id):
    """刷新指定worker的代理"""
    api_url = "https://dps.kdlapi.com/api/getdps/?secret_id=ocj9drbbrtvcx5dz5noo&signature=3l06tgp34sbarv971vh4scbltjw49vdm&num=1&pt=1&sep=11"
    try:
        proxy_ip = requests.get(api_url).text.strip()
        if proxy_ip:
            new_proxy = {
                "http":  f"http://d3886795008:301hierq@{proxy_ip}/",
                "https": f"http://d3886795008:301hierq@{proxy_ip}/"
            }
            proxies[worker_id] = new_proxy
            my_log(f"Worker {worker_id} 代理已更新为 {proxy_ip}")
        else:
            my_log(f"Worker {worker_id} 获取新代理失败，返回空IP")
    except Exception as e:
        my_log(f"Worker {worker_id} 刷新代理时出错: {str(e)}")


start_index = 1
end_index = 870000
item_folder = './itemnew'
os.makedirs(item_folder, exist_ok=True)

log_filename = 'log.txt'
logging.basicConfig(filename=log_filename, level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')


def my_log(msg):
    print(msg)
    logging.info(msg)


def clean_content(content):
    for br in content.find_all('br'):
        br.replace_with('\n')
    content_text = content.get_text(separator='\n', strip=True)
    content_text = re.sub(r'\s+', ' ', content_text)
    content_text = re.sub(r'\n+', '\n', content_text)
    return content_text


def parse_synthesis(synth_str):
    recipes = []
    for line in synth_str.split("\n"):
        if "[使用:" in line:
            workbench_match = re.search(r"\[使用: (.*?) \]", line)
            if not workbench_match:
                continue
            workbench = workbench_match.group(1)
            inputs, outputs = line.split("↓")
            inputs = inputs.split("] ")[1].strip()
            outputs = outputs.strip()
            recipes.append({
                "workbench": workbench,
                "inputs": inputs,
                "outputs": outputs
            })
    return recipes


def get_item_data(item_id, worker_id, retries=2):
    file_path = os.path.join(item_folder, f'item{item_id}.json')
    if os.path.exists(file_path):
        # my_log(f"Item {item_id} 已存在，跳过爬取")
        return True

    session = requests.Session()
    adapter = requests.adapters.HTTPAdapter(pool_connections=10, pool_maxsize=10)
    session.mount('http://', adapter)
    headers = {'User-Agent': random.choice(USER_AGENTS)}

    for attempt in range(retries):
        time.sleep(random.uniform(0.25, 0.5))
        proxy = proxies[worker_id]

        try:
            response = session.get(
                f"https://www.mcmod.cn/item/{item_id}.html",
                headers=headers,
                proxies=proxy,
                timeout=15
            )

            if response.status_code == 403:
                my_log(f"Item {item_id} 被屏蔽！尝试更换IP")
                refresh_proxy(worker_id)
                continue

            response.encoding = 'utf-8'
            soup = BeautifulSoup(response.text, 'html.parser')

            # 原有解析逻辑
            content_div = soup.find('div', {'class': 'common-center'})
            if not content_div:
                my_log(f"Item {item_id} 内容未找到")
                return False

            # 名称解析
            name_div = content_div.find('div', {'class': 'itemname'})
            full_name = name_div.find('h5').text.strip() if name_div and name_div.find('h5') else "N/A"
            match = re.match(r"(.+?) \((.+?)\)", full_name)
            chinese_name = match.group(1) if match else full_name
            english_name = match.group(2) if match else "N/A"

            # 命令解析
            give_command_div = content_div.find('div', class_='item-give')
            give_command = give_command_div.get('data-command', 'N/A') if give_command_div else 'N/A'

            # 内容处理
            content = content_div.find('div', class_='item-content')
            content_text = clean_content(content) if content else 'N/A'

            # 合成配方
            synthesis_data = []
            synthesis_tables = soup.find_all('div', class_='item-table-frame item-table-frame-out')
            
            for table in synthesis_tables:
                rows = table.find_all('tr')
                for row in rows:
                    # 获取配方内容
                    recipe_cell = row.find('td', class_='item-table-count')
                    gui_cell = row.find('td', class_='item-table-gui')
                    remarks_cell = row.find('td', class_='item-table-remarks')
                    
                    if recipe_cell:
                        text = recipe_cell.get_text(separator=' ', strip=True)
                        text = re.sub(r'\s+', ' ', text)
                        parsed_synthesis = parse_synthesis(text)
                        
                        # 提取备注信息
                        mod_requirements = []
                        additional_info = []
                        version_requirements = []
                        
                        if remarks_cell:
                            # 提取需要安装的模组
                            for mod_alert in remarks_cell.find_all('span', class_='alert-table-forother'):
                                if mod_alert:
                                    mod_link = mod_alert.find('a')
                                    if mod_link:
                                        mod_name = mod_link.get('data-original-title', '') or mod_link.text.strip()
                                        mod_url = mod_link.get('href', '')
                                        if mod_url.startswith('//'):
                                            mod_url = f'https:{mod_url}'
                                        if mod_name and mod_url:
                                            mod_requirements.append({
                                                "name": mod_name,
                                                "url": mod_url
                                            })
                            
                            # 提取版本要求信息
                            for version_alert in remarks_cell.find_all('span', class_='alert-table-startver'):
                                if version_alert and version_alert.text.strip():
                                    version_text = version_alert.text.strip()
                                    version_requirements.append(version_text)
                            
                            # 提取其他信息（例如"此xxx是由另一个模组提供的"）
                            for other_alert in remarks_cell.find_all('span', class_='alert-table-guifromother'):
                                if other_alert:
                                    other_text = other_alert.get_text(strip=True)
                                    other_links = other_alert.find_all('a')
                                    item_links = []
                                    for link in other_links:
                                        item_name = link.get('data-original-title', '') or link.text.strip()
                                        item_url = link.get('href', '')
                                        if item_url.startswith('//'):
                                            item_url = f'https:{item_url}'
                                        if item_name and item_url:
                                            item_links.append({
                                                "name": item_name,
                                                "url": item_url
                                            })
                                    additional_info.append({
                                        "text": other_text,
                                        "links": item_links
                                    })
                            
                            # 提取其他备注信息
                            remark_span = remarks_cell.find('span', class_='remark')
                            if remark_span and remark_span.text.strip():
                                additional_info.append({"text": remark_span.text.strip(), "links": []})
                        
                        # 处理合成表格GUI（仅对工作台配方）
                        crafting_grid_items = [None] * 10  # 创建长度为10的列表
                        
                        if gui_cell and any(recipe.get("workbench") == "工作台" for recipe in parsed_synthesis):
                            table_block = gui_cell.find('div', class_='TableBlock')
                            if table_block:
                                # 查找所有物品元素
                                item_divs = table_block.find_all('div', class_='item-table-hover')
                                
                                # 预定义位置映射
                                position_map = {
                                    "34px 0 0 46px": 0,   # 第一行第一列
                                    "34px 0 0 82px": 1,   # 第一行第二列
                                    "34px 0 0 118px": 2,  # 第一行第三列
                                    "70px 0 0 46px": 3,   # 第二行第一列
                                    "70px 0 0 82px": 4,   # 第二行第二列
                                    "70px 0 0 118px": 5,  # 第二行第三列
                                    "106px 0 0 46px": 6,  # 第三行第一列
                                    "106px 0 0 82px": 7,  # 第三行第二列
                                    "106px 0 0 118px": 8, # 第三行第三列
                                    "70px 0 0 234px": 9   # 输出物品
                                }
                                
                                for item_div in item_divs:
                                    # 获取样式中的margin值
                                    style = item_div.get('style', '')
                                    margin_match = re.search(r'margin:(.*?);', style)
                                    if not margin_match:
                                        continue
                                        
                                    margin = margin_match.group(1)
                                    
                                    # 确定物品在合成表中的位置
                                    position = position_map.get(margin)
                                    if position is None:
                                        continue
                                    
                                    # 提取物品信息
                                    tooltip = item_div.get('data-original-title', '')
                                    item_id = item_div.get('item-id', '')
                                    
                                    # 从tooltip中提取物品名称和mod名称
                                    name_match = re.search(r"<p class='item-table-hover-t1'>(.*?)</p>", tooltip)
                                    mod_match = re.search(r"<p class='item-table-hover-t2'><b>(.*?)</b></p>", tooltip)
                                    
                                    item_name = name_match.group(1).strip() if name_match else "未知物品"
                                    mod_name = mod_match.group(1).strip() if mod_match else "未知模组"
                                    
                                    # 提取图片URL
                                    img_tag = item_div.find('img')
                                    img_url = ""
                                    if img_tag and img_tag.get('src'):
                                        img_src = img_tag['src']
                                        img_url = f'https:{img_src}' if img_src.startswith('//') else img_src
                                    
                                    # 提取物品链接
                                    a_tag = item_div.find('a')
                                    item_url = ""
                                    if a_tag and a_tag.get('href'):
                                        href = a_tag['href']
                                        item_url = f'https:{href}' if href.startswith('//') else href
                                    
                                    # 保存物品信息到对应位置
                                    crafting_grid_items[position] = {
                                        "name": item_name,
                                        "mod": mod_name,
                                        "id": item_id,
                                        "img_url": img_url,
                                        "item_url": item_url
                                    }
                        
                        synthesis_data.append({
                            "recipe": parsed_synthesis,
                            "mod_requirements": mod_requirements,
                            "version_requirements": version_requirements,
                            "additional_info": additional_info,
                            "crafting_grid": crafting_grid_items
                        })

            # 导航信息   
            nav_info = {
                "nav1": "N/A",
                "nav2": "N/A",
                "nav3": "N/A",
                "nav4": "N/A"
            }
            
            nav_div = soup.find('div', class_='common-nav')
            if nav_div:
                nav_items = nav_div.find_all('li')
                
                for i, li in enumerate(nav_items):
                    if i == 2:
                        a_tag = li.find('a', class_='item')
                        if a_tag:
                            nav_info["nav1"] = a_tag.text.strip()
                    elif i == 4:
                        a_tag = li.find('a', class_='item')
                        if a_tag:
                            nav_info["nav2"] = a_tag.text.strip()
                    elif i == 6:
                        a_tag = li.find('a', class_='item')
                        if a_tag:
                            nav_info["nav3"] = a_tag.text.strip()
                    elif i == 8: 
                        div_tag = li.find('div', class_='item')
                        if div_tag:
                            nav_info["nav4"] = div_tag.text.strip()

            # 图片信息
            img_url = "N/A"
            item_data_div = content_div.find('div', class_='item-data')
            if item_data_div:
                img_tag = item_data_div.find('img')
                if img_tag and img_tag.get('src'):
                    img_src = img_tag['src']
                    img_url = f'https:{img_src}' if img_src.startswith('//') else img_src

            data = {
                "id": item_id,
                "chinese_name": chinese_name,
                "english_name": english_name,
                "give_command": give_command,
                "content": content_text,
                "synthesis": synthesis_data,
                "navigation": nav_info,
                "img_url": img_url
            }

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=4)
                my_log(f"Item {item_id} 保存成功")
                return True

        except Exception as e:
            my_log(f"Item {item_id} 第{attempt + 1}次尝试失败: {str(e)}")
            refresh_proxy(worker_id)
            if attempt == retries - 1:
                return False

    return False


def worker_task(worker_id):
    """处理item_id ≡ worker_id (mod 10)的所有项目"""
    current_id = start_index + ((worker_id - (start_index % 10)) % 10)

    while current_id < end_index:
        if not get_item_data(current_id, worker_id):
            my_log(f"Worker {worker_id} 处理 {current_id} 最终失败")
        current_id += 10


if __name__ == "__main__":
    try:
        proxies = proxy_init()
        my_log("代理初始化成功")
    except Exception as e:
        my_log(f"代理初始化失败: {str(e)}")
        exit(1)

    start_time = time.perf_counter()
    with ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(worker_task, wid) for wid in range(10)]
        for future in as_completed(futures):
            try:
                future.result()
            except Exception as e:
                my_log(f"Worker异常: {str(e)}")

    elapsed = time.perf_counter() - start_time
    my_log(f"总耗时: {elapsed:.2f}秒")