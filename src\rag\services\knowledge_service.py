"""
知识库服务模块

此模块提供与知识库管理相关的核心业务逻辑服务。
它封装了知识库内容的添加、获取和处理功能，支持多种文件格式。
"""

from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional, Tuple # 确保 Dict, Any, Optional, Tuple 已导入
from fastapi import HTTPException, status, UploadFile # 确保 UploadFile 已导入
import json
import os
import tempfile
# from datetime import datetime # 如果需要用到
# import shutil # 如果需要用到

from ..database import KnowledgeBaseItem, KnowledgeBaseUpload, KnowledgeBaseType, User # 确保 KnowledgeBaseType 已导入
from ..logging_config import get_logger

# 使用统一的日志配置
logger = get_logger("knowledge_service")


class KnowledgeService:
    """
    知识库服务类，提供知识库内容管理功能
    """

    def __init__(self, db: Session):
        """
        初始化知识库服务

        Args:
            db: 数据库会话
        """
        self.db = db

    async def get_knowledge_base_content(
            self,
            kb_type: KnowledgeBaseType,
            start_index: int,
            page_size: int
    ) -> Dict[str, Any]:
        """
        分页获取知识库内容

        Args:
            kb_type: 知识库类型
            start_index: 起始索引
            page_size: 每页大小

        Returns:
            Dict[str, Any]: 包含知识库内容和分页信息的字典
        """
        try:
            items_query = self.db.query(KnowledgeBaseItem).filter(
                KnowledgeBaseItem.type == kb_type
            ).order_by(KnowledgeBaseItem.created_at.desc())

            total_count = items_query.count()
            items = items_query.offset(start_index).limit(page_size).all()
            page_count = (total_count + page_size - 1) // page_size if page_size > 0 else 0

            content = []
            for item in items:
                content.append({
                    "title": item.title,
                    "content": item.content
                })

            return {
                "content": content,
                "page_count": page_count
            }

        except Exception as e:
            logger.error(f"获取知识库内容时出错: {e}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取知识库内容失败: {str(e)}"
            )

    async def upload_knowledge_base(
            self,
            files: List[UploadFile],
            metadata_list: List[Dict[str, Any]],
            admin_user: User
    ) -> Dict[str, Any]:
        """
        上传并处理知识库文件

        Args:
            files: 文件列表
            metadata_list: 每个文件对应的元数据字典列表
            admin_user: 上传的管理员用户

        Returns:
            Dict[str, Any]: 上传结果
        """
        if len(files) != len(metadata_list):
            logger.error(f"文件数量 ({len(files)}) 与元数据数量 ({len(metadata_list)}) 不匹配。")
            # 这个错误应该由路由层处理，但在这里也加一道保险
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文件数量与元数据数量不匹配。"
            )

        processed_files_count = 0
        # failed_files_info = [] # 可选：用于收集处理失败的文件信息

        # 注意：如果任何一个文件的处理失败（_process_file抛出异常），
        # 下面的循环会被中断，整个操作会进入此函数的except块。
        try:
            for i, file_item in enumerate(files):
                current_file_metadata_dict = metadata_list[i]
                logger.info(f"开始处理文件: {file_item.filename}, 元数据: {current_file_metadata_dict}")

                upload_record = KnowledgeBaseUpload(
                    filename=file_item.filename,
                    file_type=self._get_file_extension(file_item.filename or ""),
                    file_size=0,
                    kb_metadata=json.dumps(current_file_metadata_dict.get("kb_metadata", {})),
                    uploaded_by=admin_user.id,
                    processed=False, # 初始状态为未处理
                    items_count=0
                )
                self.db.add(upload_record)
                try:
                    self.db.commit()
                    self.db.refresh(upload_record)
                except Exception as e_commit_upload_record:
                    self.db.rollback()
                    logger.error(f"保存文件 '{file_item.filename}' 的上传记录到数据库失败: {e_commit_upload_record}", exc_info=True)
                    # 让外层 try...except 处理，并最终返回500错误
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail=f"保存文件 '{file_item.filename}' 的上传记录失败。"
                    ) from e_commit_upload_record


                # 单独的 try-catch 块用于 _process_file，以便在失败时仍能记录 upload_record 的状态
                try:
                    await self._process_file(file_item, current_file_metadata_dict, upload_record, admin_user)
                    # _process_file 内部成功时会 commit upload_record 的 processed=True 和 items_count
                    processed_files_count += 1
                except Exception as e_process:
                    # _process_file 内部的 except 块已经 rollback 并尝试更新 upload_record 为 processed=False
                    # 这里我们记录日志，然后重新抛出异常，中断整个批处理并返回错误给客户端
                    logger.error(f"文件 '{file_item.filename}' 的内容处理失败: {e_process}", exc_info=True)
                    # failed_files_info.append({"filename": file_item.filename, "error": str(e_process)})
                    raise # 重新抛出，由最外层的 try-except 处理

            # 如果所有文件都成功处理
            return {
                "success": True,
                "message": f"成功处理 {processed_files_count} 个文件。"
            }

        except HTTPException: # 由内部逻辑（如参数校验或_process_file中的HTTPException）抛出
            # 已经在 _process_file 中 rollback过了，这里不需要再次 rollback
            # 或者，如果 _process_file 不抛出 HTTPException，则这里的 rollback 是必要的
            # 当前设计是 _process_file 抛出通用 Exception, 这里再包装
            raise
        except Exception as e: # 捕获由 _process_file 重新抛出的通用异常或其他意外错误
            self.db.rollback() # 确保回滚
            logger.error(f"上传知识库文件批处理时发生未处理的错误: {e}", exc_info=True)
            # message = f"上传失败: {str(e)}."
            # if failed_files_info:
            #     message += f" 以下文件处理失败: {'; '.join(f['filename'] for f in failed_files_info)}"

            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"上传知识库文件时发生服务器内部错误。"
                # detail=message # 可以提供更详细的错误信息
            )


    async def _process_file(
            self,
            file_item: UploadFile,
            metadata_dict: Dict[str, Any],
            upload_record: KnowledgeBaseUpload, # 传入upload_record以更新其状态
            admin_user: User
    ) -> None:
        temp_file_path: Optional[str] = None
        try:
            logger.debug(f"正在处理文件 {file_item.filename} (临时路径将被创建)")
            temp_file_suffix = self._get_file_extension(file_item.filename or "")
            if temp_file_suffix and not temp_file_suffix.startswith('.'):
                temp_file_suffix = f".{temp_file_suffix}"

            with tempfile.NamedTemporaryFile(delete=False, suffix=temp_file_suffix) as temp_file:
                temp_file_path = temp_file.name
                contents = await file_item.read()
                temp_file.write(contents)

            logger.debug(f"文件 {file_item.filename} 已保存到临时路径: {temp_file_path}")
            upload_record.file_size = len(contents)
            # 注意：这里先不 commit file_size，等所有条目处理完后和 processed, items_count 一起提交

            file_type_ext = self._get_file_extension(file_item.filename or "").lower()
            processed_kb_items: List[Dict[str, Any]] = []

            if file_type_ext == "pdf":
                processed_kb_items = await self._process_pdf(temp_file_path, metadata_dict)
            elif file_type_ext == "html":
                processed_kb_items = await self._process_html(temp_file_path, metadata_dict)
            elif file_type_ext == "md":
                processed_kb_items = await self._process_markdown(temp_file_path, metadata_dict)
            elif file_type_ext in ["txt", "csv", "json"]:
                processed_kb_items = await self._process_text(temp_file_path, metadata_dict, file_type_ext)
            else:
                logger.warning(f"文件 {file_item.filename} 的文件类型不受支持: {file_type_ext}")
                upload_record.processed = False # 明确标记为未处理
                upload_record.items_count = 0
                self.db.add(upload_record) # 确保在会话中
                self.db.commit() # 提交 upload_record 的状态
                return # 对于不支持的文件类型，直接返回

            # 将处理结果保存到数据库
            for item_data in processed_kb_items:
                kb_type_str_from_item = item_data.get('type')
                kb_type_enum: KnowledgeBaseType
                if isinstance(kb_type_str_from_item, KnowledgeBaseType):
                    kb_type_enum = kb_type_str_from_item
                elif isinstance(kb_type_str_from_item, str):
                    try:
                        kb_type_enum = KnowledgeBaseType(kb_type_str_from_item.upper())
                    except ValueError:
                        logger.warning(f"来自处理器的知识库类型字符串 '{kb_type_str_from_item}' 无效，将回退到文件元数据。")
                        kb_type_enum = self._determine_kb_type_from_dict(metadata_dict)
                else:
                    kb_type_enum = self._determine_kb_type_from_dict(metadata_dict)

                kb_item = KnowledgeBaseItem(
                    type=kb_type_enum,
                    title=item_data.get('title', metadata_dict.get("title", "无标题条目")),
                    content=item_data.get('content', ""),
                    kb_metadata=json.dumps(item_data.get('kb_item_metadata', {})),
                    source_url=item_data.get('source_url', metadata_dict.get("source_url")),
                    created_by=admin_user.id
                )
                self.db.add(kb_item)

            upload_record.items_count = len(processed_kb_items)
            upload_record.processed = True
            self.db.add(upload_record) # 确保更新的 upload_record 在会话中
            self.db.commit() # 提交所有更改 (upload_record 更新, kb_items 添加)

            logger.info(f"成功处理文件 {file_item.filename}，提取了 {len(processed_kb_items)} 个条目")

        except Exception as e:
            self.db.rollback()
            logger.error(f"处理文件 {file_item.filename if file_item else 'N/A'} 时发生错误: {e}", exc_info=True)
            # 尝试更新 upload_record 状态为失败，即使发生错误
            # upload_record 在此函数开始时已通过 commit() 保证存在于数据库中
            upload_record.processed = False
            upload_record.items_count = 0
            try:
                self.db.add(upload_record) # 确保对象在会话中以进行更新
                self.db.commit()
                logger.info(f"已将文件 {file_item.filename if file_item else 'N/A'} 的上传记录标记为处理失败。")
            except Exception as db_err_on_fail_update:
                logger.error(f"为文件 {file_item.filename if file_item else 'N/A'} 更新错误状态失败: {db_err_on_fail_update}", exc_info=True)
                self.db.rollback() # 如果更新失败状态也出错，则再次回滚
            raise e # 将原始异常重新抛出，以便上层函数处理
        finally:
            if temp_file_path and os.path.exists(temp_file_path):
                try:
                    os.unlink(temp_file_path)
                    logger.debug(f"临时文件 {temp_file_path} 已删除。")
                except Exception as e_unlink:
                    logger.error(f"删除临时文件 {temp_file_path} 失败: {e_unlink}")

    def _get_file_extension(self, filename: str) -> str:
        if "." in filename:
            return filename.rsplit(".", 1)[1]
        return ""

    def _determine_kb_type_from_dict(self, metadata_dict: Dict[str, Any]) -> KnowledgeBaseType:
        type_str = metadata_dict.get("type", "WIKI").upper()
        try:
            return KnowledgeBaseType(type_str)
        except ValueError:
            logger.warning(f"无效的 KnowledgeBaseType 字符串 '{type_str}'，将默认使用 WIKI。")
            return KnowledgeBaseType.WIKI

    async def _process_pdf(self, file_path: str, metadata_dict: Dict[str, Any]) -> List[Dict[str, Any]]:
        items: List[Dict[str, Any]] = []
        kb_type_for_items = self._determine_kb_type_from_dict(metadata_dict)
        base_title = metadata_dict.get("title", os.path.basename(file_path))
        base_source_url = metadata_dict.get("source_url")
        try:
            from pypdf import PdfReader
            reader = PdfReader(file_path)
            for i, page in enumerate(reader.pages):
                text = page.extract_text()
                if text and text.strip():
                    items.append({
                        'type': kb_type_for_items.value, # 确保传递的是字符串值或枚举本身
                        'title': f"{base_title} - 第 {i + 1} 页",
                        'content': text,
                        'kb_item_metadata': {'page_number': i + 1, 'original_filename': os.path.basename(file_path)},
                        'source_url': base_source_url
                    })
            return items
        except ImportError:
            logger.error(f"处理PDF文件 {os.path.basename(file_path)} 失败：pypdf 库未安装。")
            items.append({'type': kb_type_for_items.value,'title': f"{base_title} - 处理错误",'content': "PDF处理失败：缺少pypdf库。",'kb_item_metadata': {'error': 'missing_library'},'source_url': base_source_url})
            return items
        except Exception as e:
            logger.error(f"处理PDF文件 {os.path.basename(file_path)} 时出错: {e}", exc_info=True)
            items.append({'type': kb_type_for_items.value,'title': f"{base_title} - 处理错误",'content': f"PDF处理错误: {str(e)}",'kb_item_metadata': {'error': str(e)},'source_url': base_source_url})
            return items

    async def _process_html(self, file_path: str, metadata_dict: Dict[str, Any]) -> List[Dict[str, Any]]:
        items: List[Dict[str, Any]] = []
        kb_type_for_items = self._determine_kb_type_from_dict(metadata_dict)
        base_title = metadata_dict.get("title", os.path.basename(file_path))
        base_source_url = metadata_dict.get("source_url")
        try:
            from bs4 import BeautifulSoup
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                html_content = f.read()
            soup = BeautifulSoup(html_content, 'html.parser')
            title_from_html = soup.title.string if soup.title else base_title
            main_content_tag = soup.find('article') or soup.find('main') or soup.body
            text_content = ""
            if main_content_tag:
                text_content = main_content_tag.get_text(separator='\n', strip=True)
            if text_content:
                 items.append({
                    'type': kb_type_for_items.value,
                    'title': title_from_html,
                    'content': text_content,
                    'kb_item_metadata': {'source_type': 'html_main_text', 'original_filename': os.path.basename(file_path)},
                    'source_url': base_source_url
                })
            return items
        except ImportError:
             logger.error(f"处理HTML文件 {os.path.basename(file_path)} 失败：BeautifulSoup4 库未安装。")
             items.append({'type': kb_type_for_items.value,'title': f"{base_title} - 处理错误",'content': "HTML处理失败：缺少BeautifulSoup4库。",'kb_item_metadata': {'error': 'missing_library'},'source_url': base_source_url})
             return items
        except Exception as e:
            logger.error(f"处理HTML文件 {os.path.basename(file_path)} 时出错: {e}", exc_info=True)
            items.append({'type': kb_type_for_items.value,'title': f"{base_title} - 处理错误",'content': f"HTML处理错误: {str(e)}",'kb_item_metadata': {'error': str(e)},'source_url': base_source_url})
            return items

    async def _process_markdown(self, file_path: str, metadata_dict: Dict[str, Any]) -> List[Dict[str, Any]]:
        items: List[Dict[str, Any]] = []
        kb_type_for_items = self._determine_kb_type_from_dict(metadata_dict)
        base_title = metadata_dict.get("title", os.path.basename(file_path))
        base_source_url = metadata_dict.get("source_url")
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                md_content = f.read()
            if md_content:
                items.append({
                    'type': kb_type_for_items.value,
                    'title': base_title,
                    'content': md_content,
                    'kb_item_metadata': {'source_type': 'markdown', 'original_filename': os.path.basename(file_path)},
                    'source_url': base_source_url
                })
            return items
        except Exception as e:
            logger.error(f"处理Markdown文件 {os.path.basename(file_path)} 时出错: {e}", exc_info=True)
            items.append({'type': kb_type_for_items.value,'title': f"{base_title} - 处理错误",'content': f"Markdown处理错误: {str(e)}",'kb_item_metadata': {'error': str(e)},'source_url': base_source_url})
            return items

    async def _process_text(self, file_path: str, metadata_dict: Dict[str, Any], file_type_ext: str) -> List[Dict[str, Any]]:
        items: List[Dict[str, Any]] = []
        kb_type_for_items = self._determine_kb_type_from_dict(metadata_dict)
        base_title = metadata_dict.get("title", os.path.basename(file_path))
        base_source_url = metadata_dict.get("source_url")
        content = ""
        item_metadata_extra = {'original_filename': os.path.basename(file_path)}

        try:
            if file_type_ext == "csv":
                import csv
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    csv_reader = csv.reader(f)
                    rows = list(csv_reader)
                if rows:
                    content = "\n".join([",".join(row) for row in rows])
                item_metadata_extra['source_type'] = 'csv'
                base_title = f"{base_title} (CSV)"
            elif file_type_ext == "json":
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    json_data = json.load(f)
                content = json.dumps(json_data, ensure_ascii=False, indent=2)
                item_metadata_extra['source_type'] = 'json'
                base_title = f"{base_title} (JSON)"
            else: # txt
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                item_metadata_extra['source_type'] = 'txt'

            if content:
                items.append({
                    'type': kb_type_for_items.value,
                    'title': base_title,
                    'content': content,
                    'kb_item_metadata': item_metadata_extra,
                    'source_url': base_source_url
                })
            return items
        except Exception as e:
            logger.error(f"处理文本文件 ({file_type_ext}) {os.path.basename(file_path)} 时出错: {e}", exc_info=True)
            items.append({'type': kb_type_for_items.value,'title': f"{base_title} - 处理错误",'content': f"文本文件({file_type_ext})处理错误: {str(e)}",'kb_item_metadata': {'error': str(e), 'source_type': file_type_ext},'source_url': base_source_url})
            return items