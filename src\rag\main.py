from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from contextlib import asynccontextmanager
from langchain_chroma import Chroma
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_community.document_transformers import EmbeddingsRedundantFilter
from langchain_core.prompts import ChatPromptTemplate
import os
import sys
from pathlib import Path
from langchain_core.documents import Document
from langchain.memory import ConversationBufferMemory
from dotenv import load_dotenv
from fastapi.middleware.cors import CORSMiddleware
from .routes import router as api_router
from .database import init_db
from . import config
# V3 阶段三：移除了 RAG_PROMPT_TEMPLATE 的导入，因为它不再用于创建传递给 RAGPipeline 的主 prompt
from .prompts import TITLE_GENERATION_PROMPT_TEMPLATE, INTENT_CLASSIFICATION_PROMPT_TEMPLATE
from .rag_pipeline.pipeline import RAGPipeline
from .rag_pipeline.retrievers.factory import RetrieverFactory
from .rag_pipeline.processors.filters import DocumentFilterManager
from .rag_pipeline.intent_processing import IntentClassifier
from .rag_pipeline.reranker import Reranker  # V3.1: 导入重排序器
from .rag_pipeline.query_enhancer import QueryEnhancer  # V3.1: 导入查询增强器
from .prompts import QUERY_EXPANSION_PROMPT_TEMPLATE  # V3.1: 导入查询扩展提示模板
from . import llm_clients  # 导入LLM客户端模块

# 初始加载环境变量
load_dotenv()
baai_path = config.BAAI_PATH
chroma_path = config.CHROMA_PATH

if not os.path.exists(baai_path):
    print(f"Warning: BAAI_PATH {baai_path} not found, checking alternative locations...")
    local_baai_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "BAAI", "bge-large-zh-v1.5")
    if os.path.exists(local_baai_path):
        print(f"Found model at {local_baai_path}")
        baai_path = local_baai_path
    else:
        print("Using model name directly from HuggingFace")
        baai_path = "BAAI/bge-large-zh-v1.5"

@asynccontextmanager
async def lifespan(app: FastAPI):
    """FastAPI 应用的生命周期管理器，用于在应用启动时初始化资源。"""
    # 初始化数据库
    init_db()
    print("数据库初始化完成")

    # 初始化应用状态
    app.state.embedding = None
    app.state.vector_db = None
    app.state.llm = None

    # 初始化嵌入模型和向量数据库
    def init_embedding_and_db():
        """初始化 HuggingFace 嵌入模型和 Chroma 向量数据库。"""
        try:
            current_baai_path = config.BAAI_PATH # 使用新的配置模块
            app.state.embedding = HuggingFaceEmbeddings(
                model_name=current_baai_path,
                model_kwargs={'device': config.DEVICE}, # 使用新的配置模块
                encode_kwargs={'normalize_embeddings': True}
            )
            print(f"嵌入模型已加载: {current_baai_path}")

            current_chroma_path = config.CHROMA_PATH # 使用新的配置模块
            if not Path(current_chroma_path).exists():
                print(f"Vector database not found at {current_chroma_path}")
                raise HTTPException(status_code=500, detail=f"Vector database not found at {current_chroma_path}")

            app.state.vector_db = Chroma(
                persist_directory=current_chroma_path,
                embedding_function=app.state.embedding
            )
            print(f"向量数据库已加载: {current_chroma_path}")
            doc_count = app.state.vector_db._collection.count()
            print(f"向量/文档总数: {doc_count}")

            # top10_entries = app.state.vector_db._collection.peek()

            # for idx, (doc_id, doc_content) in enumerate(zip(top10_entries["ids"], top10_entries["documents"])):
            #     print(f"文档 {idx+1} [ID: {doc_id}]: {doc_content}")
            #     print(f"元数据: {top10_entries['metadatas'][idx]}")
            #     print("-" * 50)

            return True
        except Exception as e:
            print(f"Error initializing embedding and vector DB: {e}")
            return False

    # 初始化LLM
    def init_llm():
        """初始化所有任务所需的 LLM 客户端实例。"""
        try:
            # 导入LLM客户端创建函数
            from .llm_clients import (
                create_rag_llm, create_title_generation_llm,
                create_metadata_matching_llm, create_intent_classification_llm,
                create_item_extraction_llm
            )

            # 创建新的LLM客户端实例
            rag_llm = create_rag_llm()
            title_llm = create_title_generation_llm()
            metadata_llm = create_metadata_matching_llm()
            intent_llm = create_intent_classification_llm()
            item_extraction_llm = create_item_extraction_llm()

            # 将app.state.llm初始化为一个字典，包含所有新创建的LLM客户端
            app.state.llm = {
                "rag": rag_llm,
                "title": title_llm,
                "metadata": metadata_llm,
                "intent": intent_llm,
                "item_extraction": item_extraction_llm
            }

            # 获取主RAG模型名称用于日志
            model_name = getattr(rag_llm, "model_name", None)
            if model_name is None:
                # 尝试获取其他可能的属性名
                model_name = getattr(rag_llm, "_model", getattr(rag_llm, "model_name_or_path", "未知模型"))

            print(f"LLM已加载: {model_name}")
            return True
        except Exception as e:
            print(f"Error initializing LLM: {e}")
            return False

    # 配置热重载逻辑已移除

    # 初始化组件
    if not init_embedding_and_db():
        sys.exit(1)

    if not init_llm():
        sys.exit(1)

    # 从配置中读取一次 k 值
    retriever_k_value = config.RETRIEVER_INITIAL_K # 使用新的配置模块
    print(f"Retriever K value set to: {retriever_k_value}")

    # RetrieverFactory 和 ensemble_retriever 的创建已移除，
    # 因为 app.state.ensemble 不再需要，且 RAGPipeline 内部处理其检索器。
    # 创建冗余过滤器
    redundant_filter = DocumentFilterManager.create_redundant_filter(
        app.state.embedding
    )

    # 创建提示模板
    # V3 阶段三：为 RAGPipeline 中的 LLMChain 提供一个简单的模板，
    # 因为完整的 Prompt 已在 pipeline 内部由 format_unified_rag_prompt 构建。
    # LLMChain 的输入将是 {"question": final_prompt_string, "chat_history": ...}
    simple_llm_prompt = ChatPromptTemplate.from_template("{question}")
    title_prompt = ChatPromptTemplate.from_template(TITLE_GENERATION_PROMPT_TEMPLATE)
    intent_prompt = ChatPromptTemplate.from_template(INTENT_CLASSIFICATION_PROMPT_TEMPLATE)

    # 创建意图分类器
    intent_classifier = IntentClassifier(app.state.llm["intent"])

    # 创建重排序器
    reranker = Reranker(
        model_name=config.RERANK_MODEL_NAME,
        device_str=config.DEVICE
    )

    # 创建查询增强器
    # 创建专用的查询扩展LLM客户端
    query_expansion_llm = llm_clients.create_query_expansion_llm()
    app.state.llm["query_expansion"] = query_expansion_llm

    query_enhancer = QueryEnhancer(
        llm=query_expansion_llm,  # 使用专用的查询扩展LLM
        expansion_prompt_template_str=QUERY_EXPANSION_PROMPT_TEMPLATE
    )

    # 移除旧的 RetrieverFactory 实例化，RAGPipeline 将在内部创建它

    # 获取 k 值用于 RAGPipeline 初始化 - 已移到前面统一读取

    # 创建RAG Pipeline
    # RAGPipeline 的 __init__ 已修改，不再接受 retriever_factory
    # 而是接受 vector_db, metadata_llm, k
    rag_pipeline = RAGPipeline(
        vector_db=app.state.vector_db,
        metadata_llm=app.state.llm["metadata"],
        k=retriever_k_value, # 使用统一的 k 值
        filter=redundant_filter,
        llm=app.state.llm["rag"],
        prompt=simple_llm_prompt, # 使用 V3 调整后的简单模板
        title_llm=app.state.llm["title"],
        title_prompt=title_prompt,
        intent_classifier=intent_classifier,
        reranker=reranker,  # V3.1: 添加重排序器
        query_enhancer=query_enhancer  # V3.1: 添加查询增强器
    )

    # 初始化合成导航组件
    from .crafting import ItemEntityExtractor, CraftingTreeGenerator, CraftingRAGPipeline

    # 创建物品实体识别器
    item_entity_extractor = ItemEntityExtractor(
        llm=app.state.llm["item_extraction"],
        temperature=config.ITEM_EXTRACTION_TEMPERATURE
    )

    # 创建合成树生成器
    crafting_tree_generator = CraftingTreeGenerator(
        neo4j_enabled=config.NEO4J_ENABLED
    )

    # 创建合成导航RAG Pipeline
    crafting_rag_pipeline = CraftingRAGPipeline(
        item_extractor=item_entity_extractor,
        tree_generator=crafting_tree_generator,
        base_rag_pipeline=rag_pipeline
    )

    # 保存组件到应用状态
    app.state.filter = redundant_filter
    app.state.prompt = simple_llm_prompt # 确保 app.state 也使用更新后的 prompt
    app.state.intent_classifier = intent_classifier
    app.state.reranker = reranker  # V3.1: 保存重排序器
    app.state.query_enhancer = query_enhancer  # V3.1: 保存查询增强器
    app.state.rag_pipeline = rag_pipeline

    # 保存合成导航组件
    app.state.item_entity_extractor = item_entity_extractor
    app.state.crafting_tree_generator = crafting_tree_generator
    app.state.crafting_rag_pipeline = crafting_rag_pipeline

    print("应用初始化完成（包含合成导航功能）") # 移除了热重载逻辑
    yield

app = FastAPI(lifespan=lifespan)

app.add_middleware(
    CORSMiddleware,
    allow_origins=config.CORS_ALLOWED_ORIGINS, # 使用新的配置模块
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加API路由
app.include_router(api_router)

class QueryRequest(BaseModel):
    query: str

class AnswerResponse(BaseModel):
    answer: str


# 移除了未使用的 import_time 函数