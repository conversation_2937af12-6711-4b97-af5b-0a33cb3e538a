import json
import re
import os
from pathlib import Path

from MetadataMatchingRetriever import MetadataMatchingRetriever
import SourcedRetriever
from dotenv import load_dotenv
from langchain.retrievers import EnsembleRetriever
from langchain_community.document_transformers import EmbeddingsRedundantFilter
from langchain_chroma import Chroma
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnablePassthrough
from langchain_core.output_parsers import StrOutputParser
from langchain_openai import ChatOpenAI
from langchain_core.documents import Document
from langchain.memory import ConversationBufferMemory
from langchain_core.messages import HumanMessage, AIMessage
from langchain_core.runnables import RunnableLambda

# 环境配置
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'
os.environ['HF_HUB_DISABLE_SYMLINKS_WARNING'] = '1'
load_dotenv()
baai_path = os.getenv("BAAI_PATH", "")
chroma_path = os.getenv("CHROMA_PATH", "")
device = os.getenv("DEVICE", "")

# ----------------- 初始化嵌入模型 -----------------
embedding = HuggingFaceEmbeddings(
    model_name=baai_path,
    model_kwargs={'device': device},
    encode_kwargs={'normalize_embeddings': True}
)

def create_qa_chain(vector_db):    
    llm = ChatOpenAI(
        model=os.getenv("LLM_MODEL"),
        base_url=os.getenv("BASE_URL"),
        api_key=os.getenv("API_KEY"),
        temperature=0.1
    )

    dense_retriever = vector_db.as_retriever(
        search_type="mmr",
        search_kwargs={
            "k": 4,
            "fetch_k": 20,
            "lambda_mult": 0.5
        }
    )
    sourced_compression_retriever = SourcedRetriever(dense_retriever, "dense_compression")

    metadatas = vector_db.get()["metadatas"]
    documents_content = vector_db.get()["documents"]
    all_documents = [
        Document(page_content=content, metadata=metadata)
        for content, metadata in zip(documents_content, metadatas)
    ]
    metadata_matching_retriever = MetadataMatchingRetriever(all_documents, llm)
    sourced_metadata_matching_retriever = SourcedRetriever(metadata_matching_retriever, "metadata_matching")

    redundant_filter = EmbeddingsRedundantFilter(embeddings=embedding)
    ensemble_retriever = EnsembleRetriever(
        retrievers=[sourced_compression_retriever, sourced_metadata_matching_retriever],
        weights=[0.5, 0.5]
    )
    memory = ConversationBufferMemory(
        memory_key="chat_history",
        return_messages=True
    )

    prompt_template = """
    你是一个专业的Minecraft模组专家，请根据以下提供的上下文信息回答用户问题。
    
    以下是之前的对话历史：
    {chat_history}
    
    上下文信息:
    {context}
    
    用户问题: {question}
    
    回答要求:
    1. 如果找不到足够信息回答问题，直接说明"我没有足够信息回答这个问题"，不要编造
    2. 仅使用上下文中的事实信息，不要添加不在上下文中的细节
    3. 专注于用户问题的核心，提供简明扼要的答案
    4. 使用中文Markdown格式，保持回答结构清晰
    5. 引用源数据中的精确数值
    6. 对于参考的文档，请在回答最后附上它的源网址
    """
    prompt = ChatPromptTemplate.from_template(prompt_template)

    def retrieve_and_process(query):
        docs = ensemble_retriever.invoke(query)
        docs_with_reduced_redundancy = redundant_filter.transform_documents(docs)
        formatted_docs = []
        for doc in docs_with_reduced_redundancy:
            formatted_docs.append(f"{doc.metadata}\n{doc.page_content}")
        
        return "\n----------------------\n".join(formatted_docs)    

    def print_prompt(formatted_prompt):
        print("\nPrompt>>\n")
        if hasattr(formatted_prompt, "to_messages"):
            messages = formatted_prompt.to_messages()
            for message in messages:
                role = "Human" if isinstance(message, HumanMessage) else "AI"
                print(f"{role}: {message.content}\n")
        else:
            print(formatted_prompt)
        return formatted_prompt

    qa_chain = (
        {
            "context": lambda query: retrieve_and_process(query),
            "chat_history": lambda x: memory.load_memory_variables({})["chat_history"],
            "question": RunnablePassthrough()
        }
        | prompt
        | RunnableLambda(print_prompt)
        | llm
        | StrOutputParser()
    )
    return qa_chain, memory

if __name__ == "__main__":
    CHROMA_PATH = chroma_path
    
    if Path(CHROMA_PATH).exists():
        print("正在加载现有数据库...")
        vector_db = Chroma(
            persist_directory=CHROMA_PATH,
            embedding_function=embedding
        )
    qa_chain, memory = create_qa_chain(vector_db)
    print(f"数据库已就绪，包含 {vector_db._collection.count()} 个向量")
    while True:
        query = input("\n请输入问题（输入q退出）: \n")
        if query.lower() == 'q':
            break
        answer = qa_chain.invoke(query, verbose=True)
        print(answer)
        memory.save_context(
            {"input": query},
            {"output": answer}
        )
        print("\n\n对话历史>>")
        for message in memory.load_memory_variables({})["chat_history"]:
            if isinstance(message, HumanMessage):
                print(f"用户: {message.content}")
            elif isinstance(message, AIMessage):
                print(f"AI: {message.content}")
            else:
                print(f"未知消息类型: {message}")
