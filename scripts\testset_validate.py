import json
import os
import sys
from pathlib import Path
from tqdm import tqdm
from dotenv import load_dotenv
from langchain_chroma import Chroma
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_openai import ChatOpenAI
from langchain.retrievers import EnsembleRetriever
from langchain_community.document_transformers import EmbeddingsRedundantFilter
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnablePassthrough, RunnableLambda
from langchain_core.output_parsers import StrOutputParser
from langchain_core.documents import Document
from langchain.memory import ConversationBufferMemory
from SourcedRetriever import SourcedRetriever
from MetadataMatchingRetriever import MetadataMatchingRetriever

def load_testset(file_path):
    """加载测试集数据"""
    with open(file_path, "r", encoding="utf-8") as f:
        return json.load(f)

def save_results(results, output_file):
    """保存验证结果"""
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

def setup_rag_system():
    """设置RAG检索和回答系统"""
    load_dotenv()
    
    # 获取环境变量
    baai_path = os.getenv("BAAI_PATH", "")
    chroma_path = os.getenv("CHROMA_PATH", "")
    
    # 检查embedding模型路径
    if not os.path.exists(baai_path):
        print(f"Warning: BAAI_PATH {baai_path} not found, checking alternative locations...")
        local_baai_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "BAAI", "bge-large-zh-v1.5")
        if os.path.exists(local_baai_path):
            print(f"Found model at {local_baai_path}")
            baai_path = local_baai_path
        else:
            print("Using model name directly from HuggingFace")
            baai_path = "BAAI/bge-large-zh-v1.5"
    
    # 加载embedding模型
    try:
        embedding = HuggingFaceEmbeddings(
            model_name=baai_path,
            model_kwargs={'device': os.getenv("DEVICE", "cpu")},
            encode_kwargs={'normalize_embeddings': True}
        )
    except Exception as e:
        print(f"Error loading embedding model: {e}")
        sys.exit(1)
    
    # 检查向量数据库路径
    if not Path(chroma_path).exists():
        print(f"Vector database not found at {chroma_path}")
        sys.exit(1)
    
    # 加载向量数据库
    vector_db = Chroma(persist_directory=chroma_path, embedding_function=embedding)
    
    # 初始化LLM
    llm = ChatOpenAI(
        model=os.getenv("LLM_MODEL"),
        base_url=os.getenv("BASE_URL"),
        api_key=os.getenv("API_KEY"),
        temperature=0.1
    )
    
    # 设置稠密检索器
    dense_retriever = vector_db.as_retriever(
        search_type="mmr",
        search_kwargs={
            "k": 4,
            "fetch_k": 20,
            "lambda_mult": 0.5
        }
    )
    sourced_compression_retriever = SourcedRetriever(dense_retriever, "dense_compression")
    
    # 设置元数据匹配检索器
    metadatas = vector_db.get()["metadatas"]
    documents_content = vector_db.get()["documents"]
    all_documents = [
        Document(page_content=content, metadata=metadata)
        for content, metadata in zip(documents_content, metadatas)
    ]
    metadata_matching_retriever = MetadataMatchingRetriever(all_documents, llm)
    sourced_metadata_matching_retriever = SourcedRetriever(metadata_matching_retriever, "metadata_matching")
    
    # 设置冗余过滤器和集成检索器
    redundant_filter = EmbeddingsRedundantFilter(embeddings=embedding)
    ensemble_retriever = EnsembleRetriever(
        retrievers=[sourced_compression_retriever, sourced_metadata_matching_retriever],
        weights=[0.5, 0.5]
    )
    
    # 定义提示模板
    prompt_template = """
    你是一个专业的Minecraft模组专家，请根据以下提供的上下文信息回答用户问题。
    
    上下文信息:
    {context}
    
    用户问题: {question}
    
    回答要求:
    1. 如果找不到足够信息回答问题，直接说明"我没有足够信息回答这个问题"，不要编造
    2. 仅使用上下文中的事实信息，不要添加不在上下文中的细节
    3. 专注于用户问题的核心，提供简明扼要的答案
    4. 使用中文Markdown格式，保持回答结构清晰
    5. 引用源数据中的精确数值
    6. 对于参考的文档，请在回答最后附上它的源网址
    """
    prompt = ChatPromptTemplate.from_template(prompt_template)
    
    # 定义处理查询的函数
    def process_query(query):
        docs = ensemble_retriever.invoke(query)
        filtered_docs = redundant_filter.transform_documents(docs)
        return "\n\n".join([f"{d.metadata}\n{d.page_content}" for d in filtered_docs])
    
    # 构建回答链
    chain = (
        {"context": RunnableLambda(process_query),
         "question": RunnablePassthrough()}
        | prompt
        | llm
        | StrOutputParser()
    )
    
    return chain

def validate_testset():
    """对测试集进行验证并保存结果"""
    print("正在加载测试集...")
    testset = load_testset("mod_qa_testset.json")
    print(f"已加载 {len(testset)} 个测试用例")
    
    print("设置RAG系统...")
    chain = setup_rag_system()
    
    print("开始验证测试集...")
    results = []
    
    for item in tqdm(testset, desc="处理测试问题"):
        query = item["user_input"]
        try:
            answer = chain.invoke(query)
            
            # 保存结果
            result = {
                "user_input": query,
                "reference": item.get("reference", ""),
                "reference_contexts": item.get("reference_contexts", []),
                "synthesizer_name": item.get("synthesizer_name", ""),
                "answer": answer
            }
            results.append(result)
        except Exception as e:
            print(f"处理问题时出错: {query}")
            print(f"错误: {e}")
            # 记录错误信息
            result = {
                "user_input": query,
                "reference": item.get("reference", ""),
                "reference_contexts": item.get("reference_contexts", []),
                "synthesizer_name": item.get("synthesizer_name", ""),
                "answer": f"错误: {str(e)}"
            }
            results.append(result)
    
    print("保存验证结果...")
    save_results(results, "mod_qa_validate.json")
    print(f"结果已保存到 mod_qa_validate.json，共处理 {len(results)} 个问题")
    return results

if __name__ == "__main__":
    validate_testset()