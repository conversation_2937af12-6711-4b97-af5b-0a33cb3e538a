"""
数据库迁移管理脚本
Database Migration Management Script

此脚本用于管理数据库迁移，包括执行迁移和回滚操作。
"""

import sys
import os
import importlib.util
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

def get_migration_files():
    """获取所有迁移文件"""
    migrations_dir = Path(__file__).parent / "migrations"
    if not migrations_dir.exists():
        print("migrations目录不存在")
        return []
    
    migration_files = []
    for file in migrations_dir.glob("*.py"):
        if file.name != "__init__.py":
            migration_files.append(file)
    
    # 按文件名排序
    migration_files.sort(key=lambda x: x.name)
    return migration_files

def load_migration_module(migration_file):
    """加载迁移模块"""
    spec = importlib.util.spec_from_file_location("migration", migration_file)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module

def run_migration(migration_file):
    """执行单个迁移"""
    print(f"\n执行迁移: {migration_file.name}")
    print("=" * 50)
    
    try:
        module = load_migration_module(migration_file)
        if hasattr(module, 'run_migration'):
            return module.run_migration()
        else:
            print(f"迁移文件 {migration_file.name} 缺少 run_migration 函数")
            return False
    except Exception as e:
        print(f"执行迁移 {migration_file.name} 时发生错误: {e}")
        return False

def rollback_migration(migration_file):
    """回滚单个迁移"""
    print(f"\n回滚迁移: {migration_file.name}")
    print("=" * 50)
    
    try:
        module = load_migration_module(migration_file)
        if hasattr(module, 'rollback_migration'):
            return module.rollback_migration()
        else:
            print(f"迁移文件 {migration_file.name} 缺少 rollback_migration 函数")
            return False
    except Exception as e:
        print(f"回滚迁移 {migration_file.name} 时发生错误: {e}")
        return False

def run_all_migrations():
    """执行所有迁移"""
    print("开始执行所有数据库迁移...")
    
    migration_files = get_migration_files()
    if not migration_files:
        print("没有找到迁移文件")
        return True
    
    success_count = 0
    for migration_file in migration_files:
        if run_migration(migration_file):
            success_count += 1
        else:
            print(f"迁移 {migration_file.name} 失败，停止执行后续迁移")
            break
    
    print(f"\n迁移完成: {success_count}/{len(migration_files)} 个迁移成功执行")
    return success_count == len(migration_files)

def rollback_all_migrations():
    """回滚所有迁移"""
    print("开始回滚所有数据库迁移...")
    
    migration_files = get_migration_files()
    if not migration_files:
        print("没有找到迁移文件")
        return True
    
    # 按逆序回滚
    migration_files.reverse()
    
    success_count = 0
    for migration_file in migration_files:
        if rollback_migration(migration_file):
            success_count += 1
        else:
            print(f"回滚 {migration_file.name} 失败，停止执行后续回滚")
            break
    
    print(f"\n回滚完成: {success_count}/{len(migration_files)} 个迁移成功回滚")
    return success_count == len(migration_files)

def run_specific_migration(migration_name):
    """执行特定迁移"""
    migration_files = get_migration_files()
    
    for migration_file in migration_files:
        if migration_name in migration_file.name:
            return run_migration(migration_file)
    
    print(f"未找到包含 '{migration_name}' 的迁移文件")
    return False

def rollback_specific_migration(migration_name):
    """回滚特定迁移"""
    migration_files = get_migration_files()
    
    for migration_file in migration_files:
        if migration_name in migration_file.name:
            return rollback_migration(migration_file)
    
    print(f"未找到包含 '{migration_name}' 的迁移文件")
    return False

def list_migrations():
    """列出所有迁移文件"""
    migration_files = get_migration_files()
    
    if not migration_files:
        print("没有找到迁移文件")
        return
    
    print("可用的迁移文件:")
    for i, migration_file in enumerate(migration_files, 1):
        print(f"{i}. {migration_file.name}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="数据库迁移管理脚本")
    parser.add_argument("--all", action="store_true", help="执行所有迁移")
    parser.add_argument("--rollback-all", action="store_true", help="回滚所有迁移")
    parser.add_argument("--migration", type=str, help="执行特定迁移（部分文件名匹配）")
    parser.add_argument("--rollback", type=str, help="回滚特定迁移（部分文件名匹配）")
    parser.add_argument("--list", action="store_true", help="列出所有迁移文件")
    
    args = parser.parse_args()
    
    if args.list:
        list_migrations()
    elif args.all:
        success = run_all_migrations()
        sys.exit(0 if success else 1)
    elif args.rollback_all:
        success = rollback_all_migrations()
        sys.exit(0 if success else 1)
    elif args.migration:
        success = run_specific_migration(args.migration)
        sys.exit(0 if success else 1)
    elif args.rollback:
        success = rollback_specific_migration(args.rollback)
        sys.exit(0 if success else 1)
    else:
        parser.print_help()
        print("\n示例用法:")
        print("  python migrate.py --list                    # 列出所有迁移")
        print("  python migrate.py --all                     # 执行所有迁移")
        print("  python migrate.py --migration conversation  # 执行包含'conversation'的迁移")
        print("  python migrate.py --rollback conversation   # 回滚包含'conversation'的迁移")
        print("  python migrate.py --rollback-all            # 回滚所有迁移")
