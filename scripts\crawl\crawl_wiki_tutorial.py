import json
import os
import re
import time
import random
import logging
import traceback
from bs4 import BeautifulSoup
from urllib.parse import urljoin, unquote
from pathlib import Path

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options as ChromeOptions
from webdriver_manager.chrome import ChromeDriverManager
from selenium.common.exceptions import TimeoutException, WebDriverException

# --- Configuration ---
WIKI_OUTPUT_FOLDER = './wiki'
LOG_FILE = 'crawl_wiki_selenium_log.txt'
REQUEST_TIMEOUT = 30  # Selenium page load timeout
MAX_RETRIES = 3

# --- Target Site Configuration ---
WIKI_BASE_URL = "https://zh.minecraft.wiki"
WIKI_TUTORIAL_PAGE = "https://zh.minecraft.wiki/w/教程"

# Default cookies (will be added to Selenium driver)
DEFAULT_COOKIES = [
    {"name": "theme", "value": "light"},
    {"name": "VEE", "value": "wikitext"}
]

# --- Global Variables ---
tutorial_urls = []

# --- Logging Setup ---
logging.basicConfig(filename=LOG_FILE, level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S')

def my_log(msg):
    """Prints message to console and logs it to file."""
    print(msg)
    logging.info(msg)

# --- Utility Functions ---
def clean_text(text):
    """Cleans text by removing excess whitespace and non-breaking spaces."""
    if text:
        text = text.replace('\xa0', ' ') # Replace non-breaking space
        text = re.sub(r'\s+', ' ', text) # Replace multiple whitespace with single space
        return text.strip()
    return ""

def extract_wiki_id(wiki_url):
    """Extract the wiki ID from the URL and sanitize it for use as a filename."""
    match = re.search(r'/w/(.+)$', wiki_url)
    if match:
        path_segment = match.group(1)
        path_segment = unquote(path_segment)
        # Sanitize for filename: replace common problematic characters
        path_segment = path_segment.replace('/', '_')
        path_segment = path_segment.replace(' ', '_')
        path_segment = path_segment.replace(':', '_') # Crucial for Windows
        path_segment = path_segment.replace('?', '_')
        path_segment = path_segment.replace('*', '_')
        path_segment = path_segment.replace('"', '_')
        path_segment = path_segment.replace('<', '_')
        path_segment = path_segment.replace('>', '_')
        path_segment = path_segment.replace('|', '_')
        
        # Limit filename length (optional, but good practice for extreme cases)
        # Windows max path is ~260, but individual components also have limits.
        # Max filename length is often 255 characters.
        max_filename_len = 150 # Being conservative for the segment
        if len(path_segment) > max_filename_len:
            my_log(f"Warning: Extracted wiki_id segment '{path_segment}' is too long ({len(path_segment)} chars). Truncating.")
            # Simple truncation, could be smarter (e.g., keeping extension if any)
            path_segment = path_segment[:max_filename_len]
            
        return path_segment
    return None

def clean_html_content(html_content):
    """Clean the HTML content and extract text."""
    soup = BeautifulSoup(html_content, 'html.parser')

    # More comprehensive list of elements to remove for cleaner text
    selectors_to_remove = [
        'script', 'style', '.mw-editsection', '.mw-references-wrap', 
        '.reference', '#p-lang-btn', '.vector-menu-content', '#siteNotice', 
        '#mw-navigation', '.mw-footer', '#footer', '.noprint', '.toc', 
        '#catlinks', '.printfooter', 'div.thumb', 'figure', '.gallery', 
        'table.navbox', '.vertical-navbox', '.infobox', '.metadata', '.ambox'
    ]
    for selector in selectors_to_remove:
        for unwanted in soup.select(selector):
            unwanted.decompose()
    
    content = clean_text(soup.get_text(separator=' ', strip=True))
    return content

# --- Wiki Crawling Functions ---
def fetch_tutorial_links(driver):
    """Fetches all tutorial links from the main tutorial page using Selenium."""
    global tutorial_urls
    try:
        my_log(f"正在请求教程页面: {WIKI_TUTORIAL_PAGE}")
        driver.get(WIKI_TUTORIAL_PAGE)
        # Increased sleep time as initial tutorial page might have more JS or checks
        time.sleep(random.uniform(5.0, 8.0)) 

        if "Weird Gloop" in driver.title or "Redirecting" in driver.title or "访问被拒绝" in driver.page_source:
            my_log(f"[Blocked] Access to tutorial page {WIKI_TUTORIAL_PAGE} seems blocked. Title: {driver.title}")
            return False

        page_source = driver.page_source
        # Saving the page source can be helpful for debugging selectors
        # with open('tutorial_page_selenium_debug.html', 'w', encoding='utf-8') as f:
        # f.write(page_source)
        # my_log(f"已保存教程页面 HTML (Selenium) 到 tutorial_page_selenium_debug.html for inspection.")

        soup = BeautifulSoup(page_source, 'html.parser')
        
        urls = []
        # Try to find navboxes first, as they are usually more curated
        navboxes = soup.select('div.navbox') # General selector for any navbox
        if navboxes:
            my_log(f"Found {len(navboxes)} navbox(es). Attempting to extract links from them.")
            for navbox in navboxes:
                # Prioritize links within list items, then any link within the navbox
                links_in_navbox = navbox.select('li a[href^="/w/"]') 
                if not links_in_navbox:
                    links_in_navbox = navbox.select('a[href^="/w/"]')
                
                for link_element in links_in_navbox:
                    href = link_element.get('href')
                    # Filter more strictly for navbox links
                    if href and ('/Tutorial:' in href or '/教程:' in href) and \
                       not href.startswith('/w/Special:') and \
                       'action=edit' not in href and \
                       'action=history' not in href and \
                       'redlink=1' not in href:
                        full_url = urljoin(WIKI_BASE_URL, href)
                        if full_url not in urls:
                            urls.append(full_url)
        
        if not urls: # Fallback to a broader search if navboxes yield no suitable links or are not found
            my_log(f"Navboxes did not yield suitable tutorial links or no navboxes found. Trying broader search for tutorial links on the page...")
            
            # Candidate elements: looking for links that are likely content pages
            candidate_link_elements = soup.select('a[href^="/w/"]') # Select all links starting with /w/

            for link_element in candidate_link_elements:
                href = link_element.get('href', '')
                text = clean_text(link_element.get_text())

                is_potential_tutorial = False
                # Condition 1: URL explicitly indicates a tutorial
                if '/Tutorial:' in href or '/教程:' in href:
                    is_potential_tutorial = True
                
                # Condition 2: Link text suggests a tutorial, and URL is a general article
                elif '教程' in text and not any(indicator in text.lower() for indicator in ["列表", "模板", "分类", "编辑", "历史"]):
                    is_potential_tutorial = True

                if is_potential_tutorial:
                    # Apply general exclusions
                    if not href.startswith('/w/Special:') and \
                       'action=edit' not in href and \
                       'action=history' not in href and \
                       'redlink=1' not in href:
                        
                        full_url = urljoin(WIKI_BASE_URL, href)
                        if full_url not in urls:
                            urls.append(full_url)
        
        if urls:
            my_log(f"成功获取到 {len(urls)} 个候选教程链接")
            tutorial_urls = list(set(urls)) # Ensure unique URLs
            my_log(f"Unique tutorial links found: {len(tutorial_urls)}")
            # for u in tutorial_urls[:20]: my_log(f"  Sample URL: {u}") # Log some samples
            return True
        else:
            my_log(f"[错误] 无法找到任何教程链接 from {WIKI_TUTORIAL_PAGE} after all attempts.")
            return False

    except TimeoutException:
        my_log(f"获取教程链接时超时: {WIKI_TUTORIAL_PAGE}")
        return False
    except WebDriverException as e:
        my_log(f"获取教程链接时发生 WebDriverException: {e}")
        traceback.print_exc()
        return False
    except Exception as e:
        my_log(f"获取教程链接时发生未知错误: {e}")
        traceback.print_exc()
        return False

def process_wiki_page(driver, url):
    """
    Fetches, parses, and saves a single wiki page using Selenium.
    Returns:
        True: Success
        "not_found": Page indicates not found
        "blocked": Access was blocked
        "timeout": Request timed out
        "error": Other request or processing error
    """
    wiki_id = extract_wiki_id(url)
    if not wiki_id:
        my_log(f"[错误] 无法从 URL 提取 wiki ID: {url}")
        return "error"
    
    # Construct filename carefully
    safe_filename = f"wiki_{wiki_id}.json"
    output_path = Path(WIKI_OUTPUT_FOLDER) / safe_filename
    if output_path.exists() and output_path.stat().st_size > 0:
        my_log(f"跳过已存在的文件: {output_path}")
        return "skipped"

    my_log(f"Fetching Wiki {wiki_id} (filename: {safe_filename}): {url}")
    
    try:
        driver.get(url)
        time.sleep(random.uniform(3.0, 5.0)) # Shorter sleep for individual pages

        page_title = driver.title
        page_source = driver.page_source

        if "Weird Gloop" in page_title or "Redirecting" in page_title or "访问被拒绝" in page_source or \
           ("error" in page_title.lower() and "not found" in page_source.lower()) or \
           "Bad Gateway" in page_title or "Service Unavailable" in page_title:
            my_log(f"[Blocked/Error Indicator] Wiki {wiki_id}: {url} - Title: {page_title}")
            if "不存在" in page_title or "Not Found" in page_title or "no article" in page_source.lower() or "页面不存在" in page_source:
                 my_log(f"[404 Not Found Suspected by Title/Source] Wiki {wiki_id}: {url}")
                 return "not_found"
            return "blocked" 

        soup = BeautifulSoup(page_source, 'html.parser')
        if not soup:
            my_log(f"[错误] 无法解析 HTML for Wiki {wiki_id}: {url}")
            return "error"

        title_elem = soup.find('h1', {'id': 'firstHeading', 'class': 'firstHeading'}) # More specific
        if not title_elem: # Fallback if class is not there
             title_elem = soup.find('h1', {'id': 'firstHeading'})
        title_text = clean_text(title_elem.get_text(separator=' ', strip=True)) if title_elem else f"标题未找到 (Wiki {wiki_id})"
        
        if "页面不存在" in title_text or "不存在的页面" in title_text or \
           (title_elem and title_elem.find('span', class_='mw-page-title-namespace')) and "Special" in title_elem.find('span', class_='mw-page-title-namespace').get_text() or \
           (title_elem and "new" in title_elem.get('class', [])):
            my_log(f"[Not Found/Special Page by Title] Wiki {wiki_id}: {url} - Title: {title_text}")
            return "not_found" # Treat special pages found here as "not a tutorial"

        content_div = soup.find('div', {'class': 'mw-body-content'})

        if content_div:
            content_tags_to_extract = ['p', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6']
            
            # Find all specified elements in document order
            elements_in_order = content_div.find_all(content_tags_to_extract)
            
            formatted_text_parts = []
            for element in elements_in_order:
                # Get the raw text and clean it
                text = element.get_text(strip=True)
                
                # Apply your existing regex cleaning for [编辑|编辑源代码] to each element's text
                text = re.sub(r'\[\s*编辑\s*\|\s*编辑源代码\s*\]', '', text)
                
                if text:  # Process only if there's content after stripping and cleaning
                    tag_name = element.name
                    formatted_line = ""
                    
                    if tag_name == 'h1':
                        formatted_line = f"# {text}"  # Markdown for H1
                    elif tag_name == 'h2':
                        formatted_line = f"## {text}" # Markdown for H2
                    elif tag_name == 'h3':
                        formatted_line = f"### {text}" # Markdown for H3
                    elif tag_name == 'h4':
                        formatted_line = f"#### {text}" # Markdown for H4
                    elif tag_name == 'h5':
                        formatted_line = f"##### {text}" # Markdown for H5
                    elif tag_name == 'h6':
                        formatted_line = f"###### {text}" # Markdown for H6
                    elif tag_name == 'li':
                        # For list items, you could check the parent (ul or ol)
                        # to decide between '*'/' -' or numbered prefixes.
                        # For simplicity, using a generic bullet point:
                        formatted_line = f"- {text}" 
                    elif tag_name == 'p':
                        # Paragraphs might not need a prefix, or you could add one if desired
                        # e.g., formatted_line = f"Paragraph: {text}"
                        formatted_line = text 
                    else:
                        # Fallback for any other tags in your list (though current list is specific)
                        formatted_line = text
                        
                    formatted_text_parts.append(formatted_line)
                    
            # Join the formatted parts with newlines
            text_content = "\n".join(formatted_text_parts)
            
            # Apply the final truncation if the "导航" string is present
            # This should be done on the fully assembled and formatted text_content
            navigation_marker = "导航[编辑|编辑源代码]" # Original marker before regex removal
            # Alternative marker if the regex already removed parts of it
            # If "导航" is a heading, it might now be like "# 导航" or similar.
            # You might need to adjust this split logic depending on how "导航[编辑|编辑源代码]"
            # appears in a heading after formatting.
            # For now, assuming the split should happen on the raw text if it was a heading
            # or if it's a specific string pattern you expect.

            # A safer way to handle the "导航" split if it's often within a heading
            # and the [编辑|编辑源代码] part is already removed by the regex above:
            # Let's assume "导航" itself is a strong keyword.
            if "导航" in text_content: # This check might need to be more specific
                # Find the first occurrence of a line starting with a heading marker followed by "导航"
                lines = text_content.splitlines()
                nav_index = -1
                for i, line in enumerate(lines):
                    # Check for markdown headings followed by "导航"
                    if (line.startswith("# ") or \
                        line.startswith("## ") or \
                        line.startswith("### ") or \
                        line.startswith("#### ") or \
                        line.startswith("##### ") or \
                        line.startswith("###### ")) and "导航" in line:
                        nav_index = i
                        break
                    # Or check for the original marker if it somehow persisted or was part of a <p>
                    elif "导航[编辑|编辑源代码]" in line: # Less likely if regex above works on all elements
                        nav_index = i
                        break


                if nav_index != -1:
                    text_content = "\n".join(lines[:nav_index])
                    print(f"Content truncated at navigation section (index {nav_index}).")


            # Your existing data structure
            # Note: title_text.split(':')[1] might be problematic if title_text doesn't contain ':'
            # or if the structure isn't always "SomePrefix:ActualTitle". Consider error handling.
            actual_title = title_text
            if ':' in title_text:
                parts = title_text.split(':', 1) # Split only on the first colon
                if len(parts) > 1 and parts[1].strip(): # Ensure there's text after the colon
                    actual_title = parts[1].strip()
                else:
                    actual_title = parts[0].strip() # Use the part before if after is empty or no split
            
            data = {
                "url": url,
                "title": actual_title,
                "content": text_content.strip() # Add a final strip to the whole content
            }

        output_path.parent.mkdir(parents=True, exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)

        my_log(f"Wiki {wiki_id} saved successfully to {output_path}")
        return True

    except TimeoutException:
        my_log(f"[Timeout] Wiki {wiki_id}: {url} (Page load timed out after {REQUEST_TIMEOUT}s)")
        return "timeout"
    except WebDriverException as e:
        # Sanitize exception message for logging if it contains problematic chars, though less common
        err_msg = str(e).encode('utf-8', 'replace').decode('utf-8')
        my_log(f"[WebDriver Error] Wiki {wiki_id} ({url}): {err_msg[:500]}") # Log first 500 chars of error
        if "net::ERR_NAME_NOT_RESOLVED" in str(e) or "Reached error page" in str(e):
            my_log(f"Suspected Not Found or Network issue for {wiki_id}: {e}")
            return "not_found" 
        return "error"
    except OSError as e: # Specifically catch OSError for file operations
        my_log(f"[OSError during file operation] Wiki {wiki_id} ({url}), Path {output_path}: {e}")
        traceback.print_exc()
        return "error" # Critical error, stop processing this item
    except Exception as e:
        my_log(f"[Processing Error] Wiki {wiki_id} ({url}): {e}")
        traceback.print_exc()
        return "error"

# --- Main Execution ---
if __name__ == "__main__":
    start_time_main = time.perf_counter()
    my_log("脚本开始执行 - Wiki 教程爬虫 (Selenium版)...")

    os.makedirs(WIKI_OUTPUT_FOLDER, exist_ok=True)
    my_log(f"输出目录: {WIKI_OUTPUT_FOLDER}")
    my_log("-" * 30)
    my_log("重要提示:")
    my_log(f"  - 目标主页: {WIKI_TUTORIAL_PAGE}")
    my_log("  - 将爬取所有教程页面的 title, URL 和 content")
    my_log(f"  - 使用 Selenium WebDriver (Chrome) 进行爬取")
    my_log("-" * 30)

    chrome_options = ChromeOptions()
    # Minimal useful options for headless robust operation
    chrome_options.add_argument('--headless=new') # Preferred new headless mode
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36')
    chrome_options.add_argument("--lang=zh-CN,zh;q=0.9,en;q=0.8") # Added English as fallback lang
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    # Preferences to disable images and other resources for speed, if not needed for content
    # chrome_options.add_experimental_option("prefs", {
    #     "profile.managed_default_content_settings.images": 2, # Disable images
    #     # "profile.managed_default_content_settings.stylesheets": 2, # Disable CSS (can break layout)
    #     # "profile.managed_default_content_settings.javascript": 2, # Disable JS (can break sites)
    #     "profile.managed_default_content_settings.cookies": 1, # Allow cookies
    # })


    driver = None
    try:
        my_log("正在初始化 Selenium WebDriver...")
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.set_page_load_timeout(REQUEST_TIMEOUT)
        # Execute script to prevent detection (optional, use with caution)
        # driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
        #     "source": """
        #         Object.defineProperty(navigator, 'webdriver', {
        #           get: () => undefined
        #         });
        #     """
        # })
        
        my_log(f"Navigating to {WIKI_BASE_URL} to set initial cookies.")
        driver.get(WIKI_BASE_URL) # Load base URL first
        time.sleep(random.uniform(1.5, 2.5)) 
        for cookie_dict in DEFAULT_COOKIES:
            try:
                driver.add_cookie(cookie_dict)
            except Exception as cookie_e:
                my_log(f"Warning: Could not add cookie {cookie_dict.get('name', '')}: {cookie_e}")
        my_log(f"Default cookies processed for domain {WIKI_BASE_URL}")
        time.sleep(random.uniform(0.5, 1.0))


        my_log("获取所有教程链接...")
        if not fetch_tutorial_links(driver):
            my_log("获取教程链接失败。脚本终止。")
            if driver: driver.quit()
            exit(1)
    
        if not tutorial_urls:
            my_log("未找到任何教程链接。脚本终止。")
            if driver: driver.quit()
            exit(1)
            
        my_log(f"共获取到 {len(tutorial_urls)} 个教程链接，准备开始爬取...")
    
        processed_count = 0
        success_count = 0
        fail_count = 0 # General failures not covered by specific counters
        skipped_count = 0
        not_found_count = 0
        blocked_count = 0
        timeout_count = 0 # Specifically for page load timeouts

        BATCH_SIZE = 3 
        
        unique_urls_to_process = list(set(tutorial_urls)) 
        random.shuffle(unique_urls_to_process) # Shuffle to vary access patterns slightly
        total_urls_to_process = len(unique_urls_to_process)
        my_log(f"Processing {total_urls_to_process} unique tutorial URLs.")


        for idx, url in enumerate(unique_urls_to_process):
            current_url_num = idx + 1
            processed_count += 1 # Incremented as we start attempting this URL
            
            wiki_id = extract_wiki_id(url)
            if not wiki_id:
                my_log(f"跳过无效URL {current_url_num}/{total_urls_to_process} (无法提取 wiki_id): {url}")
                fail_count +=1 
                continue
            
            safe_filename = f"wiki_{wiki_id}.json"
            output_path = Path(WIKI_OUTPUT_FOLDER) / safe_filename


            if output_path.exists() and output_path.stat().st_size > 0:
                skipped_count += 1
                my_log(f"跳过已存在 ({current_url_num}/{total_urls_to_process}) Wiki {wiki_id}")
                continue

            my_log(f"--- Processing URL {current_url_num}/{total_urls_to_process}: {url} ---")

            # Random delay before processing each new URL in a batch (not before the first one)
            if current_url_num > 1 and (current_url_num -1) % BATCH_SIZE != 0 :
                 time.sleep(random.uniform(1.0, 3.0)) # Shorter inter-URL sleep within a batch


            page_processed_successfully_this_run = False # Tracks if current URL was processed successfully in this run
            last_result_status = "unknown"

            for attempt in range(MAX_RETRIES):
                if attempt > 0:
                    retry_sleep = random.uniform(3.0, 6.0) * attempt # Incremental backoff
                    my_log(f"重试 {attempt+1}/{MAX_RETRIES} for {wiki_id}，等待 {retry_sleep:.2f} 秒...")
                    time.sleep(retry_sleep)

                result = process_wiki_page(driver, url)
                last_result_status = result # Store the last outcome

                if result is True:
                    success_count += 1
                    page_processed_successfully_this_run = True
                    break 
                elif result == "not_found":
                    not_found_count += 1
                    page_processed_successfully_this_run = True # Considered "processed" for this URL
                    try:
                        output_path.parent.mkdir(parents=True, exist_ok=True)
                        with open(output_path, 'w', encoding='utf-8') as f:
                            json.dump({"id": wiki_id, "url": url, "status": "not_found", "message": f"Page indicated as not found or is a non-content page."}, f, ensure_ascii=False, indent=2)
                        my_log(f"记录 'not_found' 状态 for {wiki_id} at {output_path}")
                    except Exception as e_nf:
                        my_log(f"无法创建 'not_found' 记录文件 for {wiki_id}: {e_nf}")
                    break # Stop retrying for "not_found"
                elif result == "blocked":
                    my_log(f"Wiki {wiki_id} 尝试 {attempt + 1}/{MAX_RETRIES} - 状态: ({result}).")
                    if attempt == MAX_RETRIES - 1: # If last attempt results in blocked
                        blocked_count +=1
                    # No immediate retry for "blocked" in this loop, will count as failure for this URL if all retries exhausted
                    break # Often better to stop for "blocked" and move on, or have a longer delay strategy
                elif result == "timeout":
                    my_log(f"Wiki {wiki_id} 尝试 {attempt + 1}/{MAX_RETRIES} - 状态: ({result}).")
                    if attempt == MAX_RETRIES - 1: # If last attempt results in timeout
                        timeout_count +=1
                elif result == "error":
                    my_log(f"Wiki {wiki_id} 尝试 {attempt + 1}/{MAX_RETRIES} - 状态: ({result}).")
            
            if not page_processed_successfully_this_run:
                # This block is entered if the loop finished without result being True or "not_found"
                # Specific counters (blocked_count, timeout_count) are incremented above on the *last* attempt if that was the status.
                # fail_count will capture other errors or if it exited early from 'blocked'
                if last_result_status not in [True, "not_found", "blocked", "timeout"]: # only count as general fail if not already categorized
                    fail_count += 1
                
                my_log(f"[最终处理失败] Wiki {wiki_id} 状态: {last_result_status} (尝试 {MAX_RETRIES} 次). URL: {url}")
                try:
                    output_path.parent.mkdir(parents=True, exist_ok=True)
                    # Ensure safe_filename is used if output_path was based on a problematic wiki_id initially
                    error_file_path = Path(WIKI_OUTPUT_FOLDER) / safe_filename 
                    with open(error_file_path, 'w', encoding='utf-8') as f:
                        json.dump({"id": wiki_id, "url": url, "status": "failed", "reason": last_result_status, "message": f"Failed after {MAX_RETRIES} retries, last status: {last_result_status}"}, f, ensure_ascii=False, indent=2)
                    my_log(f"记录失败状态 for {wiki_id} at {error_file_path}")
                except Exception as e_fail:
                    my_log(f"无法创建失败记录文件 for {wiki_id}: {e_fail}")

            # Batch delay logic
            if current_url_num % BATCH_SIZE == 0 and current_url_num < total_urls_to_process:
                batch_sleep_time = random.uniform(5.0, 12.0) # Longer sleep between batches
                my_log(f"批次 ({BATCH_SIZE} URLs) 完成，休息 {batch_sleep_time:.2f} 秒...")
                time.sleep(batch_sleep_time)


            if processed_count % 5 == 0 or not page_processed_successfully_this_run:
                # Calculate general_fail_count for logging display
                current_general_fails = fail_count 
                # The other_failures calculation in the final summary will be more accurate.
                # Here, fail_count is a running tally of items that didn't succeed and weren't "not_found".
                my_log(f"进度 - 已处理: {processed_count}/{total_urls_to_process}, 成功: {success_count}, 跳过: {skipped_count}, 未找到: {not_found_count}, 被阻止: {blocked_count}, 超时: {timeout_count}, 其他失败: {current_general_fails}")


    except KeyboardInterrupt:
        my_log("脚本被用户中断 (KeyboardInterrupt)。")
    except Exception as e_main:
        my_log(f"主执行流程中发生严重错误: {e_main}")
        traceback.print_exc()
    finally:
        if driver:
            my_log("正在关闭 Selenium WebDriver...")
            driver.quit()
            my_log("WebDriver 已关闭。")

    elapsed_main = time.perf_counter() - start_time_main
    my_log("-" * 30)
    my_log(f"爬虫执行完毕.")
    my_log(f"总耗时: {elapsed_main:.2f} 秒 ({elapsed_main/60:.2f} 分钟)")
    my_log(f"总教程URL数 (尝试处理的目标): {len(unique_urls_to_process)} (Original links found: {len(tutorial_urls)})")

    my_log(f"最终统计:")
    my_log(f"  成功抓取并保存: {success_count}")
    my_log(f"  已存在跳过: {skipped_count}")
    my_log(f"  页面未找到/非内容页: {not_found_count}")
    my_log(f"  访问被阻止 (最终): {blocked_count}")
    my_log(f"  处理超时 (最终): {timeout_count}")
    # fail_count is incremented if a URL processing loop finishes and result was not True or 'not_found', AND it wasn't specifically a final 'blocked' or 'timeout'
    # This means fail_count should capture other 'error' states after all retries.
    my_log(f"  其他原因处理失败 (最终): {fail_count}")
    my_log(f"  总计已处理/尝试: {processed_count} (includes skipped, successful, and all forms of failed attempts for distinct URLs)")
    my_log("-" * 30)
    my_log(f"日志文件位于: {LOG_FILE}")
    my_log(f"输出文件位于: {WIKI_OUTPUT_FOLDER}")