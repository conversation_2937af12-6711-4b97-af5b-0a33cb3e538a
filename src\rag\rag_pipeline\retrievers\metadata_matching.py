"""
元数据匹配检索器

此模块提供了一个专门为Minecraft模组领域设计的检索器，
能够从用户查询中提取模组名称和物品名称，并根据元数据匹配为文档评分。
"""

from langchain_core.runnables.base import Runnable
from langchain_core.messages import HumanMessage
import json
import re
from ...prompts import METADATA_PROMPT_TEMPLATE, WIKI_METADATA_PROMPT_TEMPLATE, TUTORIAL_METADATA_PROMPT_TEMPLATE
from ...logging_config import get_logger

# 使用统一的日志配置
logger = get_logger("metadata_matching")

def process_name(name):
    if isinstance(name, str):
        name = name.strip()
        if name.startswith("[") and "]" in name:
            name = name.split("]")[1].strip()
        if "(" in name:
            name = name.split("(")[0].strip()
        if "（" in name:
            name = name.split("（")[0].strip()
        return name

class ItemMetadataRetriever(Runnable):
    """
    元数据匹配检索器，专为Minecraft模组领域设计

    能够从用户查询中提取模组名称和物品名称，并根据元数据匹配为文档评分。
    当无匹配时提供关键词回退机制。
    """
    
    def __init__(self, vector_db, llm, k=4):
        """
        初始化元数据匹配检索器

        Args:
            vector_db: 向量数据库
            llm: 用于提取实体的语言模型
            k: 返回的最大文档数量
        """
        self.vector_db = vector_db
        self.llm = llm
        self.k = k

    def _extract_entities(self, query):
        """
        从查询中提取模组名称和物品名称

        Args:
            query: 用户查询

        Returns:
            dict: 包含提取的模组名称和物品名称的字典
        """
        prompt = METADATA_PROMPT_TEMPLATE.format(query=query)
        try:
            messages = [HumanMessage(content=prompt)]
            response = self.llm.invoke(messages)
            response_text = response.content if hasattr(response, 'content') else str(response)
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group())
                return {
                    "mod_names": [name.lower() for name in result.get("mod_names", [])],
                    "item_names": [name.lower() for name in result.get("item_names", [])]
                }
            else:
                logger.warning(f"无法从响应中提取JSON: {response_text}")
        except Exception as e:
            logger.error(f"实体提取失败: {e}")
            logger.error(f"LLM响应: {response if 'response' in locals() else '无响应'}")
        return {"mod_names": [], "item_names": []}
    
    def invoke(self, query, config=None, **kwargs):
        """
        执行检索

        Args:
            query: 用户查询
            config: 配置参数
            **kwargs: 其他参数

        Returns:
            list: 相关文档列表
        """
        entities = self._extract_entities(query)
        logger.info(f"提取的实体: {entities}")
        
        if not entities["mod_names"] and not entities["item_names"]:
            logger.info("没有提取到实体，跳过元数据过滤")
            return []
        
        try:
            matched_docs = []
            base_retriever = self.vector_db.as_retriever(
                search_kwargs={
                    "k": self.k * 10,
                    "filter": {"类型": "物品"}
                }
            )

            try:
                query_text = " ".join(entities["mod_names"])
                if query_text.strip():
                    similarity_results = base_retriever.invoke(query_text)
                    scored_docs = [
                        (doc, self._score_document(doc, entities)) for doc in similarity_results
                    ]
                    scored_docs = [doc for doc in scored_docs if doc[1] > 0]
                    scored_docs.sort(key=lambda x: x[1], reverse=True)
                    matched_docs.extend([doc[0] for doc in scored_docs])
                query_text = " ".join(entities["item_names"])
                if query_text.strip():
                    similarity_results = base_retriever.invoke(query_text)
                    scored_docs = [
                        (doc, self._score_document(doc, entities)) for doc in similarity_results
                    ]
                    scored_docs = [doc for doc in scored_docs if doc[1] > 0]
                    scored_docs.sort(key=lambda x: x[1], reverse=True)
                    matched_docs.extend([doc[0] for doc in scored_docs])
                logger.info(f"item向量相似性搜索找到 {len(matched_docs)} 个文档，最高分是 {scored_docs[0][1] if scored_docs else 0}")
            except Exception as e:
                logger.warning(f"item向量相似性搜索失败: {e}")


            with open("item_metadata_matched_docs.txt", "w", encoding="utf-8") as f:
                for doc in matched_docs:
                    f.write(f"文档内容: {doc.page_content}\n")
                    f.write(f"元数据: {json.dumps(doc.metadata, ensure_ascii=False)}\n")
                    f.write("-" * 50 + "\n")
            return matched_docs[:self.k]

        except Exception as e:
            logger.warning(f"元数据过滤出错: {e}")

        logger.info("元数据过滤没有找到匹配的文档")
        return []

    def _score_document(self, doc, entities):
        """
        为文档评分

        Args:
            doc: 文档对象
            entities: 提取的实体

        Returns:
            int: 文档得分
        """
        score = 0
        if "模组名称" in doc.metadata and entities["mod_names"]:
            doc_mod_name = process_name(doc.metadata["模组名称"]).lower()
            for mod_name in entities["mod_names"]:
                if doc_mod_name == mod_name:
                    score += 4
                    break
                if doc_mod_name in mod_name or mod_name in doc_mod_name:
                    score += 2
                    break

        if "物品名称" in doc.metadata and entities["item_names"]:
            doc_item_name = process_name(doc.metadata["物品名称"]).lower()
            for item_name in entities["item_names"]:
                if doc_item_name == item_name:
                    score += 4
                    break
                if doc_item_name in item_name or item_name in doc_item_name:
                    score += 2
                    break
        return score

    def with_config(self, **kwargs):
        """配置检索器参数"""
        if "k" in kwargs:
            self.k = kwargs["k"]
        return self
    

class ModeMetadataRetriever(Runnable):
    """
    元数据匹配检索器，专为Minecraft模组领域设计

    能够从用户查询中提取模组名称，并根据元数据匹配为文档评分。
    当无匹配时提供关键词回退机制。
    """
    
    def __init__(self, vector_db, llm, k=4):
        """
        初始化元数据匹配检索器

        Args:
            vector_db: 向量数据库
            llm: 用于提取实体的语言模型
            k: 返回的最大文档数量
        """
        self.vector_db = vector_db
        self.llm = llm
        self.k = k

    def _extract_entities(self, query):
        """
        从查询中提取模组名称和物品名称

        Args:
            query: 用户查询

        Returns:
            dict: 包含提取的模组名称的字典
        """
        prompt = METADATA_PROMPT_TEMPLATE.format(query=query)
        try:
            messages = [HumanMessage(content=prompt)]
            response = self.llm.invoke(messages)
            response_text = response.content if hasattr(response, 'content') else str(response)
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group())
                return { "mod_names": [name.lower() for name in result.get("mod_names", [])] }
            else:
                logger.warning(f"无法从响应中提取JSON: {response_text}")
        except Exception as e:
            logger.error(f"实体提取失败: {e}")
            logger.error(f"LLM响应: {response if 'response' in locals() else '无响应'}")
        return {"mod_names": []}
    
    def invoke(self, query, config=None, **kwargs):
        """
        执行检索

        Args:
            query: 用户查询
            config: 配置参数
            **kwargs: 其他参数

        Returns:
            list: 相关文档列表
        """
        entities = self._extract_entities(query)
        logger.info(f"提取的实体: {entities}")
        
        if not entities["mod_names"]:
            logger.info("没有提取到实体，跳过元数据过滤")
            return []
        
        try:
            matched_docs = []
                    
            try:
                query_text = " ".join(entities["mod_names"])
                if query_text.strip():
                    base_retriever = self.vector_db.as_retriever(
                        search_kwargs={
                            "k": self.k * 10,
                            "filter": {"类型": "模组主体"}
                        }
                    )
                    similarity_results = base_retriever.invoke(query_text)
                    scored_docs = [
                        (doc, self._score_document(doc, entities)) for doc in similarity_results
                    ]
                    scored_docs = [doc for doc in scored_docs if doc[1] > 0]
                    scored_docs.sort(key=lambda x: x[1], reverse=True)
                    matched_docs.extend([doc[0] for doc in scored_docs])
                    logger.info(f"mode向量相似性搜索找到 {len(matched_docs)} 个文档，最高分是 {scored_docs[0][1] if scored_docs else 0}")
            except Exception as e:
                logger.warning(f"mode向量相似性搜索失败: {e}")

            with open("mode_metadata_matched_docs.txt", "w", encoding="utf-8") as f:
                for doc in matched_docs:
                    f.write(f"文档内容: {doc.page_content}\n")
                    f.write(f"元数据: {json.dumps(doc.metadata, ensure_ascii=False)}\n")
                    f.write("-" * 50 + "\n")
            return matched_docs[:self.k]

        except Exception as e:
            logger.warning(f"元数据过滤出错: {e}")

        logger.info("元数据过滤没有找到匹配的文档")
        return []

    def _score_document(self, doc, entities):
        """
        为文档评分

        Args:
            doc: 文档对象
            entities: 提取的实体

        Returns:
            int: 文档得分
        """
        score = 0
        if "模组名称" in doc.metadata and entities["mod_names"]:
            doc_mod_name = process_name(doc.metadata["模组名称"]).lower()
            for mod_name in entities["mod_names"]:
                if doc_mod_name == mod_name:
                    score += 4
                    break
                if doc_mod_name in mod_name or mod_name in doc_mod_name:
                    score += 2
                    break
        return score

    def with_config(self, **kwargs):
        """配置检索器参数"""
        if "k" in kwargs:
            self.k = kwargs["k"]
        return self
    
class WikiMetadataRetriever(Runnable):
    """
    元数据匹配检索器，专为Minecraft wiki领域设计

    能够从用户查询中提取wiki关键词，并根据元数据匹配为文档评分。
    当无匹配时提供关键词回退机制。
    """
    
    def __init__(self, vector_db, llm, k=4):
        """
        初始化元数据匹配检索器

        Args:
            vector_db: 向量数据库
            llm: 用于提取实体的语言模型
            k: 返回的最大文档数量
        """
        self.vector_db = vector_db
        self.llm = llm
        self.k = k

    def _extract_entities(self, query):
        """
        从查询中提取wiki关键词

        Args:
            query: 用户查询

        Returns:
            dict: 包含提取的wiki关键词的字典
        """
        prompt = WIKI_METADATA_PROMPT_TEMPLATE.format(query=query)
        try:
            messages = [HumanMessage(content=prompt)]
            response = self.llm.invoke(messages)
            response_text = response.content if hasattr(response, 'content') else str(response)
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group())
                return {
                    "wiki_keywords": [name.lower() for name in result.get("wiki_keywords", [])]
                }
            else:
                logger.warning(f"无法从响应中提取JSON: {response_text}")
        except Exception as e:
            logger.error(f"实体提取失败: {e}")
            logger.error(f"LLM响应: {response if 'response' in locals() else '无响应'}")
        return {"wiki_keywords": []}

    def invoke(self, query, config=None, **kwargs):
        """
        执行检索

        Args:
            query: 用户查询
            config: 配置参数
            **kwargs: 其他参数

        Returns:
            list: 相关文档列表
        """
        entities = self._extract_entities(query)
        logger.info(f"提取的实体: {entities}")
        
        if not entities["wiki_keywords"]:
            logger.info("没有提取到实体，跳过元数据过滤")
            return []
        matched_docs = []
        try:
            query_text = " ".join(entities["wiki_keywords"])
            if query_text.strip():
                base_retriever = self.vector_db.as_retriever(
                    search_kwargs={
                        "k": self.k * 10,
                        "filter": {"类型": "wiki"}
                    }
                )
                similarity_results = base_retriever.invoke(query_text)
                scored_docs = [
                    (doc, self._score_document(doc, entities)) for doc in similarity_results
                ]
                scored_docs = [doc for doc in scored_docs if doc[1] > 0]
                scored_docs.sort(key=lambda x: x[1], reverse=True)
                matched_docs.extend([doc[0] for doc in scored_docs])
                logger.info(f"wiki向量相似性搜索找到 {len(matched_docs)} 个文档，最高分是 {scored_docs[0][1] if scored_docs else 0}")
        except Exception as e:
            logger.warning(f"wiki向量相似性搜索失败: {e}")

        with open("wiki_metadata_matched_docs.txt", "w", encoding="utf-8") as f:
            for doc in matched_docs:
                f.write(f"文档内容: {doc.page_content}\n")
                f.write(f"元数据: {json.dumps(doc.metadata, ensure_ascii=False)}\n")
                f.write("-" * 50 + "\n")
        return matched_docs[:self.k]    

    def _score_document(self, doc, entities):
        """
        为文档评分

        Args:
            doc: 文档对象
            entities: 提取的实体

        Returns:
            int: 文档得分
        """
        score = 0
        if "标题" in doc.metadata and entities["wiki_keywords"]:
            for keyword in entities["wiki_keywords"]:
                if keyword == doc.metadata["标题"]:
                    score += 5
                    break
                if keyword in doc.metadata["标题"] or doc.metadata["标题"] in keyword:
                    score += 3
                    break
        if "摘要" in doc.metadata and entities["wiki_keywords"]:
            for keyword in entities["wiki_keywords"]:
                if keyword in doc.metadata["摘要"]:
                    score += 2
                    break
        return score

    def with_config(self, **kwargs):
        """配置检索器参数"""
        if "k" in kwargs:
            self.k = kwargs["k"]
        return self
    
class TUTORIALMetadataRetriever(Runnable):
    """
    元数据匹配检索器，专为Minecraft教程领域设计

    能够从用户查询中提取教程关键词，并根据元数据匹配为文档评分。
    当无匹配时提供关键词回退机制。
    """
    
    def __init__(self, vector_db, llm, k=4):
        """
        初始化元数据匹配检索器

        Args:
            vector_db: 向量数据库
            llm: 用于提取实体的语言模型
            k: 返回的最大文档数量
        """
        self.vector_db = vector_db
        self.llm = llm
        self.k = k

    def _extract_entities(self, query):
        """
        从查询中提取教程关键词

        Args:
            query: 用户查询

        Returns:
            dict: 包含提取的教程关键词的字典
        """
        prompt = TUTORIAL_METADATA_PROMPT_TEMPLATE.format(query=query)
        try:
            messages = [HumanMessage(content=prompt)]
            response = self.llm.invoke(messages)
            response_text = response.content if hasattr(response, 'content') else str(response)
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group())
                return {
                    "tutorial_keywords": [name.lower() for name in result.get("tutorial_keywords", [])]
                }
            else:
                logger.warning(f"无法从响应中提取JSON: {response_text}")
        except Exception as e:
            logger.error(f"实体提取失败: {e}")
            logger.error(f"LLM响应: {response if 'response' in locals() else '无响应'}")
        return {"tutorial_keywords": []}

    def invoke(self, query, config=None, **kwargs):
        """
        执行检索

        Args:
            query: 用户查询
            config: 配置参数
            **kwargs: 其他参数

        Returns:
            list: 相关文档列表
        """
        entities = self._extract_entities(query)
        logger.info(f"提取的实体: {entities}")
        
        if not entities["tutorial_keywords"]:
            logger.info("没有提取到实体，跳过元数据过滤")
            return []
        matched_docs = []
        try:
            query_text = " ".join(entities["tutorial_keywords"])
            if query_text.strip():
                base_retriever = self.vector_db.as_retriever(
                    search_kwargs={
                        "k": self.k * 10,
                        "filter": {"类型": "教程"}
                    }
                )
                similarity_results = base_retriever.invoke(query_text)
                scored_docs = [
                    (doc, self._score_document(doc, entities)) for doc in similarity_results
                ]
                scored_docs = [doc for doc in scored_docs if doc[1] > 0]
                scored_docs.sort(key=lambda x: x[1], reverse=True)
                matched_docs.extend([doc[0] for doc in scored_docs])
                logger.info(f"教程向量相似性搜索找到 {len(matched_docs)} 个文档，最高分是 {scored_docs[0][1] if scored_docs else 0}")
        except Exception as e:
            logger.warning(f"教程向量相似性搜索失败: {e}")

        with open("tutorial_metadata_matched_docs.txt", "w", encoding="utf-8") as f:
            for doc in matched_docs:
                f.write(f"文档内容: {doc.page_content}\n")
                f.write(f"元数据: {json.dumps(doc.metadata, ensure_ascii=False)}\n")
                f.write("-" * 50 + "\n")
        return matched_docs[:self.k]    

    def _score_document(self, doc, entities):
        """
        为文档评分

        Args:
            doc: 文档对象
            entities: 提取的实体

        Returns:
            int: 文档得分
        """
        score = 0
        if "摘要" in doc.metadata and entities["tutorial_keywords"]:
            for keyword in entities["tutorial_keywords"]:
                if keyword in doc.metadata["摘要"]:
                    score += 2
                    break
        return score

    def with_config(self, **kwargs):
        """配置检索器参数"""
        if "k" in kwargs:
            self.k = kwargs["k"]
        return self



