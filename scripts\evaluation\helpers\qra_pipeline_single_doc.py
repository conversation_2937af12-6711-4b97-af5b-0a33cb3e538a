"""
This implementation is adapted from the RAGEval repository. Original repository: https://github.com/OpenBMB/RAGEval
"""

import logging
import os
import sys
from pathlib import Path

from helpers.client import OpenAIClient as Client
from helpers.utils import read_prompt, write_config_json
from helpers.postprocess import postprocess_zh

# Load environment variables
api_key = os.getenv('API_KEY')
base_url = os.getenv('BASE_URL')

# Validate environment variables
if not api_key:
  logging.error("API_KEY is not set in the environment.")
  sys.exit(1)


def generate_qra(model_name: str, outline: str, output_path: Path) -> None:
  """Process each document to generate QRA triples."""
  prompts = read_prompt(Path(__file__).parent / '../prompts/qra_zh.jsonl')
  prompt_dict = {p['prompt_type']: p for p in prompts if p['prompt_type'] in ['事实性问题', '多跳推理问题', '总结性问题', '单文档reference抽取']}

  # Generate QA pairs
  tasks = [
      {
          'system_prompt': prompt_dict[key]['system_prompt'],
          'user_prompt': prompt_dict[key]['user_prompt'].format(outline=outline),
      }
      for key in ['事实性问题', '多跳推理问题', '总结性问题']
  ]
  client = Client(api_key=api_key, base_url=base_url, model_name=model_name)
  responses = client.generate(tasks, desc='Generating QA pairs')

  # Postprocess the responses
  qa_types = ['qa_fact_based', 'qa_multi_hop', 'qa_summary']
  config = {}
  for i, key in enumerate(qa_types):
    responses[i] = postprocess_zh(
        response=responses[i],
        system_prompt=tasks[i]['system_prompt'],
        user_prompt=tasks[i]['user_prompt'],
        model_name=model_name,
    )
    config[key] = responses[i]

  # Additional processing for reference extraction
  qa_tasks = [
      {
          "system_prompt": prompt_dict['单文档reference抽取']['system_prompt'],
          "user_prompt": prompt_dict['单文档reference抽取']['user_prompt'].format(outline=outline, qa_pairs=config[key])
      }
      for key in qa_types
  ]
  responses = client.generate(qa_tasks, desc='Extracting references')

  # Postprocess again
  for i, key in enumerate(qa_types):
    responses[i] = postprocess_zh(
        response=responses[i],
        system_prompt=qa_tasks[i]['system_prompt'],
        user_prompt=qa_tasks[i]['user_prompt'],
        model_name=model_name,
    )
    config[key] = responses[i]

  write_config_json(output_path, config)
