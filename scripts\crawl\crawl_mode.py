import logging
import os
import random
import re
import time
import requests
from bs4 import BeautifulSoup, NavigableString
import json
from urllib.parse import urljoin
from pathlib import Path
import traceback # Added for detailed error logging

# --- Configuration (Merged from New Script) ---
# NOTE: Since the original script wasn't multi-threaded, NUM_WORKERS is set to 1.
# If you intend to add threading later, increase this and adapt the main loop.
NUM_WORKERS = 1 # Keep it 1 for single-threaded operation based on old script structure
PROXY_API_URL = "https://dps.kdlapi.com/api/getdps/?secret_id=o908aykc7vhhaftk9iz6&signature=5818amln1aur09moioq1sie91299nzlv&num={num}&pt=1&sep=1" # From New
PROXY_USER = "d3758262232" # From New
PROXY_PASS = "e6upmnib" # From New

ID_FILE = './class_ids.txt' # From New (or adjust if needed)
OUTPUT_FOLDER = './mode' # From New
LOG_FILE = 'crawl_mode_log_merged.txt' # Renamed log file
REQUEST_TIMEOUT = 20 # From New
MAX_RETRIES = 3 # From New

USER_AGENTS = [ # From New
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) RAG-Data-Collector/1.0'
]

CATEGORY_MAP = { # From New
    1: "科技", 2: "魔法", 3: "冒险", 4: "农业", 5: "装饰",
    6: "安全", 7: "LIB", 8: "资源", 9: "世界", 10: "群系",
    11: "生物", 12: "能源", 13: "存储", 14: "物流", 15: "道具",
    16: "红石", 17: "食物", 18: "模型", 19: "指南", 20: "破坏",
    21: "魔改", 22: "Meme", 23: "实用", 24: "辅助", 25: "中式",
    26: "日式", 27: "西式", 28: "恐怖", 29: "建材", 30: "生存",
    31: "指令", 32: "优化", 33: "国创", 34: "关卡", 35: "结构"
}

# --- Global Variables (From New) ---
proxies = [] # Global proxy list (will hold only one proxy for this version)

# --- Logging Setup (From New) ---
logging.basicConfig(filename=LOG_FILE, level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S')

def my_log(msg):
    """Logs a message to both console and the log file."""
    print(msg)
    logging.info(msg)

# --- Proxy Management (Adapted from New for single proxy) ---
def proxy_init():
    """Initializes the proxy list with ONE proxy."""
    global proxies
    # Request only 1 proxy since the base script is single-threaded
    api_url = PROXY_API_URL.format(num=1)
    my_log(f"初始化代理 (数量: 1) from {api_url}...")
    try:
        response = requests.get(api_url, timeout=30)
        response.raise_for_status() # Raise an exception for bad status codes
        proxy_ip = response.text.strip()

        if not proxy_ip:
            raise ValueError(f"获取代理失败，返回空 IP")

        proxies = [ # Store the single proxy in the list
            {
                "http":  f"http://{PROXY_USER}:{PROXY_PASS}@{proxy_ip}/",
                "https": f"http://{PROXY_USER}:{PROXY_PASS}@{proxy_ip}/"
            }
        ]
        my_log(f"成功初始化 1 个代理: {proxy_ip}")
        return True
    except requests.exceptions.RequestException as e:
        my_log(f"代理初始化请求失败: {e}")
    except Exception as e:
        my_log(f"代理初始化失败: {e}")
    return False

def refresh_proxy():
    """Refreshes the single proxy."""
    global proxies
    if not proxies:
         my_log("代理列表为空，无法刷新。请先调用 proxy_init().")
         return False # Indicate failure

    api_url = PROXY_API_URL.format(num=1)
    my_log(f"正在刷新代理 from {api_url}...")
    try:
        response = requests.get(api_url, timeout=30)
        response.raise_for_status()
        proxy_ip = response.text.strip()
        if proxy_ip:
            new_proxy = {
                "http":  f"http://{PROXY_USER}:{PROXY_PASS}@{proxy_ip}/",
                "https": f"http://{PROXY_USER}:{PROXY_PASS}@{proxy_ip}/"
            }
            proxies[0] = new_proxy # Replace the first (only) proxy
            my_log(f"代理已更新为 {proxy_ip}")
            return True # Indicate success
        else:
            my_log(f"获取新代理失败，返回空IP")
    except requests.exceptions.RequestException as e:
        my_log(f"刷新代理请求失败: {e}")
    except Exception as e:
        my_log(f"刷新代理时出错: {e}")

    # Refresh failed, maybe keep the old one? Or clear it?
    # Let's keep the old one for now, but log the failure.
    my_log(f"代理刷新失败，将继续使用旧代理 (如果存在).")
    return False # Indicate failure

# --- Text and HTML Cleaning (Kept from Old, similar to New) ---
def clean_text(text):
    """Cleans text by removing excessive whitespace and newlines."""
    if text:
        # Replace non-breaking spaces first (important for some sites)
        text = text.replace('\xa0', ' ')
        # Replace multiple spaces/tabs/newlines with a single space
        text = re.sub(r'\s+', ' ', text)
        # Specific handling for multiple newlines if needed after space normalization
        # text = re.sub(r'\n+', '\n', text) # This might be redundant after \s+
        return text.strip()
    return ""

def clean_html_content(content):
    """Basic HTML cleaning from original script."""
    # This version is simpler than the new script's, preserving old behavior.
    # If more robust cleaning is needed, integrate the new script's version.
    for br in content.find_all('br'):
        br.replace_with('\n')
    # Get text, separating block elements with newlines might be better here.
    # Using the original approach for now:
    # return clean_text(content.get_text(separator='\n', strip=True))
    # Let's use a slightly more robust version combining ideas:
    for script_or_style in content(['script', 'style']):
        script_or_style.decompose()
    text = content.get_text(separator='\n', strip=True)
    return clean_text(text) # Use the updated clean_text

# --- Scraping Functions (Adapted from Old, incorporating session, proxy, error handling) ---

def handle_item_block_url(url, session, proxy):
    """Fetches and parses item/block list pages using session and proxy (Adapted from Old + New)."""
    try:
        my_log(f"  Fetching item block URL: {url}")
        response = session.get(url, timeout=REQUEST_TIMEOUT, proxies=proxy, headers={'User-Agent': random.choice(USER_AGENTS)})
        if response.status_code == 403:
            my_log(f"  [403 Blocked] Item block URL: {url}")
            return "blocked"
        response.raise_for_status()
        response.encoding = 'utf-8'
        soup = BeautifulSoup(response.text, 'html.parser')
        if not soup:
            my_log(f"  无法解析 HTML: {url}")
            return [] # Return empty list on parsing failure

        tables = soup.find_all('table')
        if not tables:
            # It's possible the page has data but not in tables, check original logic if needed.
            my_log(f"  未找到表格: {url}")
            # Let's check for list items directly as a fallback, similar to original logic
            lis = soup.select('ul li a[href^="/item/"]')
            if not lis:
                 return [] # No tables and no direct list items found

        items = []
        # Process tables if found
        for table in tables:
            lis = table.select('ul li')
            for li in lis:
                a_tag = li.select_one('a[href^="/item/"]')
                if a_tag:
                    item_name = clean_text(a_tag.text.strip())
                    item_url = a_tag['href'].strip()
                    match = re.search(r'/item/(\d+)\.html', item_url)
                    if match:
                        item_id = match.group(1)
                        items.append({
                            "name": item_name,
                            "id": item_id
                        })

        # Fallback: Process list items directly if no tables were found
        if not tables and lis:
             my_log(f"  未找到表格，尝试直接解析列表项: {url}")
             for a_tag in lis:
                 item_name = clean_text(a_tag.text.strip())
                 item_url = a_tag['href'].strip()
                 match = re.search(r'/item/(\d+)\.html', item_url)
                 if match:
                     item_id = match.group(1)
                     items.append({
                         "name": item_name,
                         "id": item_id
                     })

        my_log(f"  成功获取 {len(items)} items from {url}")
        return items
    except requests.exceptions.Timeout:
        my_log(f"  [Timeout] Item block URL: {url}")
        return "timeout"
    except requests.exceptions.RequestException as e:
        my_log(f"  错误 fetching item block URL {url}: {e}")
        return "error"
    except Exception as e:
        my_log(f"  解析 item block URL 时出错 {url}: {e}")
        return "error"


# Helper function from old script for post content cleaning (slightly adapted)
def clean_post_html_content_old(html_content):
    """Cleans post HTML based on the OLD script's specific logic."""
    soup = BeautifulSoup(html_content, 'html.parser')
    title = ""
    title_tag = soup.select_one(".postname .name h5") # Selector from old process_post cleanup
    if title_tag:
        title = clean_text(title_tag.text.strip()) # Use shared clean_text

    content_div = soup.select_one(".post-content .text") # Selector from old process_post cleanup
    if not content_div:
        return title, "" # Return title even if content is missing

    cleaned_content_list = []
    if title: # Prepend title as in old logic
        cleaned_content_list.append(title)

    # Iterate through specific tags like in the old logic
    for elem in content_div.find_all(['p', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
        text = elem.get_text(strip=True)
        if text:
            cleaned_content_list.append(clean_text(text)) # Clean each part

    result = "\n".join(cleaned_content_list)
    result = result.replace("&nbsp;", " ") # From old logic
    result = clean_text(result) # Final cleanup
    return title, result

# URL patterns definition (kept from old script, used locally in get_mod_data)
url_patterns = {
    1: "data_item1_block", 2: "data_item2_biome", 3: "data_item3_dimension",
    4: "data_item4_entity", 5: "data_item5_enchantment", 6: "data_item6_buff",
    7: "data_item7_multiblock", 8: "data_item8_natural_generation",
    9: "data_item9_hotkey", 10: "data_item10_game_setting"
}

def get_mod_data(mod_id):
    """Fetches and processes data for a single mod ID using assigned proxy and retries (Old Structure + New Features)."""
    filepath = os.path.join(OUTPUT_FOLDER, f"mode{mod_id}.json")
    if os.path.exists(filepath):
        # my_log(f"Mod {mod_id} 已存在，跳过.")
        return True # Already processed

    session = requests.Session()
    adapter = requests.adapters.HTTPAdapter(pool_connections=10, pool_maxsize=10) # Good practice from new
    session.mount('http://', adapter)
    session.mount('https://', adapter)

    url = f"https://www.mcmod.cn/class/{mod_id}.html"
    headers = { # Keep headers from old script
        'User-Agent': random.choice(USER_AGENTS), # Use the list from new
        'Referer': 'https://www.mcmod.cn/'
    }

    for attempt in range(MAX_RETRIES):
        if not proxies:
             my_log(f"Mod {mod_id} [失败] - 无可用代理.")
             return False
        proxy = proxies[0] # Get the single proxy

        my_log(f"处理 Mod {mod_id} (尝试 {attempt + 1}/{MAX_RETRIES}) 使用代理 {proxy.get('http', 'N/A').split('@')[-1]}")
        time.sleep(random.uniform(0.5, 1.5)) # Delay between attempts

        try:
            response = session.get(url, headers=headers, proxies=proxy, timeout=REQUEST_TIMEOUT)

            if response.status_code == 403:
                my_log(f"Mod {mod_id} [403 Blocked]! (尝试 {attempt + 1})")
                refresh_proxy() # Refresh the single proxy
                continue # Retry with the potentially new proxy

            if response.status_code == 404:
                my_log(f"Mod {mod_id} [404 Not Found] at {url}")
                return False # Don't retry on 404

            response.raise_for_status() # Raise for other bad status codes (e.g., 5xx)
            response.encoding = 'utf-8' # Consistent encoding
            soup = BeautifulSoup(response.text, 'html.parser')
            current_url = response.url # Use the final URL

            # --- Start Parsing (using OLD script's structure/selectors) ---
            title_div = soup.find('div', {'class': 'class-title'})
            if not title_div:
                # This check was present in the new script, good to keep
                my_log(f"Mod {mod_id}: 未找到标题 div (class-title). 可能页面结构已更改或内容无效.")
                return False # Cannot proceed without title

            # --- Initialize data dict ---
            data = {"id": mod_id}

            # --- Extract basic info (using OLD script's selectors/logic) ---
            data["status"] = clean_text(title_div.find('div', class_='class-status').text) if title_div.find('div', class_='class-status') else "无"
            data["source"] = clean_text(title_div.find('div', class_='class-source').text) if title_div.find('div', class_='class-source') else "未开源"
            data["short_name"] = clean_text(title_div.find('span', class_='short-name').text[1:-1]) if title_div.find('span', class_='short-name') else "无缩写"
            data["chinese_name"] = clean_text(title_div.find('h3').text)
            data["english_name"] = clean_text(title_div.find('h4').text) if title_div.find('h4') else ""

            # --- Extract supported versions (using OLD script's selectors/logic) ---
            version_li = soup.find('li', class_='mcver')
            if version_li:
                supported_versions = []
                main_ul = version_li.find('ul')
                if main_ul:
                    type_uls = main_ul.find_all('ul', recursive=False)
                    for type_ul in type_uls:
                        type_name_li = type_ul.find('li', recursive=False) # First li is type name in old structure
                        if not type_name_li: continue
                        type_name = clean_text(type_name_li.get_text(strip=True).rstrip(':'))
                        versions = []
                        # Get remaining LIs for versions (start from index 1)
                        version_lis = type_ul.find_all('li')[1:]
                        for li in version_lis:
                            a_tag = li.find('a', href=lambda x: x and 'mcver=' in x)
                            if a_tag:
                                try:
                                    # Extract version robustly
                                    version = a_tag['href'].split('mcver=')[1].split('&')[0]
                                    versions.append(version)
                                except (IndexError, AttributeError):
                                     my_log(f"Mod {mod_id}: 解析版本链接时出错: {a_tag}")
                        if versions:
                            type_str = f"{type_name}:{';'.join(versions)}"
                            supported_versions.append(type_str)
                data["supported_versions"] = '/'.join(supported_versions) if supported_versions else ""
            else:
                data["supported_versions"] = ""

            # --- !! NEW: Extract Categories (Logic from New Script) !! ---
            category_main_name = None
            category_tags_list = [] # Use a list to store tags

            category_div = soup.find('div', class_='common-class-category')
            if category_div:
                main_li = category_div.find('li', class_='main')
                if main_li:
                    main_a = main_li.find('a')
                    if main_a and main_a.has_attr('href'):
                        href = main_a['href']
                        id_match = re.search(r'category=(\d+)', href)
                        if id_match:
                            try:
                                category_id = int(id_match.group(1))
                                category_main_name = CATEGORY_MAP.get(category_id, f"未知分类ID({category_id})")
                            except ValueError:
                                my_log(f"Mod {mod_id}: Extracted category ID '{id_match.group(1)}' is not a valid integer.")
                                category_main_name = "无效分类ID"
                        else:
                            my_log(f"Mod {mod_id}: Could not extract category ID from href: {href}")
                            category_main_name = "未找到分类ID"
                    else:
                         my_log(f"Mod {mod_id}: Main category <li> found, but no valid <a> tag with href found.")
                         category_main_name = "主分类链接无效"
                else:
                    my_log(f"Mod {mod_id}: Main category <li> (class='main') not found.")
                    category_main_name = "未找到主分类Li"

                # Find Secondary Categories (Tags)
                tag_links = category_div.select('ul > li > a.normal') # Selector from new
                for tag_a in tag_links:
                    tag_text = clean_text(tag_a.get_text(strip=True))
                    if tag_text:
                        category_tags_list.append(tag_text)
            else:
                my_log(f"Mod {mod_id}: Category div ('common-class-category') not found.")
                category_main_name = "分类区块未找到" # Set default if div is missing

            # Add extracted category info to data dict with requested key names
            data['categoryMain'] = category_main_name if category_main_name else "未指定" # Provide default
            data['categorys'] = category_tags_list # Assign the list of tags

            # --- Process Frame 1: Data/Relations (Using OLD script's selectors/logic) ---
            content_div_frame1 = soup.find('div', {'class': 'class-menu-main', 'data-frame': '1'})
            if content_div_frame1:
                # Mod Data (Items, Blocks, etc.)
                data_li = content_div_frame1.find('li', {'data-id': '1'})
                mod_data_results = [] # Renamed from 'results' in old script for clarity
                if data_li:
                    # Using 'damn' variable name from old script
                    damn = data_li.find_all('li', class_=re.compile(r'mold-\d+'))
                    if damn:
                        for mold_li in damn: # Renamed from 'mold' in old script
                            if 'mold-0' in mold_li.get('class', []): continue # Skip mold-0 as per old logic

                            link_tag = mold_li.find('a')
                            if not link_tag: continue

                            try: # Add try-except for robustness
                                title = clean_text(link_tag.find('span', class_='title').text)
                                desc = clean_text(link_tag.find('span', class_='content').text)
                                count_text = link_tag.find('span', class_='count').text
                                count_match = re.search(r'\((\d+)', count_text) # Safer count extraction
                                count = int(count_match.group(1)) if count_match else 0
                                relative_url = link_tag['href']
                                absolute_url = urljoin(current_url, relative_url)

                                mod_data_info = {
                                    "category": title, "description": desc,
                                    "count": count, "url": absolute_url
                                }
                                mod_data_results.append(mod_data_info)

                                # Check URL patterns and fetch item/block data (logic from old + new error handling)
                                for pattern_id, data_key in url_patterns.items():
                                    if f"/item/list/{mod_id}-{pattern_id}.html" in relative_url:
                                        item_block_data = handle_item_block_url(absolute_url, session, proxy)
                                        # Handle return status from sub-function
                                        if item_block_data == "blocked":
                                            my_log(f"Mod {mod_id}: 获取 {data_key} 时被阻止 ({absolute_url})")
                                            refresh_proxy()
                                            # Mark as failed for this attempt, retry might fetch it
                                            data[data_key] = "blocked"
                                            # Optionally raise an exception to force outer retry immediately
                                            # raise ConnectionError(f"Sub-request blocked for {data_key}")
                                        elif item_block_data == "timeout":
                                            my_log(f"Mod {mod_id}: 获取 {data_key} 时超时 ({absolute_url})")
                                            data[data_key] = "timeout"
                                            # Optionally raise exception
                                        elif item_block_data == "error":
                                             my_log(f"Mod {mod_id}: 获取 {data_key} 时出错 ({absolute_url})")
                                             data[data_key] = "error"
                                        else: # Success
                                            data[data_key] = item_block_data
                                        break # Found pattern, move to next mold_li
                            except Exception as e:
                                 my_log(f"Mod {mod_id}: 解析 mod data link 时出错 ({link_tag}): {e}")
                                 traceback.print_exc() # Log traceback

                data["mod_data"] = mod_data_results # Assign collected data list

                # Mod Relations (Using OLD script's selectors/logic)
                relations = []
                relation_li = content_div_frame1.find('li', {'data-id': '2'})
                if relation_li:
                    for fieldset in relation_li.find_all('fieldset'):
                        legend_tag = fieldset.find('legend')
                        legend = clean_text(legend_tag.text) if legend_tag else "Unknown Version"
                        # Old script iterates through 'li.relation', new uses 'ul.relation'. Check HTML structure.
                        # Assuming 'ul.relation' from new script is more standard, let's try that first.
                        relation_containers = fieldset.find_all('ul', class_='relation')
                        if not relation_containers:
                             # Fallback to old script's 'li.relation' if 'ul.relation' not found
                             relation_containers = fieldset.find_all('li', class_='relation')

                        for rel_container in relation_containers: # Use generic name
                            relation_span = rel_container.find('span') # Type is usually in a span
                            relation_type = clean_text(relation_span.text) if relation_span else "Unknown Type"
                            mods = []
                            for mod_a in rel_container.find_all('a'): # Links are in <a> tags
                                mod_name = clean_text(mod_a.get_text(strip=True))
                                mod_url = urljoin(current_url, mod_a['href'])
                                mod_id_match = re.search(r'/class/(\d+)\.html', mod_url)
                                related_mod_id = mod_id_match.group(1) if mod_id_match else None
                                if related_mod_id:
                                    mods.append({ "name": mod_name, "url": mod_url, "id": related_mod_id })
                            if mods:
                                relations.append({ "version": legend, "type": relation_type, "mods": mods })
                data["mod_relations"] = relations

            # --- Process Frame 2: Intro/Tutorial/Discussion (Using OLD script's selectors/logic) ---
            content_div_frame2 = soup.find('div', {'class': 'class-menu-main', 'data-frame': '2'})
            if content_div_frame2:
                # Mod Intro
                intro_li = content_div_frame2.find('li', {'data-id': '1'})
                if intro_li:
                    # Use the cleaning approach from the old script's frame 2 processing
                    # Create a temporary soup object for manipulation
                    intro_soup = BeautifulSoup(str(intro_li), 'html.parser')
                    for a_tag in intro_soup.find_all('a'):
                        href = a_tag.get('href', '')
                        link_text = clean_text(a_tag.get_text(strip=True))
                        # Replace invalid links with text, make others absolute
                        if not href or href.strip() in ('#', 'javascript:void(0);'):
                            a_tag.replace_with(link_text)
                        else:
                            full_url = urljoin(current_url, href)
                            # Format as Markdown-like link text
                            a_tag.replace_with(f"{link_text} ({full_url})")
                    # Get cleaned text after link replacement
                    cleaned_intro_text = clean_text(intro_soup.get_text('\n', strip=True))
                    data["mod_intro"] = cleaned_intro_text
                else:
                    data["mod_intro"] = "" # Default if missing

                # Mod Tutorial
                tutorial_li = content_div_frame2.find('li', {'data-id': '2'})
                tutorials_list = [] # Store tutorial info
                post_links_to_process = [] # Store links for sub-processing
                if tutorial_li:
                    for post_div in tutorial_li.find_all('div', class_='post-block'): # Selector from old
                         title_div = post_div.select_one('.info .title a') # Selector from old
                         if title_div and title_div.has_attr('href'):
                             title = clean_text(title_div.get_text(strip=True))
                             link = urljoin(current_url, title_div['href'])
                             tutorials_list.append(f"{title} ({link})") # Format from old
                             if "/post/" in link:
                                 post_links_to_process.append(link)

                data["mod_tutorial"] = "; ".join(tutorials_list) if tutorials_list else "" # Join like old script

                # Mod Discussion
                discussion_li = content_div_frame2.find('li', {'data-id': '3'})
                discussions_list = []
                bbs_links_to_process = []
                if discussion_li:
                    thread_list = discussion_li.find('ul', class_='class-thread-list') # Selector from old
                    if thread_list:
                        for thread_li in thread_list.find_all('li'):
                            a_tag = thread_li.find('a')
                            if a_tag and a_tag.has_attr('href'):
                                title = clean_text(a_tag.get_text(strip=True))
                                link = urljoin(current_url, a_tag['href'])
                                discussions_list.append(f"{title} ({link})") # Format from old
                                if "bbs.mcmod.cn/thread-" in link:
                                    bbs_links_to_process.append(link)

                data["mod_discussion"] = "; ".join(discussions_list) if discussions_list else "" # Join like old script

            # --- Save successful result ---
            os.makedirs(OUTPUT_FOLDER, exist_ok=True)
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=4)
            my_log(f"Mod {mod_id} [成功]. 已保存到 {filepath}")
            return True # Success for this mod_id

        # --- Handle Exceptions during the request/parsing ---
        except requests.exceptions.Timeout as e:
            my_log(f"Mod {mod_id} [Timeout] (尝试 {attempt + 1}): {e}")
            # Refresh proxy on timeout might help
            refresh_proxy()
        except requests.exceptions.RequestException as e:
            my_log(f"Mod {mod_id} [Request Error] (尝试 {attempt + 1}): {e}")
            # Refresh proxy on general request errors too
            refresh_proxy()
        except Exception as e:
            my_log(f"Mod {mod_id} [处理错误] (尝试 {attempt + 1}): {e}")
            traceback.print_exc() # Print full traceback for debugging
            # Optional: Refresh proxy even for parsing errors
            # refresh_proxy()

        # --- End of single attempt ---

    # --- If loop completes after all retries without success ---
    my_log(f"Mod {mod_id} [失败] after {MAX_RETRIES} attempts.")
    return False


def read_mod_ids(filename=ID_FILE):
    """Reads mod IDs from the specified file, skipping the first line (from New)."""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        # Skip first line (i > 0), filter empty lines, convert to int
        mod_ids = [int(line.strip()) for i, line in enumerate(lines) if i > 0 and line.strip()]
        my_log(f"从 {filename} 读取 {len(mod_ids)} 个 mod IDs.")
        return mod_ids
    except FileNotFoundError:
        my_log(f"错误: ID 文件 '{filename}' 未找到.")
        return []
    except ValueError as e:
        my_log(f"错误: ID 文件 '{filename}' 中包含无效的数字: {e}")
        return []
    except Exception as e:
        my_log(f"读取 ID 文件时出错 '{filename}': {e}")
        return []

# --- Main Execution (Adapted from Old + New) ---
if __name__ == "__main__":
    start_time = time.perf_counter()
    my_log("脚本开始执行 (合并版本)...")
    os.makedirs(OUTPUT_FOLDER, exist_ok=True) # Ensure output folder exists

    # 1. Initialize Proxy (Single Proxy)
    if not proxy_init():
        my_log("代理初始化失败，退出脚本.")
        exit(1)

    success_count = 0
    failure_count = 0

    # 3. Process mods sequentially (as per old script structure)
    for mod_id in range(1, 5000):
        my_log(f"\n--- 开始处理 Mod {mod_id}---")
        filepath = os.path.join(OUTPUT_FOLDER, f"mode{mod_id}.json")
        if os.path.exists(filepath):
             my_log(f"Mod {mod_id} 文件已存在，跳过处理.")
             success_count += 1
             continue

        success = get_mod_data(mod_id)
        if success:
            success_count += 1
        else:
            failure_count += 1

    # 4. Final Summary
    elapsed = time.perf_counter() - start_time
    my_log("-" * 30)
    my_log(f"脚本执行完毕.")
    my_log(f"总耗时: {elapsed:.2f} 秒")
    my_log(f"成功 (或已存在): {success_count}")
    my_log(f"失败: {failure_count}")
    my_log("-" * 30)