import subprocess
import os
import sys

def run_uvicorn():
    # 构建 uvicorn 命令
    # 使用 sys.executable 确保使用当前 Python 环境的 uvicorn
    command = [
        sys.executable, # Python解释器路径
        "-m", "uvicorn",
        "src.rag.main:app",
        "--host", "0.0.0.0",
        "--port", "8080",
        "--reload",  # 开发时使用热重载
        "--reload-dir", "src",
    ]

    print(f"Running command: {' '.join(command)}")

    try:
        # 在项目根目录（脚本所在的目录）下执行命令
        project_root = os.path.dirname(os.path.abspath(__file__))
        process = subprocess.Popen(command, cwd=project_root)
        process.wait() # 等待 Uvicorn 进程结束
    except KeyboardInterrupt:
        print("\nServer stopped by user.")
    except Exception as e:
        print(f"Failed to start server: {e}")

if __name__ == "__main__":
    run_uvicorn()