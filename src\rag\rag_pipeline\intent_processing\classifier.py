"""
意图分类器模块

此模块提供了意图分类器的实现，用于从用户查询中识别意图。
"""

import json
import re
import time
from typing import List
from langchain_core.messages import HumanMessage
from langchain_openai import ChatOpenAI

from ...prompts import INTENT_CLASSIFICATION_PROMPT_TEMPLATE
from ...logging_config import get_logger

# 使用统一的日志配置
logger = get_logger("intent_classifier")

class IntentClassifier:
    """
    意图分类器，用于从用户查询中识别意图

    能够识别多个意图，并以JSON格式返回结果。
    """

    def __init__(self, llm: ChatOpenAI):
        """
        初始化意图分类器

        Args:
            llm: 用于意图分类的语言模型
        """
        self.llm = llm

    async def classify_intent(self, query: str) -> List[str]:
        """
        从用户查询中识别意图

        Args:
            query: 用户查询

        Returns:
            List[str]: 识别出的意图列表
        """
        # 记录开始时间
        start_time = time.time()

        # 使用提示模板格式化查询
        prompt = INTENT_CLASSIFICATION_PROMPT_TEMPLATE.format(query=query)

        try:
            # 调用LLM进行意图分类
            messages = [HumanMessage(content=prompt)]
            response = self.llm.invoke(messages)
            response_text = response.content if hasattr(response, 'content') else str(response)

            # 从响应中提取JSON
            # 首先尝试匹配对象格式 {"intents": [...]}
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                try:
                    result = json.loads(json_match.group())
                    intents = result.get("intents", [])

                    # 记录耗时
                    classification_time = time.time() - start_time
                    logger.info(f"意图分类耗时: {classification_time:.2f}秒，识别出的意图: {intents}")

                    return intents
                except json.JSONDecodeError:
                    logger.warning(f"JSON对象解析失败: {json_match.group()}")

            # 如果对象格式匹配失败，尝试匹配数组格式 [...]
            array_match = re.search(r'\[.*\]', response_text, re.DOTALL)
            if array_match:
                try:
                    intents = json.loads(array_match.group())
                    if isinstance(intents, list):
                        # 记录耗时
                        classification_time = time.time() - start_time
                        logger.info(f"意图分类耗时: {classification_time:.2f}秒，识别出的意图(数组格式): {intents}")

                        return intents
                except json.JSONDecodeError:
                    logger.warning(f"JSON数组解析失败: {array_match.group()}")

            # 如果所有尝试都失败
            logger.warning(f"无法从响应中提取JSON: {response_text}")
            return ["chit_chat"]  # 默认返回闲聊意图

        except Exception as e:
            logger.error(f"意图分类失败: {e}")
            return ["chit_chat"]  # 出错时默认返回闲聊意图
