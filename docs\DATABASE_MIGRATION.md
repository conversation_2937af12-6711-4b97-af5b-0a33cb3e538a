# Database Migration Guide

This guide helps you understand and manage database schema changes in the RAG application.

## Problem Description

The application is failing with the error:
```
Unknown column 'users.role' in 'field list'
```

This occurs because the code has been updated to include a `role` column in the `users` table, but the database schema hasn't been migrated to reflect this change.

## Quick Fix (Recommended)

To immediately fix the issue and get your application running:

### Step 1: Backup Your Database (Recommended)
```bash
cd /path/to/your/rag/project
python scripts/backup_database.py
```

### Step 2: Apply the Migration
```bash
python scripts/quick_fix_user_role.py
```

This script will:
- Check if the `role` column already exists
- Add the `role` column with ENUM('USER', 'ADMIN') type
- Set default value to 'USER' for existing users
- Create an index on the role column
- Verify the migration was successful

### Step 3: Restart Your Application
After the migration completes successfully, restart your FastAPI application.

## Advanced Migration Management

For more comprehensive migration management, use the full migration tool:

### Check for Schema Differences
```bash
python scripts/database_migration.py --check
```

### Apply All Pending Migrations
```bash
python scripts/database_migration.py --migrate
```

### View Migration Status
```bash
python scripts/database_migration.py --status
```

### Rollback Last Migration
```bash
python scripts/database_migration.py --rollback
```

## Database Schema Changes

The following changes have been made to the database schema:

### Users Table
- **Added**: `role` column
  - Type: `ENUM('USER', 'ADMIN')`
  - Default: `'USER'`
  - Index: `ix_users_role`
  - Comment: New user role field (新增用户角色字段)

This change allows the application to distinguish between regular users and administrators.

## Prerequisites

Ensure you have the required dependencies installed:

```bash
pip install pymysql sqlalchemy
```

For backup/restore functionality, you also need MySQL client tools:
- **Windows**: Install MySQL Community Server or MySQL Workbench
- **macOS**: `brew install mysql-client`
- **Linux**: `sudo apt-get install mysql-client` or `sudo yum install mysql`

## Database Configuration

The application connects to MySQL using the configuration in `src/rag/config.py`:

```python
DATABASE_URL = get_env_var("DATABASE_URL", "mysql+pymysql://root:123456@localhost/rag_db_default")
```

You can override this by setting the `DATABASE_URL` environment variable in your `.env` file.

## Troubleshooting

### Migration Fails
1. Check database connection:
   ```bash
   mysql -h localhost -u root -p rag_db_default
   ```

2. Verify user permissions:
   ```sql
   SHOW GRANTS FOR 'root'@'localhost';
   ```

3. Check if column already exists:
   ```sql
   DESCRIBE users;
   ```

### Backup Fails
1. Ensure mysqldump is installed and in PATH
2. Check database credentials
3. Verify database exists and is accessible

### Application Still Fails After Migration
1. Restart the application completely
2. Check application logs for other errors
3. Verify the migration was applied:
   ```sql
   SELECT * FROM schema_migrations;
   DESCRIBE users;
   ```

## File Structure

```
scripts/
├── database_migration.py      # Full migration management tool
├── quick_fix_user_role.py    # Quick fix for role column issue
└── backup_database.py        # Database backup/restore tool

backups/                      # Created automatically
└── rag_db_backup_YYYYMMDD_HHMMSS.sql
```

## Safety Notes

1. **Always backup your database** before running migrations
2. **Test migrations on a development environment** first
3. **Review generated SQL** before applying to production
4. **Monitor application logs** after migration
5. **Have a rollback plan** ready

## Future Migrations

To add new migrations in the future:

1. Update SQLAlchemy models in `src/rag/database.py`
2. Run migration check: `python scripts/database_migration.py --check`
3. Review and apply: `python scripts/database_migration.py --migrate`

The migration system will automatically track applied migrations and prevent duplicate applications.

## Support

If you encounter issues:

1. Check the migration logs in `migration.log`
2. Review database error messages
3. Ensure all prerequisites are installed
4. Verify database connectivity and permissions

For additional help, refer to the SQLAlchemy and MySQL documentation.
