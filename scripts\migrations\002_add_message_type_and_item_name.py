"""
数据库迁移脚本：为消息表添加类型和物品名称字段
Migration: Add type and item_name fields to messages table

此脚本为现有的messages表添加type和item_name字段，支持合成导航功能。
"""

import sys
import os
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.rag.config import DATABASE_URL
from src.rag.database import MessageType

def run_migration():
    """执行数据库迁移"""
    print("开始执行数据库迁移：添加消息类型和物品名称字段...")

    # 创建数据库连接
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    with SessionLocal() as session:
        try:
            # 检查type字段是否已存在
            result = session.execute(text("""
                SELECT COUNT(*) as count
                FROM information_schema.columns
                WHERE table_name = 'messages'
                AND column_name = 'type'
            """))

            type_column_exists = result.fetchone()[0] > 0

            # 检查item_name字段是否已存在
            result = session.execute(text("""
                SELECT COUNT(*) as count
                FROM information_schema.columns
                WHERE table_name = 'messages'
                AND column_name = 'item_name'
            """))

            item_name_column_exists = result.fetchone()[0] > 0

            if type_column_exists and item_name_column_exists:
                print("type和item_name字段已存在，跳过迁移")
                return True

            # 添加type字段
            if not type_column_exists:
                print("添加type字段到messages表...")
                session.execute(text("""
                    ALTER TABLE messages
                    ADD COLUMN type ENUM('text', 'crafting')
                    NOT NULL DEFAULT 'text'
                """))

                # 为type字段添加索引
                print("为type字段添加索引...")
                session.execute(text("""
                    CREATE INDEX idx_messages_type ON messages(type)
                """))
            else:
                print("type字段已存在，跳过添加")

            # 添加item_name字段
            if not item_name_column_exists:
                print("添加item_name字段到messages表...")
                session.execute(text("""
                    ALTER TABLE messages
                    ADD COLUMN item_name VARCHAR(255) NULL
                """))

                # 为item_name字段添加索引
                print("为item_name字段添加索引...")
                session.execute(text("""
                    CREATE INDEX idx_messages_item_name ON messages(item_name)
                """))
            else:
                print("item_name字段已存在，跳过添加")

            # 提交更改
            session.commit()
            print("数据库迁移完成！")

            # 验证迁移结果
            print("验证迁移结果...")
            
            # 验证type字段
            result = session.execute(text("""
                SELECT COUNT(*) as count
                FROM information_schema.columns
                WHERE table_name = 'messages'
                AND column_name = 'type'
            """))

            if result.fetchone()[0] > 0:
                print("✓ type字段已成功添加")
            else:
                print("✗ type字段添加失败")
                return False

            # 验证item_name字段
            result = session.execute(text("""
                SELECT COUNT(*) as count
                FROM information_schema.columns
                WHERE table_name = 'messages'
                AND column_name = 'item_name'
            """))

            if result.fetchone()[0] > 0:
                print("✓ item_name字段已成功添加")
            else:
                print("✗ item_name字段添加失败")
                return False

            # 检查type字段索引是否创建成功
            result = session.execute(text("""
                SHOW INDEX FROM messages WHERE Key_name = 'idx_messages_type'
            """))

            if result.fetchone():
                print("✓ type字段索引已成功创建")
            else:
                print("✗ type字段索引创建失败")
                return False

            # 检查item_name字段索引是否创建成功
            result = session.execute(text("""
                SHOW INDEX FROM messages WHERE Key_name = 'idx_messages_item_name'
            """))

            if result.fetchone():
                print("✓ item_name字段索引已成功创建")
            else:
                print("✗ item_name字段索引创建失败")
                return False

            print("所有迁移步骤已成功完成！")
            return True

        except Exception as e:
            print(f"迁移过程中发生错误: {e}")
            session.rollback()
            return False

def rollback_migration():
    """回滚数据库迁移"""
    print("开始回滚数据库迁移：移除消息类型和物品名称字段...")

    # 创建数据库连接
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    with SessionLocal() as session:
        try:
            # 检查字段是否存在
            result = session.execute(text("""
                SELECT COUNT(*) as count
                FROM information_schema.columns
                WHERE table_name = 'messages'
                AND column_name IN ('type', 'item_name')
            """))

            columns_exist = result.fetchone()[0] > 0

            if not columns_exist:
                print("type和item_name字段不存在，无需回滚")
                return True

            # 删除索引
            print("删除字段索引...")
            try:
                session.execute(text("""
                    DROP INDEX idx_messages_type ON messages
                """))
            except Exception as e:
                print(f"删除type索引时出错（可能不存在）: {e}")

            try:
                session.execute(text("""
                    DROP INDEX idx_messages_item_name ON messages
                """))
            except Exception as e:
                print(f"删除item_name索引时出错（可能不存在）: {e}")

            # 删除字段
            print("删除type字段...")
            try:
                session.execute(text("""
                    ALTER TABLE messages DROP COLUMN type
                """))
            except Exception as e:
                print(f"删除type字段时出错: {e}")

            print("删除item_name字段...")
            try:
                session.execute(text("""
                    ALTER TABLE messages DROP COLUMN item_name
                """))
            except Exception as e:
                print(f"删除item_name字段时出错: {e}")

            # 提交更改
            session.commit()
            print("数据库迁移回滚完成！")
            return True

        except Exception as e:
            print(f"回滚过程中发生错误: {e}")
            session.rollback()
            return False

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="消息类型和物品名称字段数据库迁移脚本")
    parser.add_argument("--rollback", action="store_true", help="回滚迁移")

    args = parser.parse_args()

    if args.rollback:
        success = rollback_migration()
    else:
        success = run_migration()

    if success:
        print("操作成功完成！")
        sys.exit(0)
    else:
        print("操作失败！")
        sys.exit(1)
